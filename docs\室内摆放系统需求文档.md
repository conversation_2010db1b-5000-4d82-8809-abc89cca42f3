# DigDeeper 室内摆放系统需求文档

## 一、项目概述

### 1.1 项目背景
基于现有DigDeeper挖矿游戏，新增室内摆放系统作为局外玩法，旨在增强玩家留存，提供挖矿之外的成长体验，形成完整的游戏循环。

### 1.2 核心目标
- 建立挖矿-摆放-增益的闭环系统
- 提供中长期目标和短期反馈机制
- 增加游戏的个性化装扮元素

## 二、核心功能需求

### 2.1 高优先级功能

#### 2.1.1 房间网格系统
- **网格规格**：可灵活配置的网格系统（支持5×4、6×5等多种规格）
- **地面摆放**：家具占位支持1×1、2×1、2×2等多种规格
- **墙面装饰**：墙上可放置墙饰和挂饰，不占用地面网格
- **碰撞检测**：确保家具不重叠放置
- **边界限制**：家具不能超出房间边界

#### 2.1.2 家具摆放核心功能
- **点击选择**：点击家具分类中的家具，在地图中间显示对应家具
- **拖拽放置**：将显示的家具拖拽到目标位置
- **旋转功能**：支持90度旋转
- **家具操作**：点击已放置家具弹出操作菜单（删除/旋转等）

#### 2.1.3 装扮评分系统
- **评分算法**：基础分 + 相邻加成 + 主题匹配度
- **实时计算**：摆放后立即更新评分
- **档位奖励**：不同评分档位提供不同奖励

#### 2.1.4 家具获取系统
- **挖矿掉落**：挖矿时掉落家具碎片
- **碎片合成**：集齐N片自动合成家具
- **商店购买**：金币购买基础家具

### 2.2 中优先级功能

#### 2.2.1 主题房型系统
- **多主题支持**：木屋、冰洞、魔法屋
- **解锁条件**：基于挖矿关卡进度解锁
- **专属家具**：每个主题有专属家具池

#### 2.2.2 家具升级系统
- **升级材料**：消耗矿石+稀有材料
- **属性提升**：升级后评分和装饰效果提升
- **皮肤解锁**：累计升级解锁配色皮肤

#### 2.2.3 增益券系统
- **券类型**：矿石加成券（+10-30%产出）
- **获取方式**：装扮评分档位奖励
- **使用机制**：作为挖矿道具，占用局内挖矿时的道具格子

### 2.3 低优先级功能

#### 2.3.1 成就系统
- **摆放成就**：完成N件家具摆满
- **主题成就**：全解锁房型
- **特殊成就**：黄金房间、对称摆放等

## 三、用户界面设计

### 3.1 界面布局
- **入口位置**：游戏主界面下方第二个位置
- **家具分类区**：
  - 默认状态：收起状态，仅显示分类标签栏
  - 展开状态：UI下半部分显示完整家具分类内容（座椅、灯饰、地毯、装饰品、墙饰）
  - 切换方式：点击分类标签或上拉手势展开，点击空白区域或下拉手势收起
- **地图显示区**：UI上半部分显示房间俯视图，分类区收起时占用更多空间
- **家具预览**：点击分类中的家具，在地图中间显示该家具

### 3.2 交互体验
- **分类区交互**：
  - 收起状态：显示分类图标，节省屏幕空间
  - 展开状态：显示完整家具列表，便于选择
  - 平滑动画：收起/展开过程有流畅的动画效果
- **家具选择**：点击分类→展开家具列表→选择家具→地图中间显示→拖拽到目标位置
- **自动收起**：选择家具后可选择自动收起分类区，专注于摆放操作

### 3.3 动画效果规范

#### 3.3.1 分类区收起/展开动画
- **展开动画**：
  - 时长：300ms
  - 缓动函数：ease-out
  - 效果：从底部向上滑出，透明度从0到1
  - 背景：半透明遮罩渐现

- **收起动画**：
  - 时长：250ms
  - 缓动函数：ease-in
  - 效果：向下滑入，透明度从1到0
  - 背景：半透明遮罩渐隐

#### 3.3.2 家具操作动画
- **家具选择**：
  - 点击反馈：轻微缩放效果（0.95→1.0，100ms）
  - 选中状态：边框高亮，轻微发光效果

- **家具拖拽**：
  - 拖拽开始：家具轻微放大（1.0→1.1）
  - 拖拽过程：跟随手指移动，带阴影效果
  - 放置成功：缩放回原大小，绿色闪烁提示
  - 放置失败：红色闪烁，弹回原位置

#### 3.3.3 评分更新动画
- **评分变化**：
  - 数字跳动效果，从旧分数到新分数
  - 时长：500ms，配合音效
  - 颜色：增加为绿色，减少为红色

## 四、新手引导系统

### 4.1 首次进入引导
1. **欢迎提示**：
   - 弹窗介绍室内摆放系统功能
   - 突出显示"开始装扮"按钮

2. **基础操作引导**：
   - 步骤1：高亮分类区，提示"点击展开家具分类"
   - 步骤2：引导选择第一件家具
   - 步骤3：演示拖拽摆放操作
   - 步骤4：展示评分变化效果

3. **进阶功能引导**：
   - 家具旋转操作演示
   - 墙面装饰摆放演示
   - 删除家具操作演示

### 4.2 操作提示系统
- **悬浮提示**：
  - 长按家具显示详细信息
  - 拖拽时显示可放置区域高亮
  - 不可放置时显示红色禁止图标

- **状态提示**：
  - 空房间时显示"点击下方分类开始装扮"
  - 评分达到新档位时显示庆祝动画
  - 解锁新家具时显示"NEW"标识

### 4.3 帮助系统
- **操作说明**：
  - 设置中的操作指南页面
  - 包含所有手势和操作的图文说明

- **快捷提示**：
  - 右上角"?"按钮，随时查看操作提示
  - 关键操作区域的小图标提示

## 五、技术架构设计

### 5.1 数据结构设计
```typescript
// 房间数据结构
interface RoomData {
  id: string;
  theme: RoomTheme;
  gridConfig: GridConfig; // 可配置网格
  floorFurniture: PlacedFurniture[]; // 地面家具
  wallDecorations: WallDecoration[]; // 墙面装饰
  decorationScore: number;
}

// 网格配置
interface GridConfig {
  width: number;
  height: number;
  cellSize: number;
}

// 墙面装饰
interface WallDecoration {
  id: string;
  furnitureId: string;
  wallPosition: WallPosition; // 墙面位置
  position: {x: number, y: number}; // 墙面坐标
}
```

### 5.2 系统集成
- **与挖矿系统集成**：
  - 碎片掉落机制
  - 增益券作为挖矿道具使用
  - 关卡进度解锁房型
- **与货币系统集成**：金币、钻石、矿石消耗
- **与成就系统集成**：装扮相关成就

### 5.3 性能要求
- **内存占用**：单个房间场景<50MB
- **加载时间**：房间切换<2秒
- **帧率要求**：保持60fps稳定运行

## 六、数据平衡设计

### 6.1 经济平衡
- **家具价格**：基础家具100-1000金币
- **升级成本**：递增式成本设计
- **产出比例**：挖矿碎片掉落率5-15%

### 6.2 评分平衡
- **基础评分**：每件家具10-100分
- **相邻加成**：同类家具+20%
- **主题匹配**：匹配主题+50%
- **墙饰加成**：墙面装饰提供额外评分

## 七、开发里程碑

### 阶段一（核心功能）
- 可配置网格系统和基础摆放
- 墙面装饰系统
- 简单评分算法
- 基础UI界面和交互

### 阶段二（系统完善）
- 家具商店和升级
- 主题房型系统
- 与挖矿系统集成（增益券道具化）

### 阶段三（功能扩展）
- 成就系统
- 数据优化和性能调优
- 平衡性调整

## 八、风险评估

### 8.1 技术风险
- **性能风险**：大量家具和墙饰渲染可能影响性能
- **内存风险**：多主题资源占用内存过大
- **UI复杂度**：墙面和地面双重摆放系统的交互复杂度

### 8.2 设计风险
- **平衡风险**：经济系统和增益券平衡需要大量测试
- **用户体验风险**：操作复杂度可能影响用户接受度

---

**文档版本**：v1.0  
**创建日期**：2025-06-28  
**最后更新**：2025-06-28
