[{"__type__": "cc.Prefab", "_name": "checkInUI", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "checkInUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 18}, {"__id__": 42}, {"__id__": 54}, {"__id__": 76}, {"__id__": 91}, {"__id__": 108}, {"__id__": 125}, {"__id__": 151}, {"__id__": 177}, {"__id__": 203}, {"__id__": 229}, {"__id__": 255}, {"__id__": 281}, {"__id__": 347}], "_active": true, "_components": [{"__id__": 362}, {"__id__": 364}, {"__id__": 366}, {"__id__": 368}], "_prefab": {"__id__": 370}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "91c41d69-a9e5-45d4-bfe1-486bf9823c9c", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ecX7n9w+hM2bduzY5Wm5it", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "bg"}, {"__type__": "cc.TargetInfo", "localID": ["c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "签到页面-底板", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 4, "y": 76, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 961, "height": 1529}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8db5/G5KBPSoKyGkAvSJgz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5f27afe6-7bf3-472d-83c4-4c665c4f3555@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41hfn8xPZPZqkQ5ParnU2i"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 16}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abYN6f3YBNZLLv+z/9UGzL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99zhvZz7lKtaqzfIAPNiWq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "矿洞进度条_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}, {"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": -353, "y": 571, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "矿洞进度条_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 22}, "_contentSize": {"__type__": "cc.Size", "width": 716, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86dhMC1ddCBI7ho2JDGPzH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 24}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "421feb1b-f6bd-4ceb-a918-1fbc1c9aabd0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aEvHIAmZJt4601990cAAz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3nVObh41JKpo/Z1Ukb/Jj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 716, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ca8F0mbmJLgY/hCLDt1bAy"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 29}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ab4zKZuexI26SZhWrAMXNI"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6eSztpA5K6aVu6+oNNbr7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": false, "__prefab": {"__id__": 33}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f91y9CVGBFZ6ZIKrS4VW6Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55T064oahFDpCBd6gHOE0t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 716, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cAfo5JKVNpZ1lCX1hbqAR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8a3ad432-903a-4846-af8a-9d078a213511@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14wi9KTe5IzpyG22cb2ixF"}, {"__type__": "be29aRaSvJDiY8MyQ801dFR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 40}, "max": 100, "title": "", "bar": {"__id__": 26}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9SPNCqmNFTYoIxIxAVgXk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddgAhLT+5MnIC0hqKGdnJd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "签到页面-天数标", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 43}], "_active": true, "_components": [{"__id__": 49}, {"__id__": 51}], "_prefab": {"__id__": 53}, "_lpos": {"__type__": "cc.Vec3", "x": -353, "y": 581, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 35.8076171875, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64zzX7/QBNTJzRGMG4+88h"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 130, "g": 89, "b": 89, "a": 255}, "_string": "2", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 50, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e735JDq0NCmbH35zalsh6x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cFgebvA5Cmb4UyUthBBQw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 50}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 123}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16TBsuqkhGeZ5kFth0NzKu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": {"__id__": 52}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3391f3db-aced-4781-9ad7-f206c2f173fa@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17ZQHpm8NJcoPeOJpt/plH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93bBaOiI9HV4D2wESKX/Ii", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 55}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 54}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 56}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "727t2lS2FMm55VCc+lZC6A", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 57}], "propertyOverrides": [{"__id__": 64}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 71}, {"__id__": 73}, {"__id__": 74}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 58}, "components": [{"__id__": 59}, {"__id__": 61}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "150bdPVRmRKxb/GBeTKkd+z", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 54}}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 60}, "bg": {"__id__": 10}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11UqaeFjpI06J/KTrInI6l"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 54}}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 62}, "clickEvents": [{"__id__": 63}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76aTzj3U5O5bhwTyJNDJ48"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 54}, "component": "", "_componentId": "150bdPVRmRKxb/GBeTKkd+z", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_name"], "value": "宝箱-001"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -183, "y": 585, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 77}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 76}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 78}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "68MhWU5TVHoYTKJNxM/ORv", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 79}], "propertyOverrides": [{"__id__": 86}, {"__id__": 88}, {"__id__": 89}, {"__id__": 90}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 80}, "components": [{"__id__": 81}, {"__id__": 83}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "150bdPVRmRKxb/GBeTKkd+z", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 76}}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 82}, "bg": {"__id__": 10}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82HwQeniRMhZ8Xqq2UP8QM"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 76}}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 84}, "clickEvents": [{"__id__": 85}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44sNEYsRlAdrDk1dzjSedW"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 76}, "component": "", "_componentId": "150bdPVRmRKxb/GBeTKkd+z", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_name"], "value": "宝箱"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 585, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 92}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 91}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 93}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0dDKTX8FVN+rvT0YkLUk0M", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 94}], "propertyOverrides": [{"__id__": 101}, {"__id__": 103}, {"__id__": 104}, {"__id__": 105}, {"__id__": 106}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 95}, "components": [{"__id__": 96}, {"__id__": 98}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "150bdPVRmRKxb/GBeTKkd+z", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 91}}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 97}, "bg": {"__id__": 10}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11AfW8+9JCV47PxiXCGEAp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 91}}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 99}, "clickEvents": [{"__id__": 100}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ap48NA+JHCoyly2hk5Yda"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 91}, "component": "", "_componentId": "150bdPVRmRKxb/GBeTKkd+z", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_name"], "value": "宝箱-002"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 184, "y": 585, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 109}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 108}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 110}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "104w4StqpP7Zg8/DDrXwSK", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 111}], "propertyOverrides": [{"__id__": 118}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 123}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 112}, "components": [{"__id__": 113}, {"__id__": 115}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "150bdPVRmRKxb/GBeTKkd+z", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 108}}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 114}, "bg": {"__id__": 10}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bP8CEv/dC+LrCMLVIN0hn"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 108}}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 116}, "clickEvents": [{"__id__": 117}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5aNAUJx5HP532gdvo5Bz8"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 108}, "component": "", "_componentId": "150bdPVRmRKxb/GBeTKkd+z", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_name"], "value": "宝箱-003"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 367, "y": 585, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 124}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 126}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 125}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 127}, "targetOverrides": [{"__id__": 133}, {"__id__": 137}, {"__id__": 140}, {"__id__": 143}, {"__id__": 146}, {"__id__": 149}]}, {"__type__": "cc.PrefabInstance", "fileId": "7d9RcBHwdGELXQapmgw+EZ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 128}, {"__id__": 130}, {"__id__": 131}, {"__id__": 132}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_name"], "value": "checkInNormalBoard"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -290, "y": 313, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 134}, "sourceInfo": {"__id__": 135}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 134}, "targetInfo": {"__id__": 136}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 134}, "sourceInfo": {"__id__": 138}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 134}, "targetInfo": {"__id__": 139}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 134}, "sourceInfo": {"__id__": 141}, "propertyPath": ["iconBgRed"], "target": {"__id__": 134}, "targetInfo": {"__id__": 142}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 134}, "sourceInfo": {"__id__": 144}, "propertyPath": ["reward"], "target": {"__id__": 134}, "targetInfo": {"__id__": 145}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 134}, "sourceInfo": {"__id__": 147}, "propertyPath": ["fragment"], "target": {"__id__": 134}, "targetInfo": {"__id__": 148}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 134}, "targetInfo": {"__id__": 150}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 152}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 151}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 153}, "targetOverrides": [{"__id__": 159}, {"__id__": 163}, {"__id__": 166}, {"__id__": 169}, {"__id__": 172}, {"__id__": 175}]}, {"__type__": "cc.PrefabInstance", "fileId": "75cCCR4ANAB5s54JQNVrya", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 154}, {"__id__": 156}, {"__id__": 157}, {"__id__": 158}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_name"], "value": "checkInNormalBoard"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 3, "y": 313, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 160}, "sourceInfo": {"__id__": 161}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 160}, "targetInfo": {"__id__": 162}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 160}, "sourceInfo": {"__id__": 164}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 160}, "targetInfo": {"__id__": 165}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 160}, "sourceInfo": {"__id__": 167}, "propertyPath": ["iconBgRed"], "target": {"__id__": 160}, "targetInfo": {"__id__": 168}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 160}, "sourceInfo": {"__id__": 170}, "propertyPath": ["reward"], "target": {"__id__": 160}, "targetInfo": {"__id__": 171}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 160}, "sourceInfo": {"__id__": 173}, "propertyPath": ["fragment"], "target": {"__id__": 160}, "targetInfo": {"__id__": 174}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 160}, "targetInfo": {"__id__": 176}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 178}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 177}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 179}, "targetOverrides": [{"__id__": 185}, {"__id__": 189}, {"__id__": 192}, {"__id__": 195}, {"__id__": 198}, {"__id__": 201}]}, {"__type__": "cc.PrefabInstance", "fileId": "d3tjNN1Z5NmIYPJGvlIbEy", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 180}, {"__id__": 182}, {"__id__": 183}, {"__id__": 184}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_name"], "value": "checkInNormalBoard"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 293, "y": 313, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 187}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 186}, "targetInfo": {"__id__": 188}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 190}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 186}, "targetInfo": {"__id__": 191}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 193}, "propertyPath": ["iconBgRed"], "target": {"__id__": 186}, "targetInfo": {"__id__": 194}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 196}, "propertyPath": ["reward"], "target": {"__id__": 186}, "targetInfo": {"__id__": 197}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 186}, "sourceInfo": {"__id__": 199}, "propertyPath": ["fragment"], "target": {"__id__": 186}, "targetInfo": {"__id__": 200}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 186}, "targetInfo": {"__id__": 202}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 204}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 203}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 205}, "targetOverrides": [{"__id__": 211}, {"__id__": 215}, {"__id__": 218}, {"__id__": 221}, {"__id__": 224}, {"__id__": 227}]}, {"__type__": "cc.PrefabInstance", "fileId": "62EvSEv1JGAZahBbTc/yP6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 206}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_name"], "value": "checkInNormalBoard-001"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -290, "y": -14, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 207}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 212}, "sourceInfo": {"__id__": 213}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 212}, "targetInfo": {"__id__": 214}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 212}, "sourceInfo": {"__id__": 216}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 212}, "targetInfo": {"__id__": 217}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 212}, "sourceInfo": {"__id__": 219}, "propertyPath": ["iconBgRed"], "target": {"__id__": 212}, "targetInfo": {"__id__": 220}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 212}, "sourceInfo": {"__id__": 222}, "propertyPath": ["reward"], "target": {"__id__": 212}, "targetInfo": {"__id__": 223}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 212}, "sourceInfo": {"__id__": 225}, "propertyPath": ["fragment"], "target": {"__id__": 212}, "targetInfo": {"__id__": 226}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 212}, "targetInfo": {"__id__": 228}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 230}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 231}, "targetOverrides": [{"__id__": 237}, {"__id__": 241}, {"__id__": 244}, {"__id__": 247}, {"__id__": 250}, {"__id__": 253}]}, {"__type__": "cc.PrefabInstance", "fileId": "7czPDy29JMB51K8PU6UeAJ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 232}, {"__id__": 234}, {"__id__": 235}, {"__id__": 236}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_name"], "value": "checkInNormalBoard-002"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 3, "y": -14, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 238}, "sourceInfo": {"__id__": 239}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 238}, "targetInfo": {"__id__": 240}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 238}, "sourceInfo": {"__id__": 242}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 238}, "targetInfo": {"__id__": 243}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 238}, "sourceInfo": {"__id__": 245}, "propertyPath": ["iconBgRed"], "target": {"__id__": 238}, "targetInfo": {"__id__": 246}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 238}, "sourceInfo": {"__id__": 248}, "propertyPath": ["reward"], "target": {"__id__": 238}, "targetInfo": {"__id__": 249}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 238}, "sourceInfo": {"__id__": 251}, "propertyPath": ["fragment"], "target": {"__id__": 238}, "targetInfo": {"__id__": 252}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 238}, "targetInfo": {"__id__": 254}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 256}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 255}, "asset": {"__uuid__": "d820ec4a-e7a9-4e2f-b04c-e645a2c071f5", "__expectedType__": "cc.Prefab"}, "fileId": "63gc53/k5CIqLHOUlYZlCb", "instance": {"__id__": 257}, "targetOverrides": [{"__id__": 263}, {"__id__": 267}, {"__id__": 270}, {"__id__": 273}, {"__id__": 276}, {"__id__": 279}]}, {"__type__": "cc.PrefabInstance", "fileId": "7aiVktu0VEGpkMcLYJwtqX", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 258}, {"__id__": 260}, {"__id__": 261}, {"__id__": 262}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_name"], "value": "checkInNormalBoard-003"}, {"__type__": "cc.TargetInfo", "localID": ["63gc53/k5CIqLHOUlYZlCb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 293, "y": -14, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 264}, "sourceInfo": {"__id__": 265}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 264}, "targetInfo": {"__id__": 266}}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 264}, "sourceInfo": {"__id__": 268}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 264}, "targetInfo": {"__id__": 269}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 264}, "sourceInfo": {"__id__": 271}, "propertyPath": ["iconBgRed"], "target": {"__id__": 264}, "targetInfo": {"__id__": 272}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 264}, "sourceInfo": {"__id__": 274}, "propertyPath": ["reward"], "target": {"__id__": 264}, "targetInfo": {"__id__": 275}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 264}, "sourceInfo": {"__id__": 277}, "propertyPath": ["fragment"], "target": {"__id__": 264}, "targetInfo": {"__id__": 278}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 264}, "targetInfo": {"__id__": 280}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.Node", "_name": "checkInNormalBoard-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 282}, {"__id__": 288}, {"__id__": 294}, {"__id__": 302}, {"__id__": 310}, {"__id__": 318}], "_active": true, "_components": [{"__id__": 340}, {"__id__": 342}, {"__id__": 344}], "_prefab": {"__id__": 346}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -339, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "提醒标志", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 281}, "_children": [], "_active": false, "_components": [{"__id__": 283}, {"__id__": 285}], "_prefab": {"__id__": 287}, "_lpos": {"__type__": "cc.Vec3", "x": 106, "y": 138, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 284}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eHWF0JpFL8qoBuOmqJsg8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 286}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bMyV3tkhDpovBubrj9Jlq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0b9vifnlASILw8SUOdNnN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 281}, "_children": [], "_active": true, "_components": [{"__id__": 289}, {"__id__": 291}], "_prefab": {"__id__": 293}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 95, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79n8kFNOVDRYHKOmUOE2dJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 292}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "第二天", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 176, "g": 94, "b": 27, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77aJwKqPhGxo1BcEaupbt9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "406XWPMRZFC78WksKJ7f/q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 281}, "_prefab": {"__id__": 295}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 294}, "asset": {"__uuid__": "a2d3c2b8-2bb6-4213-8160-32c64989f30f", "__expectedType__": "cc.Prefab"}, "fileId": "9af+T5dPpCWaFV17snggbO", "instance": {"__id__": 296}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "00c19k3OhMS5PcaULTZixV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 297}, {"__id__": 299}, {"__id__": 300}, {"__id__": 301}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_name"], "value": "checkInNoardItem"}, {"__type__": "cc.TargetInfo", "localID": ["9af+T5dPpCWaFV17snggbO"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -173, "y": -48, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 281}, "_prefab": {"__id__": 303}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 302}, "asset": {"__uuid__": "a2d3c2b8-2bb6-4213-8160-32c64989f30f", "__expectedType__": "cc.Prefab"}, "fileId": "9af+T5dPpCWaFV17snggbO", "instance": {"__id__": 304}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "1a4OL7It5AmaPLaT7Fp+jM", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 305}, {"__id__": 307}, {"__id__": 308}, {"__id__": 309}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_name"], "value": "checkInNoardItem-001"}, {"__type__": "cc.TargetInfo", "localID": ["9af+T5dPpCWaFV17snggbO"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -48, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 306}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 281}, "_prefab": {"__id__": 311}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 310}, "asset": {"__uuid__": "a2d3c2b8-2bb6-4213-8160-32c64989f30f", "__expectedType__": "cc.Prefab"}, "fileId": "9af+T5dPpCWaFV17snggbO", "instance": {"__id__": 312}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "76nF2JpfdEJ67M/R+gB/sP", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 313}, {"__id__": 315}, {"__id__": 316}, {"__id__": 317}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_name"], "value": "checkInNoardItem-002"}, {"__type__": "cc.TargetInfo", "localID": ["9af+T5dPpCWaFV17snggbO"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 174, "y": -48, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 281}, "_children": [{"__id__": 319}, {"__id__": 327}], "_active": false, "_components": [{"__id__": 333}, {"__id__": 335}, {"__id__": 337}], "_prefab": {"__id__": 339}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "blackBlock", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 318}, "_children": [], "_active": true, "_components": [{"__id__": 320}, {"__id__": 322}, {"__id__": 324}], "_prefab": {"__id__": 326}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 321}, "_contentSize": {"__type__": "cc.Size", "width": 617, "height": 323}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97Ogsk7eZHaqOH/P6lxPdy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 323}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 77}, "_spriteFrame": {"__uuid__": "f2485ac9-598f-4c0f-bb90-1840599ba276@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bphTncyBNoK6zrQzsTRZJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 325}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 4, "_originalHeight": 4, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eAi6BajJDtahOCBSPFTzG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81YilXa1BDo5mEPxGq4dlI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "pic_castle_gou", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 318}, "_children": [], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 329}, "_contentSize": {"__type__": "cc.Size", "width": 88.2, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62wuzK8RJAj45wAzIBKpPN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 331}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "320785c7-1468-40c1-b4e6-34d9808d7b4d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "827cXbjKBJdIheUibr9Eqb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9acMB20sRCL5a3GHG1gDq5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 617, "height": 323}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87KPdsvJFErL4Ru+xyDKRE"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 336}, "_type": 3, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ed7gG7jGlHQ5rC+xbIbcS5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 338}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0ce0497f-656f-42b7-a509-119593fd6468@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43EjCisGNKE7hkw1TfjKmX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d911CpIWNIKZTNqmvFUqbG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 341}, "_contentSize": {"__type__": "cc.Size", "width": 617, "height": 323}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aFjGr0NNEJbSbvUzO2AQQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 343}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0ce0497f-656f-42b7-a509-119593fd6468@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "147RqbPCZG9Kj5NLbo6+SR"}, {"__type__": "bae14OPgV9GJrN1aYBJRoNc", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 281}, "_enabled": true, "__prefab": {"__id__": 345}, "bgOrange": null, "reddot": {"__id__": 282}, "title": {"__id__": 291}, "rewards": [null, null, null], "mask": {"__id__": 318}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfl4G136dLY4BXI5N69d5Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13XMTuMjNE7qhH+/6RWKYE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 348}], "_active": true, "_components": [{"__id__": 354}, {"__id__": 356}, {"__id__": 358}], "_prefab": {"__id__": 361}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -580, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 347}, "_children": [], "_active": true, "_components": [{"__id__": 349}, {"__id__": 351}], "_prefab": {"__id__": 353}, "_lpos": {"__type__": "cc.Vec3", "x": 2, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 350}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 58.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f76IA4PoRMjogu9y30InyP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 348}, "_enabled": true, "__prefab": {"__id__": 352}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "签到", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 176, "g": 94, "b": 27, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72baVC0yhGL7i2LJ7i6jQE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0ccwRJX1hB1Kp+UXxe5vs2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 313.2, "height": 127.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33g5p71e1B1LooVun6+rre"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 357}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e33ae026-c812-4040-bbc6-f1a08478c621@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cgMLRj3dD8rsR5C+8+o3+"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 359}, "clickEvents": [{"__id__": 360}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2OCf0D9FMcp2C92svpJv9"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "59462vfxypAQ5yg/sxWNww5", "handler": "onCheckIn", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2Yk5IajxALbdk081FRzra", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 363}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7QbrTr2dEgpiwTstCzhWc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 365}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef6nj3bIFPo5cyUkQLFPqp"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 367}, "playOnLoad": false, "_clips": [{"__uuid__": "2d25ce1e-d00d-48e7-be88-7e394a59ebf5", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "c05b09cb-cdb5-49f8-b5b0-9c41471b2996", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeNtEsFGhJlZzz2xpM+YQC"}, {"__type__": "59462vfxypAQ5yg/sxWNww5", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 369}, "progressBar": {"__id__": 39}, "day": {"__id__": 46}, "boxes": [{"__id__": 59}, {"__id__": 81}, {"__id__": 96}, {"__id__": 113}], "normalBoards": [null, null, null, null, null, null, {"__id__": 344}], "btn": {"__id__": 347}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "917r45kPpMXZko9U6aFZXw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 371}, {"__id__": 374}, {"__id__": 377}, {"__id__": 380}, {"__id__": 383}, {"__id__": 386}, {"__id__": 389}, {"__id__": 392}, {"__id__": 395}, {"__id__": 398}, {"__id__": 401}, {"__id__": 404}, {"__id__": 407}, {"__id__": 410}, {"__id__": 413}, {"__id__": 416}, {"__id__": 419}, {"__id__": 422}, {"__id__": 425}, {"__id__": 428}, {"__id__": 431}, {"__id__": 434}, {"__id__": 437}, {"__id__": 440}, {"__id__": 443}, {"__id__": 446}, {"__id__": 449}, {"__id__": 452}, {"__id__": 455}, {"__id__": 458}, {"__id__": 461}, {"__id__": 464}, {"__id__": 467}, {"__id__": 470}, {"__id__": 473}, {"__id__": 476}, {"__id__": 479}, {"__id__": 482}, {"__id__": 485}, {"__id__": 488}, {"__id__": 491}, {"__id__": 494}, {"__id__": 497}, {"__id__": 500}, {"__id__": 503}, {"__id__": 506}, {"__id__": 509}, {"__id__": 512}, {"__id__": 515}, {"__id__": 518}, {"__id__": 521}, {"__id__": 524}, {"__id__": 527}, {"__id__": 530}, {"__id__": 533}, {"__id__": 536}, {"__id__": 539}, {"__id__": 541}, {"__id__": 543}, {"__id__": 545}, {"__id__": 548}, {"__id__": 551}, {"__id__": 554}, {"__id__": 557}, {"__id__": 560}, {"__id__": 562}, {"__id__": 564}, {"__id__": 566}, {"__id__": 568}, {"__id__": 570}, {"__id__": 572}, {"__id__": 575}, {"__id__": 578}, {"__id__": 581}, {"__id__": 584}, {"__id__": 587}, {"__id__": 590}, {"__id__": 592}, {"__id__": 594}, {"__id__": 596}, {"__id__": 598}, {"__id__": 601}, {"__id__": 604}, {"__id__": 606}, {"__id__": 608}, {"__id__": 610}], "nestedPrefabInstanceRoots": [{"__id__": 310}, {"__id__": 302}, {"__id__": 294}, {"__id__": 255}, {"__id__": 229}, {"__id__": 203}, {"__id__": 177}, {"__id__": 151}, {"__id__": 125}, {"__id__": 108}, {"__id__": 91}, {"__id__": 76}, {"__id__": 54}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 372}, "propertyPath": ["close"], "target": {"__id__": 54}, "targetInfo": {"__id__": 373}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 375}, "propertyPath": ["open"], "target": {"__id__": 54}, "targetInfo": {"__id__": 376}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 378}, "propertyPath": ["receive"], "target": {"__id__": 54}, "targetInfo": {"__id__": 379}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 381}, "propertyPath": ["reddot"], "target": {"__id__": 54}, "targetInfo": {"__id__": 382}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 76}, "sourceInfo": {"__id__": 384}, "propertyPath": ["close"], "target": {"__id__": 76}, "targetInfo": {"__id__": 385}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 76}, "sourceInfo": {"__id__": 387}, "propertyPath": ["open"], "target": {"__id__": 76}, "targetInfo": {"__id__": 388}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 76}, "sourceInfo": {"__id__": 390}, "propertyPath": ["receive"], "target": {"__id__": 76}, "targetInfo": {"__id__": 391}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 76}, "sourceInfo": {"__id__": 393}, "propertyPath": ["reddot"], "target": {"__id__": 76}, "targetInfo": {"__id__": 394}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 396}, "propertyPath": ["bgOrange"], "target": {"__id__": 151}, "targetInfo": {"__id__": 397}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 399}, "propertyPath": ["reddot"], "target": {"__id__": 151}, "targetInfo": {"__id__": 400}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 402}, "propertyPath": ["title"], "target": {"__id__": 151}, "targetInfo": {"__id__": 403}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 405}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 151}, "targetInfo": {"__id__": 406}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 408}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 151}, "targetInfo": {"__id__": 409}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 411}, "propertyPath": ["icon"], "target": {"__id__": 151}, "targetInfo": {"__id__": 412}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 414}, "propertyPath": ["amount"], "target": {"__id__": 151}, "targetInfo": {"__id__": 415}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 151}, "sourceInfo": {"__id__": 417}, "propertyPath": ["mask"], "target": {"__id__": 151}, "targetInfo": {"__id__": 418}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 420}, "propertyPath": ["bgOrange"], "target": {"__id__": 125}, "targetInfo": {"__id__": 421}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 423}, "propertyPath": ["reddot"], "target": {"__id__": 125}, "targetInfo": {"__id__": 424}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 426}, "propertyPath": ["title"], "target": {"__id__": 125}, "targetInfo": {"__id__": 427}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 429}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 125}, "targetInfo": {"__id__": 430}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 432}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 125}, "targetInfo": {"__id__": 433}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 435}, "propertyPath": ["icon"], "target": {"__id__": 125}, "targetInfo": {"__id__": 436}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 438}, "propertyPath": ["amount"], "target": {"__id__": 125}, "targetInfo": {"__id__": 439}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 125}, "sourceInfo": {"__id__": 441}, "propertyPath": ["mask"], "target": {"__id__": 125}, "targetInfo": {"__id__": 442}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 444}, "propertyPath": ["bgOrange"], "target": {"__id__": 177}, "targetInfo": {"__id__": 445}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 447}, "propertyPath": ["reddot"], "target": {"__id__": 177}, "targetInfo": {"__id__": 448}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 450}, "propertyPath": ["title"], "target": {"__id__": 177}, "targetInfo": {"__id__": 451}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 453}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 177}, "targetInfo": {"__id__": 454}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 456}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 177}, "targetInfo": {"__id__": 457}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 459}, "propertyPath": ["icon"], "target": {"__id__": 177}, "targetInfo": {"__id__": 460}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 462}, "propertyPath": ["amount"], "target": {"__id__": 177}, "targetInfo": {"__id__": 463}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 177}, "sourceInfo": {"__id__": 465}, "propertyPath": ["mask"], "target": {"__id__": 177}, "targetInfo": {"__id__": 466}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 468}, "propertyPath": ["bgOrange"], "target": {"__id__": 255}, "targetInfo": {"__id__": 469}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 471}, "propertyPath": ["reddot"], "target": {"__id__": 255}, "targetInfo": {"__id__": 472}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 474}, "propertyPath": ["title"], "target": {"__id__": 255}, "targetInfo": {"__id__": 475}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 477}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 255}, "targetInfo": {"__id__": 478}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 480}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 255}, "targetInfo": {"__id__": 481}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 483}, "propertyPath": ["icon"], "target": {"__id__": 255}, "targetInfo": {"__id__": 484}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 486}, "propertyPath": ["amount"], "target": {"__id__": 255}, "targetInfo": {"__id__": 487}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 255}, "sourceInfo": {"__id__": 489}, "propertyPath": ["mask"], "target": {"__id__": 255}, "targetInfo": {"__id__": 490}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 492}, "propertyPath": ["bgOrange"], "target": {"__id__": 203}, "targetInfo": {"__id__": 493}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 495}, "propertyPath": ["reddot"], "target": {"__id__": 203}, "targetInfo": {"__id__": 496}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 498}, "propertyPath": ["title"], "target": {"__id__": 203}, "targetInfo": {"__id__": 499}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 501}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 203}, "targetInfo": {"__id__": 502}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 504}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 203}, "targetInfo": {"__id__": 505}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 507}, "propertyPath": ["icon"], "target": {"__id__": 203}, "targetInfo": {"__id__": 508}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 510}, "propertyPath": ["amount"], "target": {"__id__": 203}, "targetInfo": {"__id__": 511}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": {"__id__": 513}, "propertyPath": ["mask"], "target": {"__id__": 203}, "targetInfo": {"__id__": 514}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 516}, "propertyPath": ["bgOrange"], "target": {"__id__": 229}, "targetInfo": {"__id__": 517}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["10Dz500qxAqYWc2fLCcpPL"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 519}, "propertyPath": ["reddot"], "target": {"__id__": 229}, "targetInfo": {"__id__": 520}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["07iezxX3ZGa6CjU/4Cv+7b"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 522}, "propertyPath": ["title"], "target": {"__id__": 229}, "targetInfo": {"__id__": 523}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["eaNOwYuvRLmoQ4oEHwi43R"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 525}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 229}, "targetInfo": {"__id__": 526}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 528}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 229}, "targetInfo": {"__id__": 529}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 531}, "propertyPath": ["icon"], "target": {"__id__": 229}, "targetInfo": {"__id__": 532}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["1cm4srXHhLu5JWkHqbaubd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 534}, "propertyPath": ["amount"], "target": {"__id__": 229}, "targetInfo": {"__id__": 535}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["0fSIFySO9Eh5KHOCZ7WNBy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 229}, "sourceInfo": {"__id__": 537}, "propertyPath": ["mask"], "target": {"__id__": 229}, "targetInfo": {"__id__": 538}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetInfo", "localID": ["18DIFwQw1Oxqfzmx1/3Hg3"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 344}, "sourceInfo": null, "propertyPath": ["rewards", "0"], "target": {"__id__": 294}, "targetInfo": {"__id__": 540}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 344}, "sourceInfo": null, "propertyPath": ["rewards", "1"], "target": {"__id__": 302}, "targetInfo": {"__id__": 542}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 344}, "sourceInfo": null, "propertyPath": ["rewards", "2"], "target": {"__id__": 310}, "targetInfo": {"__id__": 544}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 310}, "sourceInfo": {"__id__": 546}, "propertyPath": ["iconBgWhite"], "target": {"__id__": 310}, "targetInfo": {"__id__": 547}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["1cRpT6h1BL8IyE6jlawsNK"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 310}, "sourceInfo": {"__id__": 549}, "propertyPath": ["iconBgBlue"], "target": {"__id__": 310}, "targetInfo": {"__id__": 550}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["c3VBUAo6dBuJYhBT+IF4b8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 310}, "sourceInfo": {"__id__": 552}, "propertyPath": ["iconBgRed"], "target": {"__id__": 310}, "targetInfo": {"__id__": 553}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["b7KpBJykhP/aYJeVfxJBkW"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 310}, "sourceInfo": {"__id__": 555}, "propertyPath": ["reward"], "target": {"__id__": 310}, "targetInfo": {"__id__": 556}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["71wmgKNABPj7+h8b4epL4/"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 310}, "sourceInfo": {"__id__": 558}, "propertyPath": ["fragment"], "target": {"__id__": 310}, "targetInfo": {"__id__": 559}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}, {"__type__": "cc.TargetInfo", "localID": ["055QrqBytHCrcksGT/8Mwj"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "0"], "target": {"__id__": 125}, "targetInfo": {"__id__": 561}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "1"], "target": {"__id__": 151}, "targetInfo": {"__id__": 563}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "2"], "target": {"__id__": 177}, "targetInfo": {"__id__": 565}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "3"], "target": {"__id__": 203}, "targetInfo": {"__id__": 567}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "4"], "target": {"__id__": 229}, "targetInfo": {"__id__": 569}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["normalBoards", "5"], "target": {"__id__": 255}, "targetInfo": {"__id__": 571}}, {"__type__": "cc.TargetInfo", "localID": ["627oPDzgtO57ArT7LuOT/4"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 573}, "propertyPath": ["close"], "target": {"__id__": 91}, "targetInfo": {"__id__": 574}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 576}, "propertyPath": ["open"], "target": {"__id__": 91}, "targetInfo": {"__id__": 577}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 579}, "propertyPath": ["receive"], "target": {"__id__": 91}, "targetInfo": {"__id__": 580}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 582}, "propertyPath": ["reddot"], "target": {"__id__": 91}, "targetInfo": {"__id__": 583}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 585}, "propertyPath": ["tip"], "target": {"__id__": 91}, "targetInfo": {"__id__": 586}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 91}, "sourceInfo": {"__id__": 588}, "propertyPath": ["tipItems"], "target": {"__id__": 91}, "targetInfo": {"__id__": 589}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["67Pme3XWZCYoxPFVXudR6u"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 59}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 54}, "targetInfo": {"__id__": 591}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 81}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 76}, "targetInfo": {"__id__": 593}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 96}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 91}, "targetInfo": {"__id__": 595}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 113}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 108}, "targetInfo": {"__id__": 597}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 599}, "propertyPath": ["tip"], "target": {"__id__": 54}, "targetInfo": {"__id__": 600}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 54}, "sourceInfo": {"__id__": 602}, "propertyPath": ["tipItems"], "target": {"__id__": 54}, "targetInfo": {"__id__": 603}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["67Pme3XWZCYoxPFVXudR6u"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["boxes", "0"], "target": {"__id__": 54}, "targetInfo": {"__id__": 605}}, {"__type__": "cc.TargetInfo", "localID": ["11UqaeFjpI06J/KTrInI6l"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["boxes", "1"], "target": {"__id__": 76}, "targetInfo": {"__id__": 607}}, {"__type__": "cc.TargetInfo", "localID": ["82HwQeniRMhZ8Xqq2UP8QM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["boxes", "2"], "target": {"__id__": 91}, "targetInfo": {"__id__": 609}}, {"__type__": "cc.TargetInfo", "localID": ["11AfW8+9JCV47PxiXCGEAp"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 368}, "sourceInfo": null, "propertyPath": ["boxes", "3"], "target": {"__id__": 108}, "targetInfo": {"__id__": 611}}, {"__type__": "cc.TargetInfo", "localID": ["0bP8CEv/dC+LrCMLVIN0hn"]}]