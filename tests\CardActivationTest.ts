/**
 * 分阶段雪人激活功能测试
 * 测试StageDataMgr中的卡牌激活逻辑
 */

import { Vec2 } from "cc";
import { StageDataMgr, CardActivationConfig, CardActivationRule } from "../assets/scripts/mine/StageDataMgr";
import { CardMgr } from "../assets/scripts/card/CardMgr";
import { EventTargetMgr } from "../assets/scripts/EventTargetMgr";
import { EventTag } from "../assets/scripts/EventId";

describe("CardActivation", () => {
    let stageDataMgr: StageDataMgr;
    let cardMgr: CardMgr;
    let mockConfig: CardActivationConfig;
    let activatedCards: number[];

    beforeEach(() => {
        // 初始化管理器
        stageDataMgr = StageDataMgr.getInstance();
        cardMgr = CardMgr.getInstance();
        activatedCards = [];

        // 模拟配置
        mockConfig = {
            rules: [
                {
                    stageId: 1,
                    round: 2,
                    cardId: 2,
                    description: "Stage 1 Round 2: 激活第2个雪人"
                },
                {
                    stageId: 2,
                    round: 1,
                    cardId: 3,
                    description: "Stage 2 Round 1: 激活第3个雪人"
                }
            ]
        };

        // 监听卡牌激活事件
        EventTargetMgr.instance.addListener(EventTag.CardActivated, 1, (cardId: number) => {
            activatedCards.push(cardId);
        }, this);

        // 模拟已装备的卡牌
        cardMgr.equippedCardIds.clear();
        cardMgr.equippedCardIds.add(1);
        cardMgr.equippedCardIds.add(2);
        cardMgr.equippedCardIds.add(3);
    });

    afterEach(() => {
        // 清理事件监听
        EventTargetMgr.instance.removeListener(EventTag.CardActivated, this);
        activatedCards = [];
    });

    describe("配置加载", () => {
        test("应该正确加载卡牌激活配置", async () => {
            // 模拟配置加载
            (stageDataMgr as any)._cardActivationConfig = mockConfig;
            
            expect((stageDataMgr as any)._cardActivationConfig).toBeDefined();
            expect((stageDataMgr as any)._cardActivationConfig.rules).toHaveLength(2);
        });

        test("配置加载失败时应该使用默认行为", async () => {
            // 模拟配置加载失败
            (stageDataMgr as any)._cardActivationConfig = { rules: [] };
            
            expect((stageDataMgr as any)._cardActivationConfig.rules).toHaveLength(0);
        });
    });

    describe("卡牌激活规则", () => {
        beforeEach(() => {
            (stageDataMgr as any)._cardActivationConfig = mockConfig;
        });

        test("Stage 1 Round 1 应该只有第1个雪人激活", () => {
            // 模拟初始化状态
            const activeCards = new Set<number>();
            activeCards.add(1); // 第1个卡牌始终激活

            cardMgr.resetCardActivationState(activeCards);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(false);
            expect(cardMgr.isCardActive(3)).toBe(false);
        });

        test("Stage 1 Round 2 应该激活第2个雪人", () => {
            activatedCards = [];
            
            // 模拟回合切换到Round 2
            (stageDataMgr as any).checkAndActivateCards(1, 2);

            expect(activatedCards).toContain(2);
            expect(activatedCards).not.toContain(3);
        });

        test("Stage 2 Round 1 应该激活第3个雪人", () => {
            activatedCards = [];
            
            // 模拟关卡切换到Stage 2
            (stageDataMgr as any).checkAndActivateCards(2, 1);

            expect(activatedCards).toContain(3);
            expect(activatedCards).not.toContain(2);
        });

        test("不匹配的关卡回合不应该激活任何卡牌", () => {
            activatedCards = [];
            
            // 测试不存在的规则
            (stageDataMgr as any).checkAndActivateCards(3, 1);
            (stageDataMgr as any).checkAndActivateCards(1, 3);

            expect(activatedCards).toHaveLength(0);
        });
    });

    describe("初始化卡牌状态", () => {
        beforeEach(() => {
            (stageDataMgr as any)._cardActivationConfig = mockConfig;
        });

        test("Stage 1 Round 1 初始化应该正确", () => {
            (stageDataMgr as any).initCardActivationState(1, 1);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(false);
            expect(cardMgr.isCardActive(3)).toBe(false);
        });

        test("Stage 1 Round 2 初始化应该包含前面回合的激活", () => {
            (stageDataMgr as any).initCardActivationState(1, 2);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(true);
            expect(cardMgr.isCardActive(3)).toBe(false);
        });

        test("Stage 2 Round 1 初始化应该正确", () => {
            (stageDataMgr as any).initCardActivationState(2, 1);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(false);
            expect(cardMgr.isCardActive(3)).toBe(true);
        });
    });

    describe("回合切换", () => {
        beforeEach(() => {
            (stageDataMgr as any)._cardActivationConfig = mockConfig;
            (stageDataMgr as any)._curStageId = 1;
            (stageDataMgr as any)._curStageProgress = { 
                stageId: 1, 
                round: 1,
                depth: 0,
                score: 0
            };
            (stageDataMgr as any)._curStageConfig = {
                round: [
                    { /* round 1 config */ },
                    { /* round 2 config */ }
                ]
            };
        });

        test("从Round 1切换到Round 2应该激活第2个雪人", () => {
            activatedCards = [];
            
            // 模拟完成回合
            (stageDataMgr as any).completeRound();

            expect(activatedCards).toContain(2);
            expect((stageDataMgr as any)._curStageProgress.round).toBe(2);
        });
    });

    describe("游戏开始和继续", () => {
        beforeEach(() => {
            (stageDataMgr as any)._cardActivationConfig = mockConfig;
        });

        test("新游戏开始应该正确初始化卡牌状态", () => {
            // 模拟新游戏
            (stageDataMgr as any)._curStageProgress = null;
            
            // 模拟startGame调用
            (stageDataMgr as any)._curStageProgress = { 
                stageId: 1, 
                round: 1,
                depth: 0,
                score: 0
            };
            (stageDataMgr as any).initCardActivationState(1, 1);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(false);
            expect(cardMgr.isCardActive(3)).toBe(false);
        });

        test("继续游戏应该恢复正确的卡牌状态", () => {
            // 模拟继续游戏到Stage 1 Round 2
            (stageDataMgr as any)._curStageProgress = { 
                stageId: 1, 
                round: 2,
                depth: 0,
                score: 0
            };
            (stageDataMgr as any).initCardActivationState(1, 2);

            expect(cardMgr.isCardActive(1)).toBe(true);
            expect(cardMgr.isCardActive(2)).toBe(true);
            expect(cardMgr.isCardActive(3)).toBe(false);
        });
    });

    describe("边界情况", () => {
        test("没有配置时不应该激活任何卡牌", () => {
            (stageDataMgr as any)._cardActivationConfig = null;
            activatedCards = [];
            
            (stageDataMgr as any).checkAndActivateCards(1, 2);
            
            expect(activatedCards).toHaveLength(0);
        });

        test("空配置时不应该激活任何卡牌", () => {
            (stageDataMgr as any)._cardActivationConfig = { rules: [] };
            activatedCards = [];
            
            (stageDataMgr as any).checkAndActivateCards(1, 2);
            
            expect(activatedCards).toHaveLength(0);
        });

        test("同一回合多个激活规则应该都生效", () => {
            const multiConfig: CardActivationConfig = {
                rules: [
                    { stageId: 1, round: 2, cardId: 2 },
                    { stageId: 1, round: 2, cardId: 3 }
                ]
            };
            (stageDataMgr as any)._cardActivationConfig = multiConfig;
            activatedCards = [];
            
            (stageDataMgr as any).checkAndActivateCards(1, 2);
            
            expect(activatedCards).toContain(2);
            expect(activatedCards).toContain(3);
            expect(activatedCards).toHaveLength(2);
        });
    });
});
