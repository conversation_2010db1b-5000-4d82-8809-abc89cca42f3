/**
 * ASCII房间摆放效果演示
 * 在控制台中模拟家具摆放和房间评分系统
 */

import { Vec2 } from "cc";
import { 
    FurnitureTemplate, 
    PlacedFurniture, 
    FurnitureType, 
    FurnitureTheme, 
    Rotation,
    GridState 
} from "../assets/scripts/decoration/DecorationDefine";
import { RoomScoreCalculator } from "../assets/scripts/decoration/RoomScoreCalculator";

// ASCII字符定义
const ASCII_CHARS = {
    EMPTY: '·',           // 空地
    OBSTACLE: '█',        // 障碍物
    CHAIR: 'C',          // 椅子 (1x1)
    BED: 'B',            // 床 (2x1)
    TABLE: 'T',          // 桌子 (2x2)
    WALL: '█',           // 墙壁
    CORNER: '+',         // 角落
    H_WALL: '-',         // 水平墙
    V_WALL: '|'          // 垂直墙
};

// 颜色代码 (ANSI)
const COLORS = {
    RESET: '\x1b[0m',
    RED: '\x1b[31m',
    GREEN: '\x1b[32m',
    YELLOW: '\x1b[33m',
    BLUE: '\x1b[34m',
    MAGENTA: '\x1b[35m',
    CYAN: '\x1b[36m',
    WHITE: '\x1b[37m',
    GRAY: '\x1b[90m'
};

class ASCIIRoomDemo {
    private roomSize: Vec2;
    private grid: GridState[][];
    private furnitureTemplates: Map<number, FurnitureTemplate>;
    private placedFurnitures: PlacedFurniture[];
    private scoreCalculator: RoomScoreCalculator;

    constructor(width: number = 10, height: number = 8) {
        this.roomSize = new Vec2(width, height);
        this.grid = this.initializeGrid();
        this.furnitureTemplates = this.createFurnitureTemplates();
        this.placedFurnitures = [];
        this.scoreCalculator = new RoomScoreCalculator();
    }

    /**
     * 初始化房间格子
     */
    private initializeGrid(): GridState[][] {
        const grid: GridState[][] = [];
        for (let y = 0; y < this.roomSize.y; y++) {
            grid[y] = [];
            for (let x = 0; x < this.roomSize.x; x++) {
                grid[y][x] = GridState.Empty;
            }
        }
        return grid;
    }

    /**
     * 创建家具模板
     */
    private createFurnitureTemplates(): Map<number, FurnitureTemplate> {
        const templates = new Map<number, FurnitureTemplate>();
        
        templates.set(1, {
            id: 1,
            name: "现代椅子",
            type: FurnitureType.Small,
            baseSize: new Vec2(1, 1),
            spriteFrame: "chair",
            description: "舒适的现代椅子",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 1,
                value: 8,
                beauty: 6
            }
        });

        templates.set(2, {
            id: 2,
            name: "古典床",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "bed",
            description: "优雅的古典床铺",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 2,
                value: 15,
                beauty: 8
            }
        });

        templates.set(3, {
            id: 3,
            name: "工业桌子",
            type: FurnitureType.Large,
            baseSize: new Vec2(2, 2),
            spriteFrame: "table",
            description: "粗犷的工业风桌子",
            properties: {
                theme: FurnitureTheme.Industrial,
                level: 2,
                value: 12,
                beauty: 5
            }
        });

        return templates;
    }

    /**
     * 摆放家具
     */
    placeFurniture(templateId: number, x: number, y: number, rotation: Rotation = Rotation.Deg0): boolean {
        const template = this.furnitureTemplates.get(templateId);
        if (!template) return false;

        const currentSize = this.getRotatedSize(template.baseSize, rotation);
        
        // 检查边界
        if (x + currentSize.x > this.roomSize.x || y + currentSize.y > this.roomSize.y) {
            return false;
        }

        // 检查占用
        for (let dy = 0; dy < currentSize.y; dy++) {
            for (let dx = 0; dx < currentSize.x; dx++) {
                if (this.grid[y + dy][x + dx] !== GridState.Empty) {
                    return false;
                }
            }
        }

        // 放置家具
        const furniture: PlacedFurniture = {
            id: `furniture_${Date.now()}`,
            templateId: templateId,
            position: new Vec2(x, y),
            rotation: rotation,
            currentSize: currentSize,
            placedTime: Date.now()
        };

        this.placedFurnitures.push(furniture);

        // 更新格子状态
        for (let dy = 0; dy < currentSize.y; dy++) {
            for (let dx = 0; dx < currentSize.x; dx++) {
                this.grid[y + dy][x + dx] = GridState.Occupied;
            }
        }

        return true;
    }

    /**
     * 获取旋转后的尺寸
     */
    private getRotatedSize(baseSize: Vec2, rotation: Rotation): Vec2 {
        switch (rotation) {
            case Rotation.Deg0:
            case Rotation.Deg180:
                return new Vec2(baseSize.x, baseSize.y);
            case Rotation.Deg90:
            case Rotation.Deg270:
                return new Vec2(baseSize.y, baseSize.x);
            default:
                return new Vec2(baseSize.x, baseSize.y);
        }
    }

    /**
     * 获取家具ASCII字符
     */
    private getFurnitureChar(templateId: number): string {
        switch (templateId) {
            case 1: return ASCII_CHARS.CHAIR;
            case 2: return ASCII_CHARS.BED;
            case 3: return ASCII_CHARS.TABLE;
            default: return '?';
        }
    }

    /**
     * 获取主题颜色
     */
    private getThemeColor(theme: FurnitureTheme): string {
        switch (theme) {
            case FurnitureTheme.Modern: return COLORS.CYAN;
            case FurnitureTheme.Classic: return COLORS.YELLOW;
            case FurnitureTheme.Natural: return COLORS.GREEN;
            case FurnitureTheme.Industrial: return COLORS.GRAY;
            case FurnitureTheme.Minimalist: return COLORS.WHITE;
            default: return COLORS.RESET;
        }
    }

    /**
     * 渲染房间
     */
    renderRoom(): void {
        console.clear();
        console.log(`${COLORS.BLUE}╔════════════════════════════════════════╗${COLORS.RESET}`);
        console.log(`${COLORS.BLUE}║           房间布局演示 (${this.roomSize.x}x${this.roomSize.y})           ║${COLORS.RESET}`);
        console.log(`${COLORS.BLUE}╚════════════════════════════════════════╝${COLORS.RESET}`);
        console.log();

        // 渲染上边框
        let topBorder = COLORS.WHITE + '+';
        for (let x = 0; x < this.roomSize.x; x++) {
            topBorder += '-';
        }
        topBorder += '+' + COLORS.RESET;
        console.log(topBorder);

        // 渲染房间内容
        for (let y = 0; y < this.roomSize.y; y++) {
            let line = COLORS.WHITE + '|' + COLORS.RESET;
            
            for (let x = 0; x < this.roomSize.x; x++) {
                const furniture = this.getFurnitureAt(x, y);
                if (furniture) {
                    const template = this.furnitureTemplates.get(furniture.templateId)!;
                    const color = this.getThemeColor(template.properties.theme);
                    const char = this.getFurnitureChar(furniture.templateId);
                    line += color + char + COLORS.RESET;
                } else {
                    line += this.grid[y][x] === GridState.Empty ? 
                        COLORS.GRAY + ASCII_CHARS.EMPTY + COLORS.RESET : 
                        COLORS.RED + ASCII_CHARS.OBSTACLE + COLORS.RESET;
                }
            }
            
            line += COLORS.WHITE + '|' + COLORS.RESET;
            console.log(line);
        }

        // 渲染下边框
        let bottomBorder = COLORS.WHITE + '+';
        for (let x = 0; x < this.roomSize.x; x++) {
            bottomBorder += '-';
        }
        bottomBorder += '+' + COLORS.RESET;
        console.log(bottomBorder);
    }

    /**
     * 获取指定位置的家具
     */
    private getFurnitureAt(x: number, y: number): PlacedFurniture | null {
        for (const furniture of this.placedFurnitures) {
            const { position, currentSize } = furniture;
            if (x >= position.x && x < position.x + currentSize.x &&
                y >= position.y && y < position.y + currentSize.y) {
                return furniture;
            }
        }
        return null;
    }

    /**
     * 显示房间评分
     */
    showRoomScore(): void {
        const scoreDetails = this.scoreCalculator.calculateRoomScore(
            this.placedFurnitures,
            this.furnitureTemplates,
            this.roomSize
        );

        console.log();
        console.log(`${COLORS.MAGENTA}╔════════════════════════════════════════╗${COLORS.RESET}`);
        console.log(`${COLORS.MAGENTA}║              房间评分详情              ║${COLORS.RESET}`);
        console.log(`${COLORS.MAGENTA}╚════════════════════════════════════════╝${COLORS.RESET}`);
        console.log();
        console.log(`${COLORS.YELLOW}主题得分: ${COLORS.GREEN}${scoreDetails.themeScore.toFixed(1)}${COLORS.RESET}`);
        console.log(`${COLORS.YELLOW}数量得分: ${COLORS.GREEN}${scoreDetails.quantityScore.toFixed(1)}${COLORS.RESET}`);
        console.log(`${COLORS.YELLOW}价值得分: ${COLORS.GREEN}${scoreDetails.valueScore.toFixed(1)}${COLORS.RESET}`);
        console.log(`${COLORS.YELLOW}布局得分: ${COLORS.GREEN}${scoreDetails.layoutScore.toFixed(1)}${COLORS.RESET}`);
        console.log(`${COLORS.CYAN}总分: ${COLORS.GREEN}${scoreDetails.totalScore}${COLORS.RESET}`);
        
        if (scoreDetails.dominantTheme) {
            const themeName = this.scoreCalculator.getThemeName(scoreDetails.dominantTheme);
            console.log(`${COLORS.YELLOW}主导主题: ${COLORS.GREEN}${themeName}${COLORS.RESET}`);
        }
    }

    /**
     * 显示图例
     */
    showLegend(): void {
        console.log();
        console.log(`${COLORS.BLUE}╔════════════════════════════════════════╗${COLORS.RESET}`);
        console.log(`${COLORS.BLUE}║                图例说明                ║${COLORS.RESET}`);
        console.log(`${COLORS.BLUE}╚════════════════════════════════════════╝${COLORS.RESET}`);
        console.log();
        console.log(`${COLORS.CYAN}C${COLORS.RESET} - 椅子 (现代风格)`);
        console.log(`${COLORS.YELLOW}B${COLORS.RESET} - 床 (古典风格)`);
        console.log(`${COLORS.GRAY}T${COLORS.RESET} - 桌子 (工业风格)`);
        console.log(`${COLORS.GRAY}·${COLORS.RESET} - 空地`);
        console.log(`${COLORS.RED}█${COLORS.RESET} - 障碍物`);
    }
}

// 演示函数
export function runASCIIRoomDemo(): void {
    const demo = new ASCIIRoomDemo(12, 8);
    
    console.log(`${COLORS.GREEN}开始房间摆放演示...${COLORS.RESET}`);
    
    // 演示步骤1: 空房间
    demo.renderRoom();
    demo.showLegend();
    console.log(`\n${COLORS.YELLOW}步骤1: 空房间${COLORS.RESET}`);
    
    setTimeout(() => {
        // 演示步骤2: 放置第一个家具
        demo.placeFurniture(1, 2, 2); // 椅子
        demo.renderRoom();
        demo.showRoomScore();
        console.log(`\n${COLORS.YELLOW}步骤2: 放置椅子${COLORS.RESET}`);
        
        setTimeout(() => {
            // 演示步骤3: 放置床
            demo.placeFurniture(2, 5, 1); // 床
            demo.renderRoom();
            demo.showRoomScore();
            console.log(`\n${COLORS.YELLOW}步骤3: 放置床${COLORS.RESET}`);
            
            setTimeout(() => {
                // 演示步骤4: 放置桌子
                demo.placeFurniture(3, 8, 4); // 桌子
                demo.renderRoom();
                demo.showRoomScore();
                console.log(`\n${COLORS.YELLOW}步骤4: 放置桌子${COLORS.RESET}`);
                
                setTimeout(() => {
                    // 演示步骤5: 添加更多椅子
                    demo.placeFurniture(1, 1, 5);
                    demo.placeFurniture(1, 4, 6);
                    demo.renderRoom();
                    demo.showRoomScore();
                    console.log(`\n${COLORS.YELLOW}步骤5: 添加更多椅子${COLORS.RESET}`);
                    console.log(`\n${COLORS.GREEN}演示完成！${COLORS.RESET}`);
                }, 2000);
            }, 2000);
        }, 2000);
    }, 2000);
}
