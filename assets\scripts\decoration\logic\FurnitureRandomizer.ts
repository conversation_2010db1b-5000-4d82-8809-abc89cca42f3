/**
 * 家具随机变化器
 * 实现家具位置和角度的随机变化功能
 */

import { Vec2 } from "cc";
import { PlacedFurniture, FurnitureTemplate, Rotation, getRotatedSize } from "./DecorationDefine";
import { FurniturePlacementValidator } from "./FurniturePlacementValidator";
import { RoomGrid } from "./RoomGrid";

/**
 * 随机变化结果
 */
export interface RandomizeResult {
    success: boolean;
    originalFurniture: PlacedFurniture;
    newFurniture?: PlacedFurniture;
    changeType: 'position' | 'rotation' | 'both' | 'none';
    attempts: number;
    reason?: string;
}

/**
 * 随机变化配置
 */
export interface RandomizeConfig {
    maxAttempts: number;        // 最大尝试次数
    rotationProbability: number; // 旋转概率 (0-1)
    searchRadius: number;       // 搜索半径
    allowRoomChange: boolean;   // 是否允许跨房间
}

export class FurnitureRandomizer {
    private roomGrid: RoomGrid;
    private validator: FurniturePlacementValidator;
    private defaultConfig: RandomizeConfig = {
        maxAttempts: 100,
        rotationProbability: 0.5,
        searchRadius: 5,
        allowRoomChange: false
    };

    constructor(roomGrid: RoomGrid) {
        this.roomGrid = roomGrid;
        this.validator = new FurniturePlacementValidator(roomGrid, []);
    }

    /**
     * 随机变化家具位置和角度
     */
    randomizeFurniture(
        targetFurniture: PlacedFurniture,
        template: FurnitureTemplate,
        allFurnitures: PlacedFurniture[],
        config?: Partial<RandomizeConfig>
    ): RandomizeResult {
        const finalConfig = { ...this.defaultConfig, ...config };
        
        // 更新验证器的家具列表
        this.validator = new FurniturePlacementValidator(this.roomGrid, allFurnitures);

        const originalPosition = new Vec2(targetFurniture.position.x, targetFurniture.position.y);
        const originalRotation = targetFurniture.rotation;

        // 确定搜索范围
        const searchBounds = this.getSearchBounds(originalPosition, finalConfig);
        
        let attempts = 0;
        let bestResult: RandomizeResult | null = null;

        while (attempts < finalConfig.maxAttempts) {
            attempts++;

            // 生成随机位置
            const newPosition = this.generateRandomPosition(originalPosition, searchBounds);
            
            // 决定是否旋转
            const shouldRotate = Math.random() < finalConfig.rotationProbability;
            const newRotation = shouldRotate ? this.generateRandomRotation(originalRotation) : originalRotation;

            // 创建测试家具
            const testFurniture: PlacedFurniture = {
                ...targetFurniture,
                position: newPosition,
                rotation: newRotation,
                currentSize: getRotatedSize(template.baseSize, newRotation)
            };

            // 验证新位置
            const validation = this.validator.validatePlacement(
                template,
                newPosition,
                newRotation,
                targetFurniture.id
            );

            if (validation.isValid) {
                const changeType = this.determineChangeType(
                    originalPosition, originalRotation,
                    newPosition, newRotation
                );

                return {
                    success: true,
                    originalFurniture: targetFurniture,
                    newFurniture: testFurniture,
                    changeType,
                    attempts
                };
            }

            // 记录最佳尝试（如果有建议位置）
            if (validation.suggestedPosition && !bestResult) {
                bestResult = {
                    success: false,
                    originalFurniture: targetFurniture,
                    changeType: 'none',
                    attempts,
                    reason: validation.reason
                };
            }
        }

        // 如果所有尝试都失败，返回失败结果
        return bestResult || {
            success: false,
            originalFurniture: targetFurniture,
            changeType: 'none',
            attempts,
            reason: "无法找到合适的位置"
        };
    }

    /**
     * 批量随机变化多个家具
     */
    randomizeMultipleFurnitures(
        furnitures: PlacedFurniture[],
        templates: Map<number, FurnitureTemplate>,
        config?: Partial<RandomizeConfig>
    ): RandomizeResult[] {
        const results: RandomizeResult[] = [];
        let currentFurnitures = [...furnitures];

        for (let i = 0; i < furnitures.length; i++) {
            const furniture = furnitures[i];
            const template = templates.get(furniture.templateId);
            
            if (!template) {
                results.push({
                    success: false,
                    originalFurniture: furniture,
                    changeType: 'none',
                    attempts: 0,
                    reason: "找不到家具模板"
                });
                continue;
            }

            const result = this.randomizeFurniture(furniture, template, currentFurnitures, config);
            results.push(result);

            // 如果成功，更新家具列表
            if (result.success && result.newFurniture) {
                currentFurnitures[i] = result.newFurniture;
            }
        }

        return results;
    }

    /**
     * 获取搜索边界
     */
    private getSearchBounds(center: Vec2, config: RandomizeConfig): { min: Vec2, max: Vec2 } {
        const roomSize = this.roomGrid.getRoomSize();
        const radius = config.searchRadius;

        return {
            min: new Vec2(
                Math.max(0, center.x - radius),
                Math.max(0, center.y - radius)
            ),
            max: new Vec2(
                Math.min(roomSize.x - 1, center.x + radius),
                Math.min(roomSize.y - 1, center.y + radius)
            )
        };
    }

    /**
     * 生成随机位置
     */
    private generateRandomPosition(center: Vec2, bounds: { min: Vec2, max: Vec2 }): Vec2 {
        return new Vec2(
            Math.floor(Math.random() * (bounds.max.x - bounds.min.x + 1)) + bounds.min.x,
            Math.floor(Math.random() * (bounds.max.y - bounds.min.y + 1)) + bounds.min.y
        );
    }

    /**
     * 生成随机旋转角度
     */
    private generateRandomRotation(currentRotation: Rotation): Rotation {
        const rotations = [Rotation.Deg0, Rotation.Deg90, Rotation.Deg180, Rotation.Deg270];
        const availableRotations = rotations.filter(r => r !== currentRotation);
        
        if (availableRotations.length === 0) {
            return currentRotation;
        }

        const randomIndex = Math.floor(Math.random() * availableRotations.length);
        return availableRotations[randomIndex];
    }



    /**
     * 确定变化类型
     */
    private determineChangeType(
        oldPos: Vec2, oldRot: Rotation,
        newPos: Vec2, newRot: Rotation
    ): 'position' | 'rotation' | 'both' | 'none' {
        const positionChanged = !oldPos.equals(newPos);
        const rotationChanged = oldRot !== newRot;

        if (positionChanged && rotationChanged) {
            return 'both';
        } else if (positionChanged) {
            return 'position';
        } else if (rotationChanged) {
            return 'rotation';
        } else {
            return 'none';
        }
    }

    /**
     * 设置默认配置
     */
    setDefaultConfig(config: Partial<RandomizeConfig>): void {
        this.defaultConfig = { ...this.defaultConfig, ...config };
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig(): RandomizeConfig {
        return { ...this.defaultConfig };
    }

    /**
     * 获取变化类型描述
     */
    static getChangeTypeDescription(changeType: string): string {
        switch (changeType) {
            case 'position':
                return '位置变化';
            case 'rotation':
                return '角度旋转';
            case 'both':
                return '位置和角度变化';
            case 'none':
                return '无变化';
            default:
                return '未知变化';
        }
    }

    /**
     * 计算变化距离
     */
    static calculateChangeDistance(oldPos: Vec2, newPos: Vec2): number {
        return Math.sqrt(Math.pow(newPos.x - oldPos.x, 2) + Math.pow(newPos.y - oldPos.y, 2));
    }
}
