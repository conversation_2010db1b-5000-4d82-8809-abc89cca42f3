{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2f22669e-5f91-4848-a3ba-04957b00a318", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2f22669e-5f91-4848-a3ba-04957b00a318@6c48a", "displayName": "合成用-时光布2", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2f22669e-5f91-4848-a3ba-04957b00a318", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2f22669e-5f91-4848-a3ba-04957b00a318@f9941", "displayName": "合成用-时光布2", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 132, "height": 247, "rawWidth": 132, "rawHeight": 247, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-66, -123.5, 0, 66, -123.5, 0, -66, 123.5, 0, 66, 123.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 247, 132, 247, 0, 0, 132, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-66, -123.5, 0], "maxPos": [66, 123.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2f22669e-5f91-4848-a3ba-04957b00a318@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2f22669e-5f91-4848-a3ba-04957b00a318@6c48a"}}