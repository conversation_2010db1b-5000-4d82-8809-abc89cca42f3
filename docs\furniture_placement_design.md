# 房间家具摆放系统设计文档

## 系统概述
设计一套完整的房间内家具摆放系统，支持多种尺寸家具的放置、旋转、移除等操作。

## 需求分析

### 房间格子系统
- 使用二维数组表示房间布局
- 0: 空地（可摆放）
- 1: 障碍（不可摆放）
- 2: 已占用（被家具占用）

### 家具规格
- 1×1: 小型家具（椅子、台灯等）
- 2×1: 中型家具（床、沙发等）
- 2×2: 大型家具（桌子、柜子等）

### 操作功能
- 家具放置：检查位置合法性
- 家具旋转：90度整数倍旋转
- 家具移除：释放占用格子
- 碰撞检测：防止重叠和越界

## 技术架构

### 核心类设计
1. **RoomGrid**: 房间格子管理
2. **Furniture**: 家具数据模型
3. **FurnitureManager**: 家具管理器
4. **PlacementValidator**: 摆放验证器
5. **RoomDecorationUI**: 摆放界面

### 数据结构
```typescript
// 家具类型枚举
enum FurnitureType {
  Chair = 1,    // 1×1
  Bed = 2,      // 2×1
  Table = 3     // 2×2
}

// 旋转角度枚举
enum Rotation {
  Deg0 = 0,
  Deg90 = 90,
  Deg180 = 180,
  Deg270 = 270
}

// 家具模板数据
interface FurnitureTemplate {
  id: number;
  name: string;
  type: FurnitureType;
  baseSize: Vec2;  // 基础尺寸
  spriteFrame: string;
}

// 已放置的家具实例
interface PlacedFurniture {
  id: string;
  templateId: number;
  position: Vec2;
  rotation: Rotation;
  currentSize: Vec2;  // 当前旋转后的尺寸
}
```

## 实现方案

### 1. 格子状态管理
- 使用二维数组跟踪每个格子的状态
- 支持动态更新格子占用状态
- 提供格子状态查询接口

### 2. 碰撞检测算法
- 检查家具边界是否超出房间范围
- 检查目标位置是否与障碍物重叠
- 检查是否与其他家具重叠

### 3. 旋转系统
- 计算旋转后的家具尺寸
- 验证旋转后位置的合法性
- 支持就地旋转和移动后旋转

### 4. UI交互设计
- 拖拽放置：从家具列表拖拽到房间
- 选中操作：点击选中已放置的家具
- 旋转控制：选中后显示旋转按钮
- 删除功能：选中后显示删除按钮

## 扩展功能
- 家具属性加成系统
- 房间装饰评分系统
- 家具解锁和获取系统
- 预设房间布局模板
