{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "c662d29a-baf8-4829-9636-cb9de6d16c18", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "c662d29a-baf8-4829-9636-cb9de6d16c18@6c48a", "displayName": "广告泡竖2格", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "c662d29a-baf8-4829-9636-cb9de6d16c18", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c662d29a-baf8-4829-9636-cb9de6d16c18@f9941", "displayName": "广告泡竖2格", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 226, "height": 393, "rawWidth": 226, "rawHeight": 393, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-113, -196.5, 0, 113, -196.5, 0, -113, 196.5, 0, 113, 196.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 393, 226, 393, 0, 0, 226, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-113, -196.5, 0], "maxPos": [113, 196.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c662d29a-baf8-4829-9636-cb9de6d16c18@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "c662d29a-baf8-4829-9636-cb9de6d16c18@6c48a"}}