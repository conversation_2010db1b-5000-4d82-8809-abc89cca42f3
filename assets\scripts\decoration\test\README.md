# 装饰系统测试文件夹

本文件夹包含装饰系统的各种测试脚本，用于验证系统功能的正确性。

## 测试文件说明

### AdjacentBonusTest.ts
**相邻加成功能测试脚本**

测试内容：
- 基础相邻检测机制
- 主题匹配相邻加成
- 特殊组合奖励（桌椅组合、挂饰装点等）
- 复杂布局综合测试
- 分散vs集中摆放对比

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `AdjacentBonusTest` 组件
3. 运行场景，查看控制台输出

### MultiRoomTest.ts
**双房间系统测试脚本**

测试内容：
- 默认房间模板验证
- 多房间管理器功能测试
- 装饰管理器集成测试
- 房间配置和家具摆放

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `MultiRoomTest` 组件
3. 运行场景，查看控制台输出

### LayoutStorageTest.ts
**布局存储功能测试脚本**

测试内容：
- 基本保存/加载功能测试
- 搜索和过滤功能测试
- 导入/导出功能测试
- 自动保存功能测试
- 存储统计和清理测试
- 布局复制功能测试
- 布局数据验证测试

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `LayoutStorageTest` 组件
3. 在属性面板中关联 `DecorationDemoManager` 组件
4. 运行场景，查看控制台输出

### FurnitureManagerMultiRoomTest.ts
**FurnitureManager 双房间入口函数测试脚本**

测试内容：
- 双房间系统可用性检查
- 双房间模式启用测试
- 双房间配置创建测试
- 跨房间家具放置测试
- 房间管理功能测试
- 双房间评分计算测试
- 错误处理和边界测试

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `FurnitureManagerMultiRoomTest` 组件
3. 运行场景，查看控制台输出
4. 可调用 `runFullTest()` 进行完整测试

### UnifiedRoomSetupTest.ts
**统一房间创建入口函数测试脚本**

测试内容：
- 单房间模式创建测试
- 双房间模式创建测试
- 多房间模式创建测试
- 自定义房间配置测试
- 参数组合验证测试
- 错误处理和边界测试
- 向后兼容性验证测试

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `UnifiedRoomSetupTest` 组件
3. 运行场景，查看控制台输出
4. 可调用 `runFullTest()` 进行完整测试

### FixRoomTemplatesTest.ts
**DefaultRoomTemplates.fixRoomTemplates 方法验证测试脚本**

测试内容：
- fixRoomTemplates 基本逻辑验证
- 边界处理正确性测试
- 障碍物邻接处理测试
- 原始模板完整性验证
- 边界情况和异常处理测试
- 所有默认模板验证测试
- 布局统计和可视化输出

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `FixRoomTemplatesTest` 组件
3. 运行场景，查看控制台输出
4. 可调用 `runFullTest()` 进行完整测试

## 测试数据

### 模拟家具模板
测试脚本包含以下模拟家具：

**AdjacentBonusTest.ts:**
- 现代椅子 (1×1, 现代风格, Small)
- 古典床 (2×1, 古典风格, Medium)
- 工业桌子 (2×2, 工业风格, Large)
- 现代沙发 (2×1, 现代风格, Medium)
- 古典壁画 (1×1, 古典风格, WallDecoration)

**MultiRoomTest.ts:**
- 测试椅子 (1×1, 现代风格)
- 测试桌子 (2×1, 现代风格)
- 测试沙发 (2×2, 古典风格)
- 测试壁画 (1×1, 古典风格, 挂饰类)

**LayoutStorageTest.ts:**
- 依赖于 `DecorationDemoManager` 提供的演示场景
- 使用不同步骤的演示布局进行测试
- 创建临时测试布局用于验证功能
- 自动清理测试数据避免污染存储

## 预期测试结果

### 相邻加成测试
```
=== 相邻加成功能测试开始 ===
--- 测试基础相邻检测 ---
两个相邻椅子的评分:
- 主题得分: 75
- 相邻得分: 7
- 总分: 45

两个不相邻椅子的评分:
- 主题得分: 75
- 相邻得分: 0
- 总分: 42

相邻加成差异: +7分
```

### 双房间系统测试
```
=== 双房间系统测试开始 ===
--- 测试默认房间模板 ---
获取到 4 个房间模板
房间A: 房间A, 尺寸: 10×10
房间A布局验证: true
房间A可用空间: 52
房间A邻墙格子: 28

--- 测试多房间管理器 ---
多房间管理器初始化完成
房间数量: 2, 房间ID: [room_a, room_b]
在房间A放置家具1: 成功
在房间B放置家具2: 成功
当前评分: 65 (主题:45, 数量:20, 价值:15, 布局:55)
```

### 布局存储测试
```
=== 开始布局存储功能测试 ===
--- 测试基本保存/加载功能 ---
测试快速保存...
快速保存结果: 成功
测试加载布局...
加载结果: 成功
测试删除布局...
删除结果: 成功

--- 测试搜索和过滤功能 ---
获取最近布局...
找到 3 个最近布局
按主题搜索布局...
找到 1 个现代风格布局
获取高分布局...
找到 2 个高分布局

--- 测试存储统计和清理 ---
存储统计: {总数量: 15, 用户保存: 12, 自动保存: 3, 平均评分: 67.5, 存储大小: 45.2KB}
清理完成，删除了 2 个布局
```

### FurnitureManager 双房间入口测试
```
=== FurnitureManager 双房间入口函数测试开始 ===
--- 测试系统可用性检查 ---
双房间系统可用性: 可用
可用房间模板数量: 4
- 房间A (10×10)
- 房间B (6×6)
- 大房间 (12×12)
- 小房间 (4×4)

--- 测试启用双房间模式 ---
启用双房间模式: 成功
多房间管理器获取: 成功

--- 测试双房间配置创建 ---
双房间配置创建: 成功
消息: 成功创建双房间配置: 房间A 和 房间B
房间ID列表: [room_a, room_b]
房间 room_a: 房间A, 尺寸: 10×10
房间 room_b: 房间B, 尺寸: 6×6

--- 测试家具放置功能 ---
使用家具模板ID: 1
在房间A放置家具: 成功
在房间B放置家具: 成功
在障碍位置放置家具: 失败
预期的错误信息: 位置被障碍物占用

--- 测试双房间评分计算 ---
双房间系统评分:
- 主题得分: 75
- 数量得分: 20
- 价值得分: 16
- 布局得分: 45
- 相邻得分: 0
- 总分: 52
- 主导主题: Modern
```

### 统一房间创建入口测试
```
=== 统一房间创建入口函数测试开始 ===
--- 测试单房间模式 ---
默认单房间创建: 成功
消息: 成功创建单房间配置，包含 1 个房间
房间数量: 1
房间ID: [room_a]
指定模板单房间创建: 成功
- 房间: 房间A (10×10)

--- 测试双房间模式 ---
默认双房间创建: 成功
消息: 成功创建双房间配置，包含 2 个房间
- 房间: 房间A (10×10)
- 房间: 房间B (6×6)
指定模板双房间创建: 成功
房间数量: 2
推荐配置双房间创建: 成功

--- 测试多房间模式 ---
默认多房间创建: 成功
消息: 成功创建多房间配置，包含 4 个房间
房间总数: 4
- 房间: 房间A (10×10)
- 房间: 房间B (6×6)
- 房间: 大房间 (12×12)
- 房间: 小房间 (4×4)

--- 测试自定义配置 ---
自定义双房间配置: 成功
- 自定义房间: 房间A (10×10)
- 自定义房间: 小房间 (4×4)

--- 测试向后兼容性 ---
旧方法 createDualRoomSetup: 成功
新方法等效调用: 成功
房间数量对比: 旧方法=2, 新方法=2
向后兼容性: 通过
```

### fixRoomTemplates 方法验证测试
```
=== DefaultRoomTemplates.fixRoomTemplates 方法验证测试开始 ===
--- 测试 fixRoomTemplates 基本逻辑 ---
原始 布局:
  ○○○○○
  ○·█·○
  ○███○
  ○·█·○
  ○○○○○
  (· = 空地, ○ = 邻墙, █ = 障碍)

修正后 布局:
  ○○○○○
  ○○█○○
  ○███○
  ○○█○○
  ○○○○○
  (· = 空地, ○ = 邻墙, █ = 障碍)

--- 测试边界处理 ---
边界处理验证: 通过

--- 测试障碍物邻接处理 ---
障碍物邻接测试 - 修正后布局:
  ○○○○○○
  ○○█○○○
  ○○██○○
  ○○○█○○
  ○○○○○○
  ○○○○○○
  (· = 空地, ○ = 邻墙, █ = 障碍)

--- 测试原始模板完整性 ---
房间A 修正验证: 通过
房间B 修正验证: 通过
小房间 修正验证: 通过
大房间 修正验证: 通过

--- 测试所有默认模板 ---
获取到 4 个默认模板
模板 1: 房间A (10×10)
模板有效性: 有效
格子统计: 空地=52, 邻墙=28, 障碍=20

模板 2: 房间B (6×6)
模板有效性: 有效
格子统计: 空地=16, 邻墙=16, 障碍=4
```

## 注意事项

1. **依赖关系**: 测试脚本依赖于装饰系统的核心类，确保相关文件已正确导入
2. **异步操作**: MultiRoomTest 和 LayoutStorageTest 包含异步操作，需要等待初始化完成
3. **组件依赖**: LayoutStorageTest 需要在属性面板中关联 DecorationDemoManager 组件
4. **控制台输出**: 所有测试结果都输出到控制台，运行时请打开开发者工具查看
5. **错误处理**: 测试脚本包含错误捕获，如果测试失败会显示详细错误信息
6. **数据清理**: LayoutStorageTest 会自动清理测试数据，避免污染实际存储

## 扩展测试

如需添加新的测试用例，可以：

1. **扩展现有测试**: 在现有测试方法中添加新的测试场景
2. **创建新测试文件**: 按照现有模式创建新的测试脚本
3. **添加性能测试**: 测试大量家具摆放时的性能表现
4. **添加边界测试**: 测试极端情况下的系统行为

## 测试最佳实践

1. **独立性**: 每个测试方法应该独立运行，不依赖其他测试的结果
2. **清理**: 测试完成后应该清理创建的数据，避免影响后续测试
3. **断言**: 添加明确的断言来验证测试结果
4. **文档**: 为新增的测试用例添加清晰的注释和说明
