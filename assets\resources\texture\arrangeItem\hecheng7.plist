<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-弹弹球.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,95}</string>
                <key>spriteSourceSize</key>
                <string>{96,95}</string>
                <key>textureRect</key>
                <string>{{127,233},{96,95}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-弹弹球2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,114}</string>
                <key>spriteSourceSize</key>
                <string>{116,114}</string>
                <key>textureRect</key>
                <string>{{135,1},{116,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-弹弹球3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,114}</string>
                <key>spriteSourceSize</key>
                <string>{116,114}</string>
                <key>textureRect</key>
                <string>{{135,117},{116,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-弹弹球4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,130}</string>
                <key>spriteSourceSize</key>
                <string>{132,130}</string>
                <key>textureRect</key>
                <string>{{1,1},{132,130}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-弹弹球5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,120}</string>
                <key>spriteSourceSize</key>
                <string>{124,120}</string>
                <key>textureRect</key>
                <string>{{1,133},{124,120}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng7.png</string>
            <key>size</key>
            <string>{252,329}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:03a8e94c13a2c0130effe70542a31bba:0422220021aa0348b52d8c89569e5991:3cfeac54e41c20f4a7c63468e170b759$</string>
            <key>textureFileName</key>
            <string>hecheng7.png</string>
        </dict>
    </dict>
</plist>
