<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-炸弹.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,397}</string>
                <key>spriteSourceSize</key>
                <string>{138,397}</string>
                <key>textureRect</key>
                <string>{{1,1},{138,397}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-炸弹2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,397}</string>
                <key>spriteSourceSize</key>
                <string>{138,397}</string>
                <key>textureRect</key>
                <string>{{1,400},{138,397}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-炸弹3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,397}</string>
                <key>spriteSourceSize</key>
                <string>{138,397}</string>
                <key>textureRect</key>
                <string>{{1,799},{138,397}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-炸弹4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,397}</string>
                <key>spriteSourceSize</key>
                <string>{138,397}</string>
                <key>textureRect</key>
                <string>{{1,1198},{138,397}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-炸弹5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{138,397}</string>
                <key>spriteSourceSize</key>
                <string>{138,397}</string>
                <key>textureRect</key>
                <string>{{1,1597},{138,397}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng1.png</string>
            <key>size</key>
            <string>{140,1995}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:deec8b838fa8f2f7f1f21c219555364b:45ef3231b4956ab855c969c6e865f2da:ce74e6dffd0d7f1f039552d5cc9501ff$</string>
            <key>textureFileName</key>
            <string>hecheng1.png</string>
        </dict>
    </dict>
</plist>
