/**
 * FurnitureManager 双房间入口函数测试
 * 验证双房间系统入口函数的正确性
 */

import { Component, Vec2, _decorator } from "cc";
import { FurnitureManager } from "../FurnitureManager";
import { Rotation } from "../DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('FurnitureManagerMultiRoomTest')
export class FurnitureManagerMultiRoomTest extends Component {
    
    private furnitureManager: FurnitureManager | null = null;

    async start() {
        console.log("=== FurnitureManager 双房间入口函数测试开始 ===");
        
        try {
            this.furnitureManager = FurnitureManager.getInstance();
            await this.furnitureManager.init();
            
            await this.testSystemAvailability();
            await this.testMultiRoomModeEnable();
            await this.testDualRoomSetup();
            await this.testFurniturePlacement();
            await this.testRoomManagement();
            await this.testScoreCalculation();
            
            console.log("=== FurnitureManager 双房间入口函数测试完成 ===");
        } catch (error) {
            console.error("测试过程中出现错误:", error);
        }
    }

    /**
     * 测试系统可用性检查
     */
    private async testSystemAvailability() {
        console.log("--- 测试系统可用性检查 ---");
        
        if (!this.furnitureManager) {
            console.error("FurnitureManager 未初始化");
            return;
        }

        // 检查双房间系统是否可用
        const isAvailable = this.furnitureManager.isMultiRoomSystemAvailable();
        console.log(`双房间系统可用性: ${isAvailable ? "可用" : "不可用"}`);

        // 获取可用的房间模板
        const roomTemplates = this.furnitureManager.getAvailableRoomTemplates();
        console.log(`可用房间模板数量: ${roomTemplates.length}`);
        
        roomTemplates.forEach(template => {
            console.log(`- ${template.name} (${template.size.x}×${template.size.y})`);
        });
    }

    /**
     * 测试启用双房间模式
     */
    private async testMultiRoomModeEnable() {
        console.log("--- 测试启用双房间模式 ---");
        
        if (!this.furnitureManager) return;

        // 启用双房间模式
        const enableResult = await this.furnitureManager.enableMultiRoomMode();
        console.log(`启用双房间模式: ${enableResult ? "成功" : "失败"}`);

        if (enableResult) {
            // 获取多房间管理器实例
            const multiRoomMgr = this.furnitureManager.getMultiRoomManager();
            console.log(`多房间管理器获取: ${multiRoomMgr ? "成功" : "失败"}`);
        }
    }

    /**
     * 测试双房间配置创建
     */
    private async testDualRoomSetup() {
        console.log("--- 测试双房间配置创建 ---");
        
        if (!this.furnitureManager) return;

        // 创建双房间配置
        const setupResult = await this.furnitureManager.createDualRoomSetup();
        console.log(`双房间配置创建: ${setupResult.success ? "成功" : "失败"}`);
        console.log(`消息: ${setupResult.message}`);
        
        if (setupResult.success && setupResult.roomIds) {
            console.log(`房间ID列表: [${setupResult.roomIds.join(', ')}]`);
            
            // 获取每个房间的信息
            for (const roomId of setupResult.roomIds) {
                const roomInfo = this.furnitureManager.getRoomInfo(roomId);
                if (roomInfo) {
                    console.log(`房间 ${roomId}: ${roomInfo.name}, 尺寸: ${roomInfo.size.x}×${roomInfo.size.y}`);
                }
            }
        }
    }

    /**
     * 测试家具放置功能
     */
    private async testFurniturePlacement() {
        console.log("--- 测试家具放置功能 ---");
        
        if (!this.furnitureManager) return;

        // 获取家具模板进行测试
        const templates = this.furnitureManager.getFurnitureTemplatesMap();
        const templateIds = Array.from(templates.keys());
        
        if (templateIds.length === 0) {
            console.log("没有可用的家具模板");
            return;
        }

        const testTemplateId = templateIds[0];
        console.log(`使用家具模板ID: ${testTemplateId}`);

        // 在房间A放置家具
        const placeResult1 = await this.furnitureManager.placeFurnitureInRoom(
            "room_a", 
            testTemplateId, 
            new Vec2(2, 2), 
            Rotation.Deg0
        );
        console.log(`在房间A放置家具: ${placeResult1.success ? "成功" : "失败"}`);
        if (!placeResult1.success) {
            console.log(`错误信息: ${placeResult1.errorMessage}`);
        }

        // 在房间B放置家具
        const placeResult2 = await this.furnitureManager.placeFurnitureInRoom(
            "room_b", 
            testTemplateId, 
            new Vec2(1, 1), 
            Rotation.Deg0
        );
        console.log(`在房间B放置家具: ${placeResult2.success ? "成功" : "失败"}`);
        if (!placeResult2.success) {
            console.log(`错误信息: ${placeResult2.errorMessage}`);
        }

        // 测试在无效位置放置家具
        const placeResult3 = await this.furnitureManager.placeFurnitureInRoom(
            "room_a", 
            testTemplateId, 
            new Vec2(0, 0), // 可能是障碍位置
            Rotation.Deg0
        );
        console.log(`在障碍位置放置家具: ${placeResult3.success ? "成功" : "失败"}`);
        if (!placeResult3.success) {
            console.log(`预期的错误信息: ${placeResult3.errorMessage}`);
        }
    }

    /**
     * 测试房间管理功能
     */
    private async testRoomManagement() {
        console.log("--- 测试房间管理功能 ---");
        
        if (!this.furnitureManager) return;

        // 获取所有房间的家具列表
        const allRoomsFurnitures = this.furnitureManager.getAllRoomsFurnitures();
        console.log("所有房间家具统计:");
        
        for (const [roomId, furnitures] of Object.entries(allRoomsFurnitures)) {
            console.log(`- 房间 ${roomId}: ${furnitures.length} 个家具`);
        }

        // 测试清空房间功能
        const clearResult = this.furnitureManager.clearRoom("room_a");
        console.log(`清空房间A: ${clearResult ? "成功" : "失败"}`);

        // 再次检查房间家具数量
        const furnituresAfterClear = this.furnitureManager.getAllRoomsFurnitures();
        const roomAFurnitures = furnituresAfterClear["room_a"] || [];
        console.log(`清空后房间A家具数量: ${roomAFurnitures.length}`);
    }

    /**
     * 测试评分计算功能
     */
    private async testScoreCalculation() {
        console.log("--- 测试评分计算功能 ---");
        
        if (!this.furnitureManager) return;

        // 计算当前双房间系统的评分
        const score = this.furnitureManager.calculateMultiRoomScore();
        
        if (score) {
            console.log("双房间系统评分:");
            console.log(`- 主题得分: ${score.themeScore}`);
            console.log(`- 数量得分: ${score.quantityScore}`);
            console.log(`- 价值得分: ${score.valueScore}`);
            console.log(`- 布局得分: ${score.layoutScore}`);
            if (score.adjacentScore !== undefined) {
                console.log(`- 相邻得分: ${score.adjacentScore}`);
            }
            console.log(`- 总分: ${score.totalScore}`);
            console.log(`- 主导主题: ${score.dominantTheme || "无"}`);
        } else {
            console.log("无法计算评分");
        }
    }

    /**
     * 测试错误处理
     */
    private async testErrorHandling() {
        console.log("--- 测试错误处理 ---");
        
        if (!this.furnitureManager) return;

        // 测试在不存在的房间放置家具
        const invalidRoomResult = await this.furnitureManager.placeFurnitureInRoom(
            "invalid_room", 
            1, 
            new Vec2(1, 1)
        );
        console.log(`在无效房间放置家具: ${invalidRoomResult.success ? "成功" : "失败"}`);
        console.log(`错误信息: ${invalidRoomResult.errorMessage}`);

        // 测试使用无效的家具模板ID
        const invalidTemplateResult = await this.furnitureManager.placeFurnitureInRoom(
            "room_a", 
            99999, // 不存在的模板ID
            new Vec2(1, 1)
        );
        console.log(`使用无效模板ID: ${invalidTemplateResult.success ? "成功" : "失败"}`);
        console.log(`错误信息: ${invalidTemplateResult.errorMessage}`);

        // 测试获取不存在房间的信息
        const invalidRoomInfo = this.furnitureManager.getRoomInfo("invalid_room");
        console.log(`获取无效房间信息: ${invalidRoomInfo ? "成功" : "失败（预期）"}`);
    }

    /**
     * 手动触发完整测试
     */
    public async runFullTest() {
        await this.start();
        await this.testErrorHandling();
    }

    /**
     * 清理测试数据
     */
    public cleanupTestData() {
        if (!this.furnitureManager) return;

        console.log("清理测试数据...");
        
        // 清空所有房间
        const roomIds = ["room_a", "room_b"];
        for (const roomId of roomIds) {
            const cleared = this.furnitureManager.clearRoom(roomId);
            console.log(`清空房间 ${roomId}: ${cleared ? "成功" : "失败"}`);
        }
    }
}
