# 家具摆放系统使用指南

## 系统概述
家具摆放系统允许玩家在房间内放置、移动、旋转和移除各种家具，每种家具都有不同的属性加成效果。

## 核心组件

### 1. DecorationMgr - 装饰管理器
主要接口，提供所有家具操作功能。

### 2. FurnitureManager - 家具管理器
管理家具模板和已放置的家具实例。

### 3. RoomGrid - 房间格子管理器
管理房间的格子状态和占用情况。

### 4. PlacementValidator - 摆放验证器
验证家具摆放的合法性。

## 使用方法

### 初始化系统
```typescript
// 在LogicMgr或其他管理器中初始化
const decorationMgr = DecorationMgr.getInstance();
await decorationMgr.init();
```

### 基本操作

#### 1. 获取家具模板
```typescript
// 获取所有家具模板
const templates = decorationMgr.getAllFurnitureTemplates();

// 获取特定家具模板
const chairTemplate = decorationMgr.getFurnitureTemplate(1);
```

#### 2. 放置家具
```typescript
import { Vec2 } from "cc";
import { Rotation } from "./decoration/DecorationDefine";

// 在位置(3,4)放置椅子
const result = decorationMgr.placeFurniture(1, new Vec2(3, 4), Rotation.Deg0);
if (result.success) {
    console.log("家具放置成功");
} else {
    console.log("放置失败:", result.errorMessage);
}
```

#### 3. 移动家具
```typescript
// 移动家具到新位置
const moveResult = decorationMgr.moveFurniture("furniture_id", new Vec2(5, 6));
```

#### 4. 旋转家具
```typescript
// 旋转家具90度
const rotateResult = decorationMgr.rotateFurniture("furniture_id", Rotation.Deg90);
```

#### 5. 移除家具
```typescript
// 移除指定家具
const removed = decorationMgr.removeFurniture("furniture_id");
```

### 获取效果和状态

#### 1. 获取装饰效果
```typescript
import { EffectType } from "./decoration/DecorationDefine";

// 获取收益加成
const incomeBonus = decorationMgr.getEffectValue(EffectType.IncomeBonus);

// 获取所有效果
const allEffects = decorationMgr.getAllEffects();
```

#### 2. 获取房间状态
```typescript
// 获取已放置的家具
const placedFurnitures = decorationMgr.getPlacedFurnitures();

// 获取装饰评分
const score = decorationMgr.getDecorationScore();

// 获取房间使用率
const usageRate = decorationMgr.getRoomUsageRate();
```

## 事件系统

系统会发送以下事件：

```typescript
import { DecorationEventTag } from "./decoration/DecorationMgr";

// 监听家具放置事件
EventTargetMgr.instance.addListener(DecorationEventTag.FurniturePlaced, 1, 
    (templateId: number, position: Vec2, rotation: Rotation) => {
        console.log("家具已放置:", templateId, position, rotation);
    }, this);

// 监听效果变化事件
EventTargetMgr.instance.addListener(DecorationEventTag.EffectsChanged, 1,
    (effects: Map<EffectType, number>) => {
        console.log("装饰效果已更新:", effects);
    }, this);
```

## 房间布局

### 默认房间布局
```
[1,1,1,0,0,0,0,1,1,1]
[1,1,0,0,0,0,0,0,1,1]
[1,0,0,0,0,0,0,0,0,1]
[0,0,0,0,0,0,0,0,0,0]
[0,0,0,0,0,0,0,0,0,0]
[0,0,0,0,0,0,0,0,0,0]
[0,0,0,0,0,0,0,0,0,0]
[1,0,0,0,0,0,0,0,0,1]
[1,1,0,0,0,0,0,0,1,1]
[1,1,1,0,0,0,0,1,1,1]
```

- 0: 可摆放区域
- 1: 障碍物（墙壁等）

## 家具类型

### 1×1 小型家具
- 椅子：收益加成 +5
- 台灯：经验加成 +3
- 花盆：生命值加成 +4

### 2×1 中型家具
- 床：生命值加成 +10
- 沙发：收益加成 +8，生命值加成 +5
- 工作台：攻击力加成 +10，收益加成 +6

### 2×2 大型家具
- 桌子：攻击力加成 +8
- 书柜：经验加成 +12，攻击力加成 +6
- 衣柜：全属性加成 +3
- 钢琴：收益加成 +15

## 测试示例

```typescript
// 测试基本功能
async function testFurnitureSystem() {
    const decorationMgr = DecorationMgr.getInstance();
    await decorationMgr.init();
    
    // 放置一些家具
    console.log("=== 测试家具放置 ===");
    
    // 放置椅子
    let result = decorationMgr.placeFurniture(1, new Vec2(3, 3), Rotation.Deg0);
    console.log("放置椅子:", result.success ? "成功" : result.errorMessage);
    
    // 放置床
    result = decorationMgr.placeFurniture(2, new Vec2(5, 3), Rotation.Deg0);
    console.log("放置床:", result.success ? "成功" : result.errorMessage);
    
    // 放置桌子
    result = decorationMgr.placeFurniture(3, new Vec2(3, 5), Rotation.Deg0);
    console.log("放置桌子:", result.success ? "成功" : result.errorMessage);
    
    // 检查效果
    console.log("=== 当前装饰效果 ===");
    const effects = decorationMgr.getAllEffects();
    effects.forEach((value, type) => {
        console.log(`${decorationMgr.getEffectTypeName(type)}: +${value}`);
    });
    
    console.log("装饰评分:", decorationMgr.getDecorationScore());
    console.log("房间使用率:", (decorationMgr.getRoomUsageRate() * 100).toFixed(1) + "%");
}

// 在适当的时候调用测试
testFurnitureSystem();
```

## 集成到现有系统

### 1. 添加到LogicMgr的加载器列表
```typescript
// 在logicMgr.ts中添加
import { DecorationMgr } from "./decoration/DecorationMgr";

// 在dataLoaders数组中添加
const dataLoaders: IDataLoader[] = [
    // ... 其他加载器
    DecorationMgr.getInstance().loader,
];
```

### 2. 集成到EffectMgr
可以将装饰效果集成到现有的EffectMgr系统中，让装饰效果影响游戏的核心数值。

### 3. 创建UI界面
创建专门的装饰UI界面，允许玩家拖拽放置家具、查看效果等。

## 扩展功能

1. **家具解锁系统**：通过游戏进度解锁新家具
2. **家具升级系统**：升级家具提升效果
3. **房间扩展**：解锁更大的房间
4. **主题套装**：特定家具组合提供额外奖励
5. **访客系统**：其他玩家可以参观房间
