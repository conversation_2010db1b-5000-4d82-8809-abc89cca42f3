{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f5b2a0ef-38d2-45a6-8865-499daa8909f6", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f5b2a0ef-38d2-45a6-8865-499daa8909f6@6c48a", "displayName": "合成用-穿云箭2", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f5b2a0ef-38d2-45a6-8865-499daa8909f6", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f5b2a0ef-38d2-45a6-8865-499daa8909f6@f9941", "displayName": "合成用-穿云箭2", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 96, "height": 426, "rawWidth": 96, "rawHeight": 426, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-48, -213, 0, 48, -213, 0, -48, 213, 0, 48, 213, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 426, 96, 426, 0, 0, 96, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-48, -213, 0], "maxPos": [48, 213, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f5b2a0ef-38d2-45a6-8865-499daa8909f6@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f5b2a0ef-38d2-45a6-8865-499daa8909f6@6c48a"}}