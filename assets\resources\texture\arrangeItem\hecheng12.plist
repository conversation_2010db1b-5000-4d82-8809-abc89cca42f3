<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-穿云箭.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,426}</string>
                <key>spriteSourceSize</key>
                <string>{96,426}</string>
                <key>textureRect</key>
                <string>{{1,1},{96,426}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-穿云箭2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,426}</string>
                <key>spriteSourceSize</key>
                <string>{96,426}</string>
                <key>textureRect</key>
                <string>{{99,1},{96,426}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-穿云箭3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{96,426}</string>
                <key>spriteSourceSize</key>
                <string>{96,426}</string>
                <key>textureRect</key>
                <string>{{197,1},{96,426}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-穿云箭4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,422}</string>
                <key>spriteSourceSize</key>
                <string>{107,422}</string>
                <key>textureRect</key>
                <string>{{295,1},{107,422}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-穿云箭5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,414}</string>
                <key>spriteSourceSize</key>
                <string>{107,414}</string>
                <key>textureRect</key>
                <string>{{404,1},{107,414}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng12.png</string>
            <key>size</key>
            <string>{512,428}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:cef9ee92bdadc2fdcef57dc25d6029ab:aff7901f88353e7d4608e399a388baf7:2b143f869823faab8b5a5b70e3325b86$</string>
            <key>textureFileName</key>
            <string>hecheng12.png</string>
        </dict>
    </dict>
</plist>
