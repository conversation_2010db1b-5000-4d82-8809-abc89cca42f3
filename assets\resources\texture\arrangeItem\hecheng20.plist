<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-勋章.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{98,140}</string>
                <key>spriteSourceSize</key>
                <string>{98,140}</string>
                <key>textureRect</key>
                <string>{{1,1},{98,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-勋章2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{98,140}</string>
                <key>spriteSourceSize</key>
                <string>{98,140}</string>
                <key>textureRect</key>
                <string>{{1,143},{98,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-勋章3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{98,140}</string>
                <key>spriteSourceSize</key>
                <string>{98,140}</string>
                <key>textureRect</key>
                <string>{{1,285},{98,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-勋章4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{98,140}</string>
                <key>spriteSourceSize</key>
                <string>{98,140}</string>
                <key>textureRect</key>
                <string>{{1,427},{98,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-勋章5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{98,140}</string>
                <key>spriteSourceSize</key>
                <string>{98,140}</string>
                <key>textureRect</key>
                <string>{{1,569},{98,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng20.png</string>
            <key>size</key>
            <string>{100,710}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:4f91dfb9603c90365b58ee6257369dff:ec6a50e16037faaac72a0b2cc0de4ee4:6bae1273bcafc75bddeb6489a2b17ed7$</string>
            <key>textureFileName</key>
            <string>hecheng20.png</string>
        </dict>
    </dict>
</plist>
