# 分阶段雪人激活系统设计文档

## 需求概述
实现关卡进度控制的雪人分阶段激活功能：
- Stage 1 Round 1: 只有第1个雪人生效
- Stage 1 Round 2: 激活第2个雪人  
- Stage 2 Round 1: 激活第3个雪人

## 技术方案

### 1. 配置数据结构

```typescript
// 卡牌激活规则
interface CardActivationRule {
  stageId: number;    // 关卡ID
  round: number;      // 回合数
  cardId: number;     // 要激活的卡牌ID
}

// 卡牌激活配置
interface CardActivationConfig {
  rules: CardActivationRule[];
}
```

### 2. 配置文件示例

```json
{
  "rules": [
    {
      "stageId": 1,
      "round": 2,
      "cardId": 2
    },
    {
      "stageId": 2,
      "round": 1,
      "cardId": 3
    }
  ]
}
```

### 3. 实现方案

#### 3.1 扩展StageDataMgr
- 添加卡牌激活配置加载
- 在回合切换时检查并触发激活
- 提供激活规则查询方法

#### 3.2 激活时机
选择在`StageDataMgr.completeRound()`中触发激活：
- 回合完成后立即检查下一回合的激活规则
- 通过`EventTargetMgr.sendEvent(EventTag.CardActivated, cardId)`触发激活

#### 3.3 初始化逻辑
在`StageDataMgr.startGame()`或`newStageProgress()`中：
- 重置所有卡牌为不激活状态
- 根据当前关卡回合激活应该生效的卡牌

### 4. 代码修改点

#### 4.1 StageDataMgr扩展
```typescript
class StageDataMgr {
  private cardActivationConfig: CardActivationConfig | null = null;
  
  // 加载卡牌激活配置
  private async loadCardActivationConfig() {
    // 从JSON文件加载配置
  }
  
  // 检查并激活卡牌
  private checkAndActivateCards(stageId: number, round: number) {
    // 查找匹配的激活规则并触发激活
  }
  
  // 在completeRound中调用
  completeRound() {
    // 现有逻辑...
    if (round < this.curStageConfig.round.length) {
      const nextRound = round + 1;
      this.checkAndActivateCards(this.curStageId, nextRound);
    }
  }
}
```

#### 4.2 CardMgr扩展
```typescript
class CardMgr {
  // 重置卡牌激活状态
  resetCardActivation(stageId: number, round: number) {
    // 根据关卡回合重置inactiveCards
  }
}
```

### 5. 数据流程

1. **游戏开始**: 
   - 加载卡牌激活配置
   - 重置所有卡牌状态
   - 激活当前关卡回合应该生效的卡牌

2. **回合切换**:
   - 检查下一回合的激活规则
   - 发送CardActivated事件
   - 各系统响应激活事件

3. **系统响应**:
   - MinerManager: 激活对应雪人
   - EntryMgr: 添加装备类型到词条池
   - arrangeUI: 显示新激活的卡牌头像

### 6. 配置文件位置
建议放置在: `assets/resources/json/cardActivationConfig.json`

### 7. 向后兼容
- 如果没有配置文件，使用默认行为（所有装备卡牌都生效）
- 配置文件可以为空，表示不使用分阶段激活

### 8. 扩展性考虑
- 支持同一回合激活多个卡牌
- 支持基于其他条件的激活（如深度、得分等）
- 支持卡牌去激活（如果需要）

## 实现优先级
1. 基础配置系统和数据结构
2. StageDataMgr中的激活逻辑
3. 配置文件和默认规则
4. 测试和调试功能
