/**
 * 双房间系统测试脚本
 * 用于验证多房间功能的正确性
 */

import { Component, Vec2, _decorator } from "cc";
import { DecorationMgr } from "../logic/DecorationMgr";
import { DefaultRoomTemplates } from "../logic/DefaultRoomTemplates";
import { MultiRoomManager } from "../logic/MultiRoomManager";
import { Rotation, FurnitureTheme } from "../logic/DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('MultiRoomTest')
export class MultiRoomTest extends Component {
    
    async start() {
        console.log("=== 双房间系统测试开始 ===");
        
        try {
            await this.testDefaultRoomTemplates();
            await this.testMultiRoomManager();
            await this.testDecorationMgrIntegration();
            
            console.log("=== 双房间系统测试完成 ===");
        } catch (error) {
            console.error("双房间系统测试失败:", error);
        }
    }

    /**
     * 测试默认房间模板
     */
    private async testDefaultRoomTemplates() {
        console.log("--- 测试默认房间模板 ---");
        
        // 测试获取所有模板
        const allTemplates = DefaultRoomTemplates.getAllTemplates();
        console.log(`获取到 ${allTemplates.length} 个房间模板`);
        
        // 测试房间A模板
        const roomA = DefaultRoomTemplates.getRoomATemplate();
        console.log(`房间A: ${roomA.name}, 尺寸: ${roomA.size.x}×${roomA.size.y}`);
        console.log(`房间A布局验证:`, DefaultRoomTemplates.validateRoomLayout(roomA.layout, roomA.size));
        console.log(`房间A可用空间:`, DefaultRoomTemplates.calculateAvailableSpace(roomA.layout));
        console.log(`房间A邻墙格子:`, DefaultRoomTemplates.calculateWallAdjacentSpace(roomA.layout));
        
        // 测试房间B模板
        const roomB = DefaultRoomTemplates.getRoomBTemplate();
        console.log(`房间B: ${roomB.name}, 尺寸: ${roomB.size.x}×${roomB.size.y}`);
        console.log(`房间B布局验证:`, DefaultRoomTemplates.validateRoomLayout(roomB.layout, roomB.size));
        console.log(`房间B可用空间:`, DefaultRoomTemplates.calculateAvailableSpace(roomB.layout));
        console.log(`房间B邻墙格子:`, DefaultRoomTemplates.calculateWallAdjacentSpace(roomB.layout));
        
        // 测试推荐双房间配置
        const dualSetup = DefaultRoomTemplates.getRecommendedDualRoomSetup();
        console.log(`推荐双房间配置: ${dualSetup.roomA.name} + ${dualSetup.roomB.name}`);
        
        // 测试根据ID获取模板
        const templateById = DefaultRoomTemplates.getTemplateById("room_a");
        console.log(`根据ID获取模板:`, templateById ? templateById.name : "未找到");
    }

    /**
     * 测试多房间管理器
     */
    private async testMultiRoomManager() {
        console.log("--- 测试多房间管理器 ---");
        
        const multiRoomMgr = MultiRoomManager.getInstance();
        
        // 创建模拟的家具模板
        const mockTemplates = this.createMockFurnitureTemplates();
        
        // 初始化多房间管理器
        await multiRoomMgr.init(mockTemplates);
        console.log("多房间管理器初始化完成");
        
        // 获取所有房间ID
        const roomIds = multiRoomMgr.getAllRoomIds();
        console.log(`房间数量: ${roomIds.length}, 房间ID: [${roomIds.join(', ')}]`);
        
        // 测试在房间A放置家具
        const placeResult1 = multiRoomMgr.placeFurniture("room_a", 1, new Vec2(2, 2));
        console.log(`在房间A放置家具1:`, placeResult1.success ? "成功" : placeResult1.errorMessage);
        
        // 测试在房间B放置家具
        const placeResult2 = multiRoomMgr.placeFurniture("room_b", 2, new Vec2(1, 1));
        console.log(`在房间B放置家具2:`, placeResult2.success ? "成功" : placeResult2.errorMessage);
        
        // 测试在无效位置放置家具
        const placeResult3 = multiRoomMgr.placeFurniture("room_a", 3, new Vec2(0, 0)); // 障碍位置
        console.log(`在障碍位置放置家具:`, placeResult3.success ? "成功" : placeResult3.errorMessage);
        
        // 获取房间家具
        const roomAFurnitures = multiRoomMgr.getRoomFurnitures("room_a");
        const roomBFurnitures = multiRoomMgr.getRoomFurnitures("room_b");
        console.log(`房间A家具数量: ${roomAFurnitures.length}`);
        console.log(`房间B家具数量: ${roomBFurnitures.length}`);
        
        // 计算评分
        const score = multiRoomMgr.calculateScore();
        console.log(`当前评分: ${score.totalScore} (主题:${score.themeScore}, 数量:${score.quantityScore}, 价值:${score.valueScore}, 布局:${score.layoutScore})`);
        
        // 测试清空房间
        const clearResult = multiRoomMgr.clearRoom("room_a");
        console.log(`清空房间A:`, clearResult ? "成功" : "失败");
        
        const roomAFurnituresAfterClear = multiRoomMgr.getRoomFurnitures("room_a");
        console.log(`清空后房间A家具数量: ${roomAFurnituresAfterClear.length}`);
    }

    /**
     * 测试装饰管理器集成
     */
    private async testDecorationMgrIntegration() {
        console.log("--- 测试装饰管理器集成 ---");
        
        const decorationMgr = DecorationMgr.getInstance();
        
        // 检查初始状态
        console.log(`多房间模式状态: ${decorationMgr.isMultiRoomMode() ? "启用" : "禁用"}`);
        
        // 启用多房间模式
        await decorationMgr.enableMultiRoomMode();
        console.log(`启用多房间模式后: ${decorationMgr.isMultiRoomMode() ? "启用" : "禁用"}`);
        
        // 获取房间列表
        const roomIds = decorationMgr.getAllRoomIds();
        console.log(`可用房间: [${roomIds.join(', ')}]`);
        
        // 在多房间模式下放置家具
        const placeResult = decorationMgr.placeFurnitureInRoom("room_b", 1, new Vec2(2, 2));
        console.log(`多房间模式放置家具:`, placeResult.success ? "成功" : placeResult.errorMessage);
        
        // 获取评分
        const score = decorationMgr.getRoomScoreDetails();
        console.log(`多房间模式评分: ${score ? score.totalScore : "无评分"}`);
        
        // 测试禁用多房间模式
        decorationMgr.disableMultiRoomMode();
        console.log(`禁用多房间模式后: ${decorationMgr.isMultiRoomMode() ? "启用" : "禁用"}`);
    }

    /**
     * 创建模拟家具模板
     */
    private createMockFurnitureTemplates() {
        const templates = new Map();
        
        // 小型家具
        templates.set(1, {
            id: 1,
            name: "测试椅子",
            baseSize: new Vec2(1, 1),
            properties: {
                theme: FurnitureTheme.Modern,
                level: 1,
                value: 10,
                beauty: 5,
                isWallDecoration: false
            }
        });
        
        // 中型家具
        templates.set(2, {
            id: 2,
            name: "测试桌子",
            baseSize: new Vec2(2, 1),
            properties: {
                theme: FurnitureTheme.Modern,
                level: 2,
                value: 20,
                beauty: 8,
                isWallDecoration: false
            }
        });
        
        // 大型家具
        templates.set(3, {
            id: 3,
            name: "测试沙发",
            baseSize: new Vec2(2, 2),
            properties: {
                theme: FurnitureTheme.Classic,
                level: 3,
                value: 30,
                beauty: 12,
                isWallDecoration: false
            }
        });
        
        // 挂饰类家具
        templates.set(4, {
            id: 4,
            name: "测试壁画",
            baseSize: new Vec2(1, 1),
            properties: {
                theme: FurnitureTheme.Classic,
                level: 2,
                value: 15,
                beauty: 10,
                isWallDecoration: true
            }
        });
        
        return templates;
    }

    /**
     * 打印房间布局（调试用）
     */
    private printRoomLayout(layout: number[][], roomName: string) {
        console.log(`${roomName} 布局:`);
        for (let y = 0; y < layout.length; y++) {
            let row = "";
            for (let x = 0; x < layout[y].length; x++) {
                const cell = layout[y][x];
                row += cell === 0 ? "·" : (cell === 1 ? "○" : "█");
            }
            console.log(row);
        }
    }
}
