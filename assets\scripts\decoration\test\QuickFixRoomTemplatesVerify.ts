/**
 * 快速验证 fixRoomTemplates 方法
 * 简化版本的验证脚本，专注于核心功能验证
 */

import { Component, Vec2, _decorator } from "cc";
import { DefaultRoomTemplates } from "../logic/DefaultRoomTemplates";
import { RoomTemplate } from "../logic/DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('QuickFixRoomTemplatesVerify')
export class QuickFixRoomTemplatesVerify extends Component {
    
    start() {
        console.log("=== 快速验证 fixRoomTemplates 方法 ===");
        
        this.verifyFixRoomTemplatesMethod();
        
        console.log("=== 快速验证完成 ===");
    }

    /**
     * 验证 fixRoomTemplates 方法的核心功能
     */
    private verifyFixRoomTemplatesMethod() {
        console.log("验证 fixRoomTemplates 方法核心功能...");

        // 测试用例1: 基本边界处理
        const testCase1: RoomTemplate = {
            id: "test1",
            name: "基本测试",
            size: new Vec2(4, 4),
            layout: [
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [0, 0, 0, 0]
            ]
        };

        console.log("\n测试用例1: 基本边界处理");
        console.log("原始布局:");
        this.printLayout(testCase1.layout);
        
        const fixed1 = DefaultRoomTemplates.fixRoomTemplates(JSON.parse(JSON.stringify(testCase1)));
        console.log("修正后布局:");
        this.printLayout(fixed1.layout);
        
        const result1 = this.validateBoundaries(fixed1.layout);
        console.log(`边界处理验证: ${result1 ? "✅ 通过" : "❌ 失败"}`);

        // 测试用例2: 障碍物邻接处理
        const testCase2: RoomTemplate = {
            id: "test2",
            name: "障碍物测试",
            size: new Vec2(5, 5),
            layout: [
                [1, 1, 1, 1, 1],
                [1, 0, 2, 0, 1],
                [1, 0, 2, 0, 1],
                [1, 0, 0, 0, 1],
                [1, 1, 1, 1, 1]
            ]
        };

        console.log("\n测试用例2: 障碍物邻接处理");
        console.log("原始布局:");
        this.printLayout(testCase2.layout);
        
        const fixed2 = DefaultRoomTemplates.fixRoomTemplates(JSON.parse(JSON.stringify(testCase2)));
        console.log("修正后布局:");
        this.printLayout(fixed2.layout);
        
        const result2 = this.validateObstacleAdjacent(fixed2.layout);
        console.log(`障碍物邻接验证: ${result2 ? "✅ 通过" : "❌ 失败"}`);

        // 测试用例3: 混合情况
        const testCase3: RoomTemplate = {
            id: "test3",
            name: "混合测试",
            size: new Vec2(6, 6),
            layout: [
                [0, 0, 0, 0, 0, 0],
                [0, 0, 2, 2, 0, 0],
                [0, 2, 2, 2, 2, 0],
                [0, 2, 0, 0, 2, 0],
                [0, 0, 2, 2, 0, 0],
                [0, 0, 0, 0, 0, 0]
            ]
        };

        console.log("\n测试用例3: 混合情况");
        console.log("原始布局:");
        this.printLayout(testCase3.layout);
        
        const fixed3 = DefaultRoomTemplates.fixRoomTemplates(JSON.parse(JSON.stringify(testCase3)));
        console.log("修正后布局:");
        this.printLayout(fixed3.layout);
        
        const result3a = this.validateBoundaries(fixed3.layout);
        const result3b = this.validateObstacleAdjacent(fixed3.layout);
        console.log(`混合情况验证: ${result3a && result3b ? "✅ 通过" : "❌ 失败"}`);

        // 验证所有默认模板
        console.log("\n验证所有默认模板:");
        const allTemplates = DefaultRoomTemplates.getAllTemplates();
        let allValid = true;

        allTemplates.forEach((template, index) => {
            const boundaryValid = this.validateBoundaries(template.layout);
            const obstacleValid = this.validateObstacleAdjacent(template.layout);
            const isValid = boundaryValid && obstacleValid;
            
            console.log(`模板 ${index + 1} (${template.name}): ${isValid ? "✅ 有效" : "❌ 无效"}`);
            
            if (!isValid) {
                allValid = false;
                console.log(`  - 边界验证: ${boundaryValid ? "通过" : "失败"}`);
                console.log(`  - 障碍物邻接验证: ${obstacleValid ? "通过" : "失败"}`);
            }
        });

        console.log(`\n总体验证结果: ${allValid ? "✅ 所有模板都有效" : "❌ 存在无效模板"}`);
    }

    /**
     * 验证边界是否正确处理
     */
    private validateBoundaries(layout: number[][]): boolean {
        const height = layout.length;
        const width = layout[0].length;

        // 检查上下边界
        for (let x = 0; x < width; x++) {
            if (layout[0][x] !== 2 && layout[0][x] !== 1) {
                console.error(`边界错误: 上边界 (0,${x}) = ${layout[0][x]}, 应该是1或2`);
                return false;
            }
            if (layout[height - 1][x] !== 2 && layout[height - 1][x] !== 1) {
                console.error(`边界错误: 下边界 (${height-1},${x}) = ${layout[height-1][x]}, 应该是1或2`);
                return false;
            }
        }

        // 检查左右边界
        for (let y = 0; y < height; y++) {
            if (layout[y][0] !== 2 && layout[y][0] !== 1) {
                console.error(`边界错误: 左边界 (${y},0) = ${layout[y][0]}, 应该是1或2`);
                return false;
            }
            if (layout[y][width - 1] !== 2 && layout[y][width - 1] !== 1) {
                console.error(`边界错误: 右边界 (${y},${width-1}) = ${layout[y][width-1]}, 应该是1或2`);
                return false;
            }
        }

        return true;
    }

    /**
     * 验证障碍物邻接是否正确处理
     */
    private validateObstacleAdjacent(layout: number[][]): boolean {
        const height = layout.length;
        const width = layout[0].length;

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (layout[y][x] === 0) {
                    // 检查四个方向是否有障碍物
                    const hasObstacleAdjacent = 
                        layout[y][x + 1] === 2 ||
                        layout[y][x - 1] === 2 ||
                        layout[y + 1][x] === 2 ||
                        layout[y - 1][x] === 2;

                    if (hasObstacleAdjacent) {
                        console.error(`障碍物邻接错误: (${y},${x}) 邻接障碍物但仍为0，应该是1`);
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 打印布局
     */
    private printLayout(layout: number[][]) {
        for (let y = 0; y < layout.length; y++) {
            let row = "  ";
            for (let x = 0; x < layout[y].length; x++) {
                const cell = layout[y][x];
                row += cell === 0 ? "·" : (cell === 1 ? "○" : "█");
            }
            console.log(row);
        }
        console.log("  (· = 空地, ○ = 邻墙, █ = 障碍)");
    }

    /**
     * 手动触发验证
     */
    public runQuickVerify() {
        this.start();
    }
}
