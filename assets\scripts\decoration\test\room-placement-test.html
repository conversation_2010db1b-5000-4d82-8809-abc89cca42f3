<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间摆放系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            padding-bottom: 160px; /* 为底部信息面板留出空间 */
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .info-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 120px;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .info-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .info-content {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .room-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .room {
            border: 2px solid #333;
            background: #fff;
            position: relative;
        }
        
        .room-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .grid {
            display: grid;
            gap: 1px;
            background: #ccc;
            border: 1px solid #999;
        }
        
        .grid-cell {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .grid-cell:hover {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        /* 格子状态样式 */
        .grid-empty {
            background-color: #f8f9fa;
        }
        
        .grid-wall-adjacent {
            background-color: #e9ecef;
            border-color: #6c757d;
        }
        
        .grid-obstacle {
            background-color: #343a40;
        }
        
        .grid-occupied {
            background-color: #28a745;
        }
        
        /* 家具样式 */
        .furniture {
            position: absolute;
            border: 2px solid #007bff;
            background: rgba(0,123,255,0.3);
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #004085;
            z-index: 10;
        }
        
        .furniture:hover {
            background: rgba(0,123,255,0.5);
            border-color: #0056b3;
            box-shadow: 0 2px 8px rgba(0,123,255,0.4);
            transform: scale(1.05);
        }
        
        .furniture.wall-decoration {
            background: rgba(220,53,69,0.3);
            border-color: #dc3545;
            color: #721c24;
        }
        
        .furniture.wall-decoration:hover {
            background: rgba(220,53,69,0.5);
            border-color: #c82333;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #545b62;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border: 1px solid #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>房间摆放系统测试</h1>
            <p>鼠标悬停在格子或家具上查看详细信息</p>
        </div>
        
        <div class="info-panel">
            <div class="info-title">信息面板</div>
            <div class="info-content" id="infoContent">
                将鼠标悬停在房间格子或家具上查看详细信息...
            </div>
        </div>
        
        <div class="room-container">
            <div>
                <div class="room-title">房间A (10×10)</div>
                <div class="room" id="roomA">
                    <div class="grid" id="gridA" style="grid-template-columns: repeat(10, 30px);"></div>
                </div>
            </div>
            
            <div>
                <div class="room-title">房间B (6×6)</div>
                <div class="room" id="roomB">
                    <div class="grid" id="gridB" style="grid-template-columns: repeat(6, 30px);"></div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color grid-empty"></div>
                <span>空地 (0)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-wall-adjacent"></div>
                <span>邻墙 (1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-obstacle"></div>
                <span>障碍 (2)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-occupied"></div>
                <span>占用 (3)</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="addRandomFurniture()">添加随机家具</button>
            <button class="btn secondary" onclick="clearAllFurniture()">清空家具</button>
            <button class="btn secondary" onclick="resetRooms()">重置房间</button>
        </div>
    </div>

    <script>
        // 房间摆放系统测试脚本
        // 模拟房间摆放系统的核心数据结构和功能
        
        // 枚举定义
        const GridState = {
            Empty: 0,          // 空地
            WallAdjacent: 1,   // 邻墙
            Obstacle: 2,       // 障碍
            Occupied: 3        // 占用
        };
        
        const FurnitureType = {
            Small: 1,           // 1×1 小型家具
            Medium: 2,          // 2×1 中型家具  
            Large: 3,           // 2×2 大型家具
            WallDecoration: 4   // 挂饰类家具
        };
        
        const FurnitureTheme = {
            Modern: 1,      // 现代风格
            Classic: 2,     // 古典风格
            Natural: 3,     // 自然风格
            Industrial: 4,  // 工业风格
            Minimalist: 5   // 简约风格
        };
        
        const Rotation = {
            Deg0: 0,
            Deg90: 90,
            Deg180: 180,
            Deg270: 270
        };
        
        // 房间配置
        const roomConfigs = {
            roomA: {
                id: 'roomA',
                name: '房间A',
                size: { x: 10, y: 10 },
                layout: [
                    [2,2,2,1,1,1,1,2,2,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,1,0,0,0,0,0,0,1,2],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [2,1,0,0,0,0,0,0,1,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,2,2,1,1,1,1,2,2,2]
                ],
                furnitures: []
            },
            roomB: {
                id: 'roomB',
                name: '房间B',
                size: { x: 6, y: 6 },
                layout: [
                    [2,2,1,1,2,2],
                    [2,1,0,0,1,2],
                    [1,0,0,0,0,1],
                    [1,0,0,0,0,1],
                    [2,1,0,0,1,2],
                    [2,2,1,1,2,2]
                ],
                furnitures: []
            }
        };
        
        // 家具模板
        const furnitureTemplates = [
            {
                id: 1,
                name: '小桌子',
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 10,
                    beauty: 5,
                    isWallDecoration: false
                }
            },
            {
                id: 2,
                name: '书架',
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 25,
                    beauty: 8,
                    isWallDecoration: false
                }
            },
            {
                id: 3,
                name: '大沙发',
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 3,
                    value: 50,
                    beauty: 12,
                    isWallDecoration: false
                }
            },
            {
                id: 4,
                name: '壁画',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 20,
                    beauty: 10,
                    isWallDecoration: true
                }
            }
        ];
        
        // 全局变量
        let infoContent = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            infoContent = document.getElementById('infoContent');
            initializeRooms();
        });

        // 初始化房间
        function initializeRooms() {
            // 渲染房间（使用预定义的布局）
            renderRoom('roomA', roomConfigs.roomA);
            renderRoom('roomB', roomConfigs.roomB);
        }


        // 渲染房间
        function renderRoom(roomId, roomConfig) {
            const gridElement = document.getElementById(`grid${roomId.charAt(roomId.length - 1).toUpperCase()}`);
            gridElement.innerHTML = '';

            const { size, layout, furnitures } = roomConfig;

            // 创建格子
            for (let y = 0; y < size.y; y++) {
                for (let x = 0; x < size.x; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.dataset.roomId = roomId;

                    // 设置格子状态样式
                    const gridState = layout[y][x];
                    switch (gridState) {
                        case GridState.Empty:
                            cell.classList.add('grid-empty');
                            break;
                        case GridState.WallAdjacent:
                            cell.classList.add('grid-wall-adjacent');
                            break;
                        case GridState.Obstacle:
                            cell.classList.add('grid-obstacle');
                            break;
                        case GridState.Occupied:
                            cell.classList.add('grid-occupied');
                            break;
                    }

                    // 添加鼠标事件
                    cell.addEventListener('mouseenter', function() {
                        showGridInfo(roomConfig, x, y, gridState);
                    });

                    cell.addEventListener('mouseleave', function() {
                        clearInfo();
                    });

                    gridElement.appendChild(cell);
                }
            }

            // 渲染家具
            renderFurnitures(roomId, roomConfig);
        }

        // 渲染家具
        function renderFurnitures(roomId, roomConfig) {
            const roomElement = document.getElementById(roomId);

            // 清除现有家具元素
            const existingFurnitures = roomElement.querySelectorAll('.furniture');
            existingFurnitures.forEach(f => f.remove());

            // 渲染每个家具
            roomConfig.furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (!template) return;

                const furnitureElement = document.createElement('div');
                furnitureElement.className = 'furniture';
                furnitureElement.dataset.furnitureId = furniture.id;

                if (template.properties.isWallDecoration) {
                    furnitureElement.classList.add('wall-decoration');
                }

                // 计算位置和尺寸
                const cellSize = 30;
                const gap = 1;
                const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

                furnitureElement.style.left = `${furniture.position.x * (cellSize + gap) + gap}px`;
                furnitureElement.style.top = `${furniture.position.y * (cellSize + gap) + gap}px`;
                furnitureElement.style.width = `${currentSize.x * cellSize + (currentSize.x - 1) * gap}px`;
                furnitureElement.style.height = `${currentSize.y * cellSize + (currentSize.y - 1) * gap}px`;

                // 显示家具名称
                furnitureElement.textContent = template.name;

                // 添加鼠标事件
                furnitureElement.addEventListener('mouseenter', function(e) {
                    e.stopPropagation();
                    showFurnitureInfo(furniture, template);
                });

                furnitureElement.addEventListener('mouseleave', function() {
                    clearInfo();
                });

                roomElement.appendChild(furnitureElement);
            });
        }

        // 显示格子信息
        function showGridInfo(roomConfig, x, y, gridState) {
            const stateNames = {
                [GridState.Empty]: '空地',
                [GridState.WallAdjacent]: '邻墙格子',
                [GridState.Obstacle]: '障碍物',
                [GridState.Occupied]: '已占用'
            };

            const stateName = stateNames[gridState] || '未知';
            const stateColor = getGridStateColor(gridState);

            infoContent.innerHTML = `
                <div><strong>格子信息</strong></div>
                <div>房间: ${roomConfig.name}</div>
                <div>位置: (${x}, ${y})</div>
                <div>状态: <span style="color: ${stateColor}; font-weight: bold;">${stateName} (${gridState})</span></div>
                <div>描述: ${getGridStateDescription(gridState)}</div>
            `;
        }

        // 显示家具信息
        function showFurnitureInfo(furniture, template) {
            const themeNames = {
                [FurnitureTheme.Modern]: '现代风格',
                [FurnitureTheme.Classic]: '古典风格',
                [FurnitureTheme.Natural]: '自然风格',
                [FurnitureTheme.Industrial]: '工业风格',
                [FurnitureTheme.Minimalist]: '简约风格'
            };

            const typeNames = {
                [FurnitureType.Small]: '小型家具 (1×1)',
                [FurnitureType.Medium]: '中型家具 (2×1)',
                [FurnitureType.Large]: '大型家具 (2×2)',
                [FurnitureType.WallDecoration]: '挂饰类家具'
            };

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);
            const themeName = themeNames[template.properties.theme] || '未知主题';
            const typeName = typeNames[template.type] || '未知类型';

            infoContent.innerHTML = `
                <div><strong>家具信息</strong></div>
                <div>名称: <span style="color: #007bff; font-weight: bold;">${template.name}</span></div>
                <div>ID: ${furniture.id}</div>
                <div>位置: (${furniture.position.x}, ${furniture.position.y})</div>
                <div>旋转: ${furniture.rotation}°</div>
                <div>尺寸: ${currentSize.x}×${currentSize.y} (原始: ${template.baseSize.x}×${template.baseSize.y})</div>
                <div>类型: ${typeName}</div>
                <div>主题: ${themeName}</div>
                <div>等级: ${template.properties.level}</div>
                <div>价值: ${template.properties.value}</div>
                <div>美观度: ${template.properties.beauty}</div>
                <div>挂饰: ${template.properties.isWallDecoration ? '是' : '否'}</div>
                <div>放置时间: ${new Date(furniture.placedTime).toLocaleTimeString()}</div>
            `;
        }

        // 清除信息显示
        function clearInfo() {
            infoContent.innerHTML = '将鼠标悬停在房间格子或家具上查看详细信息...';
        }

        // 获取格子状态颜色
        function getGridStateColor(gridState) {
            switch (gridState) {
                case GridState.Empty: return '#28a745';
                case GridState.WallAdjacent: return '#6c757d';
                case GridState.Obstacle: return '#dc3545';
                case GridState.Occupied: return '#007bff';
                default: return '#6c757d';
            }
        }

        // 获取格子状态描述
        function getGridStateDescription(gridState) {
            switch (gridState) {
                case GridState.Empty: return '可以放置普通家具';
                case GridState.WallAdjacent: return '可以放置普通家具和挂饰类家具';
                case GridState.Obstacle: return '不可放置任何家具';
                case GridState.Occupied: return '已被家具占用';
                default: return '未知状态';
            }
        }

        // 获取旋转后的尺寸
        function getRotatedSize(baseSize, rotation) {
            if (rotation === Rotation.Deg90 || rotation === Rotation.Deg270) {
                return { x: baseSize.y, y: baseSize.x };
            }
            return { x: baseSize.x, y: baseSize.y };
        }

        // 生成家具ID
        function generateFurnitureId() {
            return `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        }

        // 添加随机家具
        function addRandomFurniture() {
            const rooms = ['roomA', 'roomB'];
            const roomId = rooms[Math.floor(Math.random() * rooms.length)];
            const roomConfig = roomConfigs[roomId];

            // 随机选择家具模板
            const template = furnitureTemplates[Math.floor(Math.random() * furnitureTemplates.length)];
            const rotation = [Rotation.Deg0, Rotation.Deg90, Rotation.Deg180, Rotation.Deg270][Math.floor(Math.random() * 4)];

            // 尝试找到合适的位置
            const maxAttempts = 50;
            for (let attempt = 0; attempt < maxAttempts; attempt++) {
                const x = Math.floor(Math.random() * roomConfig.size.x);
                const y = Math.floor(Math.random() * roomConfig.size.y);

                if (canPlaceFurniture(roomConfig, template, { x, y }, rotation)) {
                    const furniture = {
                        id: generateFurnitureId(),
                        templateId: template.id,
                        position: { x, y },
                        rotation: rotation,
                        currentSize: getRotatedSize(template.baseSize, rotation),
                        placedTime: Date.now()
                    };

                    // 添加家具到房间
                    roomConfig.furnitures.push(furniture);

                    // 更新格子状态
                    updateGridOccupancy(roomConfig, furniture, true);

                    // 重新渲染房间
                    renderRoom(roomId, roomConfig);

                    console.log(`成功在${roomConfig.name}添加家具: ${template.name}`);
                    return;
                }
            }

            console.log(`无法在${roomConfig.name}找到合适位置放置${template.name}`);
        }

        // 检查是否可以放置家具
        function canPlaceFurniture(roomConfig, template, position, rotation) {
            const currentSize = getRotatedSize(template.baseSize, rotation);

            // 检查边界
            if (position.x + currentSize.x > roomConfig.size.x ||
                position.y + currentSize.y > roomConfig.size.y ||
                position.x < 0 || position.y < 0) {
                return false;
            }

            // 检查每个格子
            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const checkX = position.x + dx;
                    const checkY = position.y + dy;
                    const gridState = roomConfig.layout[checkY][checkX];

                    // 障碍物不能放置
                    if (gridState === GridState.Obstacle) {
                        return false;
                    }

                    // 已占用不能放置
                    if (gridState === GridState.Occupied) {
                        return false;
                    }

                    // 挂饰类家具只能放在邻墙格子上
                    if (template.properties.isWallDecoration && gridState !== GridState.WallAdjacent) {
                        return false;
                    }
                }
            }

            return true;
        }

        // 更新格子占用状态
        function updateGridOccupancy(roomConfig, furniture, isOccupied) {
            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

            // 获取原始布局
            const originalLayout = getOriginalLayout(roomConfig.id);

            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const x = furniture.position.x + dx;
                    const y = furniture.position.y + dy;

                    if (x >= 0 && x < roomConfig.size.x && y >= 0 && y < roomConfig.size.y) {
                        if (isOccupied) {
                            roomConfig.layout[y][x] = GridState.Occupied;
                        } else {
                            // 恢复原始状态
                            roomConfig.layout[y][x] = originalLayout[y][x];
                        }
                    }
                }
            }
        }

        // 获取原始布局
        function getOriginalLayout(roomId) {
            if (roomId === 'roomA') {
                return [
                    [2,2,2,1,1,1,1,2,2,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,1,0,0,0,0,0,0,1,2],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [2,1,0,0,0,0,0,0,1,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,2,2,1,1,1,1,2,2,2]
                ];
            } else if (roomId === 'roomB') {
                return [
                    [2,2,1,1,2,2],
                    [2,1,0,0,1,2],
                    [1,0,0,0,0,1],
                    [1,0,0,0,0,1],
                    [2,1,0,0,1,2],
                    [2,2,1,1,2,2]
                ];
            }
            return [];
        }

        // 清空所有家具
        function clearAllFurniture() {
            // 重置房间A
            roomConfigs.roomA.furnitures = [];
            roomConfigs.roomA.layout = [
                [2,2,2,1,1,1,1,2,2,2],
                [2,2,1,0,0,0,0,1,2,2],
                [2,1,0,0,0,0,0,0,1,2],
                [1,0,0,0,0,0,0,0,0,1],
                [1,0,0,0,0,0,0,0,0,1],
                [1,0,0,0,0,0,0,0,0,1],
                [1,0,0,0,0,0,0,0,0,1],
                [2,1,0,0,0,0,0,0,1,2],
                [2,2,1,0,0,0,0,1,2,2],
                [2,2,2,1,1,1,1,2,2,2]
            ];

            // 重置房间B
            roomConfigs.roomB.furnitures = [];
            roomConfigs.roomB.layout = [
                [2,2,1,1,2,2],
                [2,1,0,0,1,2],
                [1,0,0,0,0,1],
                [1,0,0,0,0,1],
                [2,1,0,0,1,2],
                [2,2,1,1,2,2]
            ];

            // 重新渲染所有房间
            renderRoom('roomA', roomConfigs.roomA);
            renderRoom('roomB', roomConfigs.roomB);

            console.log('已清空所有家具');
        }

        // 重置房间
        function resetRooms() {
            clearAllFurniture();
            console.log('已重置所有房间');
        }
    </script>
</body>
</html>
