<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>frame_main_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,117}</string>
                <key>spriteSourceSize</key>
                <string>{100,117}</string>
                <key>textureRect</key>
                <string>{{137,1},{100,117}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_binggao.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,2}</string>
                <key>spriteSize</key>
                <string>{94,100}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{321,103},{94,100}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_bingkuai.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,98}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{221,120},{108,98}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>item_bingxin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{134,138}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{1,1},{134,138}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_dongtu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{4,0}</string>
                <key>spriteSize</key>
                <string>{100,96}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{239,1},{100,96}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>item_haishui.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,3}</string>
                <key>spriteSize</key>
                <string>{68,92}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{417,181},{68,92}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>item_jiancai.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,0}</string>
                <key>spriteSize</key>
                <string>{106,110}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{113,141},{106,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_laji.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{3,-1}</string>
                <key>spriteSize</key>
                <string>{110,110}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{1,141},{110,110}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_xuehe.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,-1}</string>
                <key>spriteSize</key>
                <string>{86,80}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{423,99},{86,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_xueqiu.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,2}</string>
                <key>spriteSize</key>
                <string>{96,88}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{423,1},{96,88}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>item_yan.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,-3}</string>
                <key>spriteSize</key>
                <string>{98,84}</string>
                <key>spriteSourceSize</key>
                <string>{148,146}</string>
                <key>textureRect</key>
                <string>{{337,1},{98,84}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>itemPlist.png</string>
            <key>size</key>
            <string>{512,252}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:598c4a397ace2fbf4f7a94bf7c5e92c5:aa70fe1c711cb7bc4b7b1962f29e432e:340478aafc2aaaf284561ebfd53867e1$</string>
            <key>textureFileName</key>
            <string>itemPlist.png</string>
        </dict>
    </dict>
</plist>
