[{"id": 10001, "type": 1, "rogueEntryType": "通用", "name": "5%装备攻击", "rarity": 1, "repeatable": false, "desc": "所有装备的攻击+5%", "effect": {"name": "5%装备攻击", "type": "AtkUP", "args": "", "value": 5, "upgradeValue": 0}, "icon": "全体攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10002, "type": 1, "rogueEntryType": "通用", "name": "10%装备攻击", "rarity": 2, "repeatable": false, "desc": "所有装备的攻击+10%", "effect": {"name": "10%装备攻击", "type": "AtkUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "全体攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10003, "type": 1, "rogueEntryType": "通用", "name": "15%装备攻击", "rarity": 3, "repeatable": false, "desc": "所有装备的攻击+15%", "effect": {"name": "15%装备攻击", "type": "AtkUP", "args": "", "value": 15, "upgradeValue": 0}, "icon": "全体攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10004, "type": 1, "rogueEntryType": "通用", "name": "5%装备攻速", "rarity": 1, "repeatable": false, "desc": "所有装备的速度+5%", "effect": {"name": "5%装备攻速", "type": "UseSpeedUP", "args": "", "value": 5, "upgradeValue": 0}, "icon": "全体攻速提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10005, "type": 1, "rogueEntryType": "通用", "name": "10%装备攻速", "rarity": 2, "repeatable": false, "desc": "所有装备的速度+10%", "effect": {"name": "10%装备攻速", "type": "UseSpeedUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "全体攻速提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10006, "type": 1, "rogueEntryType": "通用", "name": "15%装备攻速", "rarity": 3, "repeatable": false, "desc": "所有装备的速度+15%", "effect": {"name": "15%装备攻速", "type": "UseSpeedUP", "args": "", "value": 15, "upgradeValue": 0}, "icon": "全体攻速提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10007, "type": 1, "rogueEntryType": "通用", "name": "30银币", "rarity": 1, "repeatable": false, "desc": "立即获得30银币", "effect": {"name": "30银币", "type": "GetMoney", "args": "", "value": 30, "upgradeValue": 0}, "icon": "获得银币", "appearCondition": {"type": 0, "values": []}}, {"id": 10008, "type": 1, "rogueEntryType": "通用", "name": "60银币", "rarity": 2, "repeatable": false, "desc": "立即获得60银币", "effect": {"name": "60银币", "type": "GetMoney", "args": "", "value": 60, "upgradeValue": 0}, "icon": "获得银币", "appearCondition": {"type": 0, "values": []}}, {"id": 10009, "type": 1, "rogueEntryType": "通用", "name": "100银币", "rarity": 3, "repeatable": false, "desc": "立即获得100银币", "effect": {"name": "100银币", "type": "GetMoney", "args": "", "value": 100, "upgradeValue": 0}, "icon": "获得银币", "appearCondition": {"type": 0, "values": []}}, {"id": 10010, "type": 1, "rogueEntryType": "通用", "name": "5波次银币", "rarity": 2, "repeatable": false, "desc": "每个波次后获得的银币+5", "effect": {"name": "5波次银币", "type": "RoundMoneyUP", "args": "", "value": 5, "upgradeValue": 0}, "icon": "每波银币提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10011, "type": 1, "rogueEntryType": "通用", "name": "10波次银币", "rarity": 3, "repeatable": false, "desc": "每个波次后获得的银币+10", "effect": {"name": "10波次银币", "type": "RoundMoneyUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "每波银币提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10012, "type": 1, "rogueEntryType": "通用", "name": "随机升级", "rarity": 3, "repeatable": false, "desc": "随机一件装备合成提升1级", "effect": {"name": "随机升级", "type": "RandomRankUP", "args": "", "value": 1, "upgradeValue": 0}, "icon": "攻击提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 10013, "type": 1, "rogueEntryType": "通用", "name": "下落速度-5%", "rarity": 1, "repeatable": false, "desc": "矿洞坍塌的速度-5%", "effect": {"name": "下落速度-5%", "type": "CollapseSpeedDown", "args": "", "value": 5, "upgradeValue": 0}, "icon": "石块移速降低", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 10014, "type": 1, "rogueEntryType": "通用", "name": "下落速度-10%", "rarity": 2, "repeatable": false, "desc": "矿洞坍塌的速度-10%", "effect": {"name": "下落速度-10%", "type": "CollapseSpeedDown", "args": "", "value": 10, "upgradeValue": 0}, "icon": "石块移速降低", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 10015, "type": 1, "rogueEntryType": "通用", "name": "下落速度-15%", "rarity": 3, "repeatable": false, "desc": "矿洞坍塌的速度-15%", "effect": {"name": "下落速度-15%", "type": "CollapseSpeedDown", "args": "", "value": 15, "upgradeValue": 0}, "icon": "石块移速降低", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 10016, "type": 1, "rogueEntryType": "通用", "name": "10%雪人体力", "rarity": 1, "repeatable": false, "desc": "雪人体力+10%", "effect": {"name": "10%雪人体力", "type": "SnowmanHpUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10017, "type": 1, "rogueEntryType": "通用", "name": "20%雪人体力", "rarity": 2, "repeatable": false, "desc": "雪人体力+20%", "effect": {"name": "20%雪人体力", "type": "SnowmanHpUP", "args": "", "value": 20, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10018, "type": 1, "rogueEntryType": "通用", "name": "30%雪人体力", "rarity": 3, "repeatable": false, "desc": "雪人体力+30%", "effect": {"name": "30%雪人体力", "type": "SnowmanHpUP", "args": "", "value": 30, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10019, "type": 1, "rogueEntryType": "通用", "name": "20%经验加成", "rarity": 1, "repeatable": false, "desc": "经验获取效率+20%", "effect": {"name": "20%经验加成", "type": "ExpUP", "args": "", "value": 20, "upgradeValue": 0}, "icon": "经验加成", "appearCondition": {"type": 0, "values": []}}, {"id": 10020, "type": 1, "rogueEntryType": "通用", "name": "30%经验加成", "rarity": 2, "repeatable": false, "desc": "经验获取效率+30%", "effect": {"name": "30%经验加成", "type": "ExpUP", "args": "", "value": 30, "upgradeValue": 0}, "icon": "经验加成", "appearCondition": {"type": 0, "values": []}}, {"id": 10021, "type": 1, "rogueEntryType": "通用", "name": "50%经验加成", "rarity": 3, "repeatable": false, "desc": "经验获取效率+50%", "effect": {"name": "50%经验加成", "type": "ExpUP", "args": "", "value": 50, "upgradeValue": 0}, "icon": "经验加成", "appearCondition": {"type": 0, "values": []}}, {"id": 10022, "type": 1, "rogueEntryType": "通用", "name": "20%雪人攻击", "rarity": 1, "repeatable": false, "desc": "雪人挖掘的伤害+20%", "effect": {"name": "20%雪人攻击", "type": "SnowmanAtkUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10023, "type": 1, "rogueEntryType": "通用", "name": "30%雪人攻击", "rarity": 2, "repeatable": false, "desc": "雪人挖掘的伤害+30%", "effect": {"name": "30%雪人攻击", "type": "SnowmanAtkUP", "args": "", "value": 20, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10024, "type": 1, "rogueEntryType": "通用", "name": "50%雪人攻击", "rarity": 3, "repeatable": false, "desc": "雪人挖掘的伤害+50%", "effect": {"name": "50%雪人攻击", "type": "SnowmanAtkUP", "args": "", "value": 30, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10025, "type": 1, "rogueEntryType": "通用", "name": "5%2级物品", "rarity": 2, "repeatable": false, "desc": "商店出现2级物品概率+5%", "effect": {"name": "5%2级物品", "type": "RefreshEquipmentRankRateUP", "args": "", "value": 5, "upgradeValue": 0}, "icon": "商店概率提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10026, "type": 1, "rogueEntryType": "通用", "name": "10%2级物品", "rarity": 3, "repeatable": false, "desc": "商店出现2级物品概率+10%", "effect": {"name": "10%2级物品", "type": "RefreshEquipmentRankRateUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "商店概率提高", "appearCondition": {"type": 0, "values": []}}, {"id": 10027, "type": 1, "rogueEntryType": "通用", "name": "刷新价格-20%", "rarity": 2, "repeatable": false, "desc": "商店的刷新价格-20%", "effect": {"name": "刷新价格-20%", "type": "RefreshUseMoneyDown", "args": "", "value": 20, "upgradeValue": 0}, "icon": "商店价格降低", "appearCondition": {"type": 0, "values": []}}, {"id": 20101, "type": 1, "rogueEntryType": "武器", "name": "10%炸弹攻击", "rarity": 1, "repeatable": false, "desc": "炸弹的攻击+10%", "effect": {"name": "10%炸弹攻击", "type": "AtkUP", "args": "1", "value": 10, "upgradeValue": 0}, "icon": "炸弹攻击提高", "appearCondition": {"type": 1, "values": ["1", "1"]}}, {"id": 20102, "type": 1, "rogueEntryType": "武器", "name": "30%炸弹攻击", "rarity": 2, "repeatable": false, "desc": "炸弹的攻击+30%", "effect": {"name": "30%炸弹攻击", "type": "AtkUP", "args": "1", "value": 30, "upgradeValue": 0}, "icon": "炸弹攻击提高", "appearCondition": {"type": 1, "values": ["1", "2"]}}, {"id": 20103, "type": 1, "rogueEntryType": "武器", "name": "10%炸弹攻速", "rarity": 1, "repeatable": false, "desc": "炸弹的速度+10%", "effect": {"name": "10%炸弹攻速", "type": "UseSpeedUP", "args": "1", "value": 10, "upgradeValue": 0}, "icon": "炸弹攻速提高", "appearCondition": {"type": 1, "values": ["1", "3"]}}, {"id": 20104, "type": 1, "rogueEntryType": "武器", "name": "30%炸弹攻速", "rarity": 2, "repeatable": false, "desc": "炸弹的速度+30%", "effect": {"name": "30%炸弹攻速", "type": "UseSpeedUP", "args": "1", "value": 30, "upgradeValue": 0}, "icon": "炸弹攻速提高", "appearCondition": {"type": 1, "values": ["1", "4"]}}, {"id": 20201, "type": 1, "rogueEntryType": "武器", "name": "10%竖钻机攻击", "rarity": 1, "repeatable": false, "desc": "竖钻机的攻击+10%", "effect": {"name": "10%竖钻机攻击", "type": "AtkUP", "args": "2", "value": 10, "upgradeValue": 0}, "icon": "竖钻机攻击提高", "appearCondition": {"type": 1, "values": ["2", "1"]}}, {"id": 20202, "type": 1, "rogueEntryType": "武器", "name": "30%竖钻机攻击", "rarity": 2, "repeatable": false, "desc": "竖钻机的攻击+30%", "effect": {"name": "30%竖钻机攻击", "type": "AtkUP", "args": "2", "value": 30, "upgradeValue": 0}, "icon": "竖钻机攻击提高", "appearCondition": {"type": 1, "values": ["2", "2"]}}, {"id": 20203, "type": 1, "rogueEntryType": "武器", "name": "10%竖钻机攻速", "rarity": 1, "repeatable": false, "desc": "竖钻机的速度+10%", "effect": {"name": "10%竖钻机攻速", "type": "UseSpeedUP", "args": "2", "value": 10, "upgradeValue": 0}, "icon": "竖钻机攻速提高", "appearCondition": {"type": 1, "values": ["2", "3"]}}, {"id": 20204, "type": 1, "rogueEntryType": "武器", "name": "30%竖钻机攻速", "rarity": 2, "repeatable": false, "desc": "竖钻机的速度+30%", "effect": {"name": "30%竖钻机攻速", "type": "UseSpeedUP", "args": "2", "value": 30, "upgradeValue": 0}, "icon": "竖钻机攻速提高", "appearCondition": {"type": 1, "values": ["2", "4"]}}, {"id": 20301, "type": 1, "rogueEntryType": "武器", "name": "10%横钻机攻击", "rarity": 1, "repeatable": false, "desc": "横钻机的攻击+10%", "effect": {"name": "10%横钻机攻击", "type": "AtkUP", "args": "3", "value": 10, "upgradeValue": 0}, "icon": "横钻机攻击提高", "appearCondition": {"type": 1, "values": ["3", "1"]}}, {"id": 20302, "type": 1, "rogueEntryType": "武器", "name": "30%横钻机攻击", "rarity": 2, "repeatable": false, "desc": "横钻机的攻击+30%", "effect": {"name": "30%横钻机攻击", "type": "AtkUP", "args": "3", "value": 30, "upgradeValue": 0}, "icon": "横钻机攻击提高", "appearCondition": {"type": 1, "values": ["3", "2"]}}, {"id": 20303, "type": 1, "rogueEntryType": "武器", "name": "10%横钻机攻速", "rarity": 1, "repeatable": false, "desc": "横钻机的速度+10%", "effect": {"name": "10%横钻机攻速", "type": "UseSpeedUP", "args": "3", "value": 10, "upgradeValue": 0}, "icon": "横钻机攻速提高", "appearCondition": {"type": 1, "values": ["3", "3"]}}, {"id": 20304, "type": 1, "rogueEntryType": "武器", "name": "30%横钻机攻速", "rarity": 2, "repeatable": false, "desc": "横钻机的速度+30%", "effect": {"name": "30%横钻机攻速", "type": "UseSpeedUP", "args": "3", "value": 30, "upgradeValue": 0}, "icon": "横钻机攻速提高", "appearCondition": {"type": 1, "values": ["3", "4"]}}, {"id": 20401, "type": 1, "rogueEntryType": "武器", "name": "10%激光笔攻击", "rarity": 1, "repeatable": false, "desc": "激光笔的攻击+10%", "effect": {"name": "10%激光笔攻击", "type": "AtkUP", "args": "4", "value": 10, "upgradeValue": 0}, "icon": "激光笔攻击提高", "appearCondition": {"type": 1, "values": ["4", "1"]}}, {"id": 20402, "type": 1, "rogueEntryType": "武器", "name": "30%激光笔攻击", "rarity": 2, "repeatable": false, "desc": "激光笔的攻击+30%", "effect": {"name": "30%激光笔攻击", "type": "AtkUP", "args": "4", "value": 30, "upgradeValue": 0}, "icon": "激光笔攻击提高", "appearCondition": {"type": 1, "values": ["4", "2"]}}, {"id": 20403, "type": 1, "rogueEntryType": "武器", "name": "10%激光笔攻速", "rarity": 1, "repeatable": false, "desc": "激光笔的速度+10%", "effect": {"name": "10%激光笔攻速", "type": "UseSpeedUP", "args": "4", "value": 10, "upgradeValue": 0}, "icon": "激光笔攻速提高", "appearCondition": {"type": 1, "values": ["4", "3"]}}, {"id": 20404, "type": 1, "rogueEntryType": "武器", "name": "30%激光笔攻速", "rarity": 2, "repeatable": false, "desc": "激光笔的速度+30%", "effect": {"name": "30%激光笔攻速", "type": "UseSpeedUP", "args": "4", "value": 30, "upgradeValue": 0}, "icon": "激光笔攻速提高", "appearCondition": {"type": 1, "values": ["4", "4"]}}, {"id": 20501, "type": 1, "rogueEntryType": "武器", "name": "10%冰冻枪攻击", "rarity": 1, "repeatable": false, "desc": "冰冻枪的攻击+10%", "effect": {"name": "10%冰冻枪攻击", "type": "AtkUP", "args": "5", "value": 10, "upgradeValue": 0}, "icon": "冰冻枪攻击提高", "appearCondition": {"type": 1, "values": ["5", "1"]}}, {"id": 20502, "type": 1, "rogueEntryType": "武器", "name": "30%冰冻枪攻击", "rarity": 2, "repeatable": false, "desc": "冰冻枪的攻击+30%", "effect": {"name": "30%冰冻枪攻击", "type": "AtkUP", "args": "5", "value": 30, "upgradeValue": 0}, "icon": "冰冻枪攻击提高", "appearCondition": {"type": 1, "values": ["5", "2"]}}, {"id": 20503, "type": 1, "rogueEntryType": "武器", "name": "10%冰冻枪攻速", "rarity": 1, "repeatable": false, "desc": "冰冻枪的速度+10%", "effect": {"name": "10%冰冻枪攻速", "type": "UseSpeedUP", "args": "5", "value": 10, "upgradeValue": 0}, "icon": "冰冻枪攻速提高", "appearCondition": {"type": 1, "values": ["5", "3"]}}, {"id": 20504, "type": 1, "rogueEntryType": "武器", "name": "30%冰冻枪攻速", "rarity": 2, "repeatable": false, "desc": "冰冻枪的速度+30%", "effect": {"name": "30%冰冻枪攻速", "type": "UseSpeedUP", "args": "5", "value": 30, "upgradeValue": 0}, "icon": "冰冻枪攻速提高", "appearCondition": {"type": 1, "values": ["5", "4"]}}, {"id": 20601, "type": 1, "rogueEntryType": "武器", "name": "10%时光布攻击", "rarity": 1, "repeatable": false, "desc": "时光布的攻击+10%", "effect": {"name": "10%时光布攻击", "type": "AtkUP", "args": "6", "value": 10, "upgradeValue": 0}, "icon": "时光布攻击提高", "appearCondition": {"type": 1, "values": ["6", "1"]}}, {"id": 20602, "type": 1, "rogueEntryType": "武器", "name": "30%时光布攻击", "rarity": 2, "repeatable": false, "desc": "时光布的攻击+30%", "effect": {"name": "30%时光布攻击", "type": "AtkUP", "args": "6", "value": 30, "upgradeValue": 0}, "icon": "时光布攻击提高", "appearCondition": {"type": 1, "values": ["6", "2"]}}, {"id": 20603, "type": 1, "rogueEntryType": "武器", "name": "10%时光布攻速", "rarity": 1, "repeatable": false, "desc": "时光布的速度+10%", "effect": {"name": "10%时光布攻速", "type": "UseSpeedUP", "args": "6", "value": 10, "upgradeValue": 0}, "icon": "时光布攻速提高", "appearCondition": {"type": 1, "values": ["6", "3"]}}, {"id": 20604, "type": 1, "rogueEntryType": "武器", "name": "30%时光布攻速", "rarity": 2, "repeatable": false, "desc": "时光布的速度+30%", "effect": {"name": "30%时光布攻速", "type": "UseSpeedUP", "args": "6", "value": 30, "upgradeValue": 0}, "icon": "时光布攻速提高", "appearCondition": {"type": 1, "values": ["6", "4"]}}, {"id": 20701, "type": 1, "rogueEntryType": "武器", "name": "10%弹弹球攻击", "rarity": 1, "repeatable": false, "desc": "弹弹球的攻击+10%", "effect": {"name": "10%弹弹球攻击", "type": "AtkUP", "args": "7", "value": 10, "upgradeValue": 0}, "icon": "弹弹球攻击提高", "appearCondition": {"type": 1, "values": ["7", "1"]}}, {"id": 20702, "type": 1, "rogueEntryType": "武器", "name": "30%弹弹球攻击", "rarity": 2, "repeatable": false, "desc": "弹弹球的攻击+30%", "effect": {"name": "30%弹弹球攻击", "type": "AtkUP", "args": "7", "value": 30, "upgradeValue": 0}, "icon": "弹弹球攻击提高", "appearCondition": {"type": 1, "values": ["7", "2"]}}, {"id": 20703, "type": 1, "rogueEntryType": "武器", "name": "10%弹弹球攻速", "rarity": 1, "repeatable": false, "desc": "弹弹球的速度+10%", "effect": {"name": "10%弹弹球攻速", "type": "UseSpeedUP", "args": "7", "value": 10, "upgradeValue": 0}, "icon": "弹弹球攻速提高", "appearCondition": {"type": 1, "values": ["7", "3"]}}, {"id": 20704, "type": 1, "rogueEntryType": "武器", "name": "30%弹弹球攻速", "rarity": 2, "repeatable": false, "desc": "弹弹球的速度+30%", "effect": {"name": "30%弹弹球攻速", "type": "UseSpeedUP", "args": "7", "value": 30, "upgradeValue": 0}, "icon": "弹弹球攻速提高", "appearCondition": {"type": 1, "values": ["7", "4"]}}, {"id": 20801, "type": 1, "rogueEntryType": "武器", "name": "10%橡皮球攻击", "rarity": 1, "repeatable": false, "desc": "橡皮球的攻击+10%", "effect": {"name": "10%橡皮球攻击", "type": "AtkUP", "args": "8", "value": 10, "upgradeValue": 0}, "icon": "橡皮球攻击提高", "appearCondition": {"type": 1, "values": ["8", "1"]}}, {"id": 20802, "type": 1, "rogueEntryType": "武器", "name": "30%橡皮球攻击", "rarity": 2, "repeatable": false, "desc": "橡皮球的攻击+30%", "effect": {"name": "30%橡皮球攻击", "type": "AtkUP", "args": "8", "value": 30, "upgradeValue": 0}, "icon": "橡皮球攻击提高", "appearCondition": {"type": 1, "values": ["8", "2"]}}, {"id": 20803, "type": 1, "rogueEntryType": "武器", "name": "10%橡皮球攻速", "rarity": 1, "repeatable": false, "desc": "橡皮球的速度+10%", "effect": {"name": "10%橡皮球攻速", "type": "UseSpeedUP", "args": "8", "value": 10, "upgradeValue": 0}, "icon": "橡皮球攻速提高", "appearCondition": {"type": 1, "values": ["8", "3"]}}, {"id": 20804, "type": 1, "rogueEntryType": "武器", "name": "30%橡皮球攻速", "rarity": 2, "repeatable": false, "desc": "橡皮球的速度+30%", "effect": {"name": "30%橡皮球攻速", "type": "UseSpeedUP", "args": "8", "value": 30, "upgradeValue": 0}, "icon": "橡皮球攻速提高", "appearCondition": {"type": 1, "values": ["8", "4"]}}, {"id": 20901, "type": 1, "rogueEntryType": "武器", "name": "10%钢铁之拳攻击", "rarity": 1, "repeatable": false, "desc": "钢铁之拳的攻击+10%", "effect": {"name": "10%钢铁之拳攻击", "type": "AtkUP", "args": "9", "value": 10, "upgradeValue": 0}, "icon": "钢铁之拳攻击提高", "appearCondition": {"type": 1, "values": ["9", "1"]}}, {"id": 20902, "type": 1, "rogueEntryType": "武器", "name": "30%钢铁之拳攻击", "rarity": 2, "repeatable": false, "desc": "钢铁之拳的攻击+30%", "effect": {"name": "30%钢铁之拳攻击", "type": "AtkUP", "args": "9", "value": 30, "upgradeValue": 0}, "icon": "钢铁之拳攻击提高", "appearCondition": {"type": 1, "values": ["9", "2"]}}, {"id": 20903, "type": 1, "rogueEntryType": "武器", "name": "10%钢铁之拳攻速", "rarity": 1, "repeatable": false, "desc": "钢铁之拳的速度+10%", "effect": {"name": "10%钢铁之拳攻速", "type": "UseSpeedUP", "args": "9", "value": 10, "upgradeValue": 0}, "icon": "钢铁之拳攻速提高", "appearCondition": {"type": 1, "values": ["9", "3"]}}, {"id": 20904, "type": 1, "rogueEntryType": "武器", "name": "30%钢铁之拳攻速", "rarity": 2, "repeatable": false, "desc": "钢铁之拳的速度+30%", "effect": {"name": "30%钢铁之拳攻速", "type": "UseSpeedUP", "args": "9", "value": 30, "upgradeValue": 0}, "icon": "钢铁之拳攻速提高", "appearCondition": {"type": 1, "values": ["9", "4"]}}, {"id": 21001, "type": 1, "rogueEntryType": "武器", "name": "10%魔法棒攻击", "rarity": 1, "repeatable": false, "desc": "魔法棒的攻击+10%", "effect": {"name": "10%魔法棒攻击", "type": "AtkUP", "args": "10", "value": 10, "upgradeValue": 0}, "icon": "魔法棒攻击提高", "appearCondition": {"type": 1, "values": ["10", "1"]}}, {"id": 21002, "type": 1, "rogueEntryType": "武器", "name": "30%魔法棒攻击", "rarity": 2, "repeatable": false, "desc": "魔法棒的攻击+30%", "effect": {"name": "30%魔法棒攻击", "type": "AtkUP", "args": "10", "value": 30, "upgradeValue": 0}, "icon": "魔法棒攻击提高", "appearCondition": {"type": 1, "values": ["10", "2"]}}, {"id": 21003, "type": 1, "rogueEntryType": "武器", "name": "10%魔法棒攻速", "rarity": 1, "repeatable": false, "desc": "魔法棒的速度+10%", "effect": {"name": "10%魔法棒攻速", "type": "UseSpeedUP", "args": "10", "value": 10, "upgradeValue": 0}, "icon": "魔法棒攻速提高", "appearCondition": {"type": 1, "values": ["10", "3"]}}, {"id": 21004, "type": 1, "rogueEntryType": "武器", "name": "30%魔法棒攻速", "rarity": 2, "repeatable": false, "desc": "魔法棒的速度+30%", "effect": {"name": "30%魔法棒攻速", "type": "UseSpeedUP", "args": "10", "value": 30, "upgradeValue": 0}, "icon": "魔法棒攻速提高", "appearCondition": {"type": 1, "values": ["10", "4"]}}, {"id": 21101, "type": 1, "rogueEntryType": "武器", "name": "10%手榴弹攻击", "rarity": 1, "repeatable": false, "desc": "手榴弹的攻击+10%", "effect": {"name": "10%手榴弹攻击", "type": "AtkUP", "args": "11", "value": 10, "upgradeValue": 0}, "icon": "手榴弹攻击提高", "appearCondition": {"type": 1, "values": ["11", "1"]}}, {"id": 21102, "type": 1, "rogueEntryType": "武器", "name": "30%手榴弹攻击", "rarity": 2, "repeatable": false, "desc": "手榴弹的攻击+30%", "effect": {"name": "30%手榴弹攻击", "type": "AtkUP", "args": "11", "value": 30, "upgradeValue": 0}, "icon": "手榴弹攻击提高", "appearCondition": {"type": 1, "values": ["11", "2"]}}, {"id": 21103, "type": 1, "rogueEntryType": "武器", "name": "10%手榴弹攻速", "rarity": 1, "repeatable": false, "desc": "手榴弹的速度+10%", "effect": {"name": "10%手榴弹攻速", "type": "UseSpeedUP", "args": "11", "value": 10, "upgradeValue": 0}, "icon": "手榴弹攻速提高", "appearCondition": {"type": 1, "values": ["11", "3"]}}, {"id": 21104, "type": 1, "rogueEntryType": "武器", "name": "30%手榴弹攻速", "rarity": 2, "repeatable": false, "desc": "手榴弹的速度+30%", "effect": {"name": "30%手榴弹攻速", "type": "UseSpeedUP", "args": "11", "value": 30, "upgradeValue": 0}, "icon": "手榴弹攻速提高", "appearCondition": {"type": 1, "values": ["11", "4"]}}, {"id": 21201, "type": 1, "rogueEntryType": "武器", "name": "10%穿云箭攻击", "rarity": 1, "repeatable": false, "desc": "穿云箭的攻击+10%", "effect": {"name": "10%穿云箭攻击", "type": "AtkUP", "args": "12", "value": 10, "upgradeValue": 0}, "icon": "穿云箭攻击提高", "appearCondition": {"type": 1, "values": ["12", "1"]}}, {"id": 21202, "type": 1, "rogueEntryType": "武器", "name": "30%穿云箭攻击", "rarity": 2, "repeatable": false, "desc": "穿云箭的攻击+30%", "effect": {"name": "30%穿云箭攻击", "type": "AtkUP", "args": "12", "value": 30, "upgradeValue": 0}, "icon": "穿云箭攻击提高", "appearCondition": {"type": 1, "values": ["12", "2"]}}, {"id": 21203, "type": 1, "rogueEntryType": "武器", "name": "10%穿云箭攻速", "rarity": 1, "repeatable": false, "desc": "穿云箭的速度+10%", "effect": {"name": "10%穿云箭攻速", "type": "UseSpeedUP", "args": "12", "value": 10, "upgradeValue": 0}, "icon": "穿云箭攻速提高", "appearCondition": {"type": 1, "values": ["12", "3"]}}, {"id": 21204, "type": 1, "rogueEntryType": "武器", "name": "30%穿云箭攻速", "rarity": 2, "repeatable": false, "desc": "穿云箭的速度+30%", "effect": {"name": "30%穿云箭攻速", "type": "UseSpeedUP", "args": "12", "value": 30, "upgradeValue": 0}, "icon": "穿云箭攻速提高", "appearCondition": {"type": 1, "values": ["12", "4"]}}, {"id": 21301, "type": 1, "rogueEntryType": "武器", "name": "10%旋风陀螺攻击", "rarity": 1, "repeatable": false, "desc": "旋风陀螺的攻击+10%", "effect": {"name": "10%旋风陀螺攻击", "type": "AtkUP", "args": "13", "value": 10, "upgradeValue": 0}, "icon": "旋风陀螺攻击提高", "appearCondition": {"type": 1, "values": ["13", "1"]}}, {"id": 21302, "type": 1, "rogueEntryType": "武器", "name": "30%旋风陀螺攻击", "rarity": 2, "repeatable": false, "desc": "旋风陀螺的攻击+30%", "effect": {"name": "30%旋风陀螺攻击", "type": "AtkUP", "args": "13", "value": 30, "upgradeValue": 0}, "icon": "旋风陀螺攻击提高", "appearCondition": {"type": 1, "values": ["13", "2"]}}, {"id": 21303, "type": 1, "rogueEntryType": "武器", "name": "10%旋风陀螺攻速", "rarity": 1, "repeatable": false, "desc": "旋风陀螺的速度+10%", "effect": {"name": "10%旋风陀螺攻速", "type": "UseSpeedUP", "args": "13", "value": 10, "upgradeValue": 0}, "icon": "旋风陀螺攻速提高", "appearCondition": {"type": 1, "values": ["13", "3"]}}, {"id": 21304, "type": 1, "rogueEntryType": "武器", "name": "30%旋风陀螺攻速", "rarity": 2, "repeatable": false, "desc": "旋风陀螺的速度+30%", "effect": {"name": "30%旋风陀螺攻速", "type": "UseSpeedUP", "args": "13", "value": 30, "upgradeValue": 0}, "icon": "旋风陀螺攻速提高", "appearCondition": {"type": 1, "values": ["13", "4"]}}, {"id": 21501, "type": 1, "rogueEntryType": "武器", "name": "10%回旋镖攻击", "rarity": 1, "repeatable": false, "desc": "回旋镖的攻击+10%", "effect": {"name": "10%回旋镖攻击", "type": "AtkUP", "args": "15", "value": 10, "upgradeValue": 0}, "icon": "回旋镖攻击提高", "appearCondition": {"type": 1, "values": ["15", "1"]}}, {"id": 21502, "type": 1, "rogueEntryType": "武器", "name": "30%回旋镖攻击", "rarity": 2, "repeatable": false, "desc": "回旋镖的攻击+30%", "effect": {"name": "30%回旋镖攻击", "type": "AtkUP", "args": "15", "value": 30, "upgradeValue": 0}, "icon": "回旋镖攻击提高", "appearCondition": {"type": 1, "values": ["15", "2"]}}, {"id": 21503, "type": 1, "rogueEntryType": "武器", "name": "10%回旋镖攻速", "rarity": 1, "repeatable": false, "desc": "回旋镖的速度+10%", "effect": {"name": "10%回旋镖攻速", "type": "UseSpeedUP", "args": "15", "value": 10, "upgradeValue": 0}, "icon": "回旋镖攻速提高", "appearCondition": {"type": 1, "values": ["15", "3"]}}, {"id": 21504, "type": 1, "rogueEntryType": "武器", "name": "30%回旋镖攻速", "rarity": 2, "repeatable": false, "desc": "回旋镖的速度+30%", "effect": {"name": "30%回旋镖攻速", "type": "UseSpeedUP", "args": "15", "value": 30, "upgradeValue": 0}, "icon": "回旋镖攻速提高", "appearCondition": {"type": 1, "values": ["15", "4"]}}, {"id": 21601, "type": 1, "rogueEntryType": "武器", "name": "镐3级提升", "rarity": 1, "repeatable": false, "desc": "被动：升3级时攻击额外+5", "effect": {"name": "镐3级提升", "type": "AtkUP", "args": "", "value": 5, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21602, "type": 1, "rogueEntryType": "武器", "name": "镐5级提升", "rarity": 1, "repeatable": false, "desc": "被动：升5级时攻击额外+10", "effect": {"name": "镐5级提升", "type": "AtkUP", "args": "", "value": 10, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21603, "type": 1, "rogueEntryType": "武器", "name": "镐8级提升", "rarity": 1, "repeatable": false, "desc": "被动：升8级时攻击额外+15", "effect": {"name": "镐8级提升", "type": "AtkUP", "args": "", "value": 15, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21604, "type": 1, "rogueEntryType": "武器", "name": "镐10级提升", "rarity": 1, "repeatable": false, "desc": "被动：升10级时攻击额外+20", "effect": {"name": "镐10级提升", "type": "AtkUP", "args": "", "value": 20, "upgradeValue": 0}, "icon": "雪人攻击提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21701, "type": 1, "rogueEntryType": "武器", "name": "护目镜3级提升", "rarity": 1, "repeatable": false, "desc": "被动：升3级时体力额外+15", "effect": {"name": "护目镜3级提升", "type": "AtkUP", "args": "", "value": 15, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21702, "type": 1, "rogueEntryType": "武器", "name": "护目镜5级提升", "rarity": 1, "repeatable": false, "desc": "被动：升5级时体力额外+30", "effect": {"name": "护目镜5级提升", "type": "AtkUP", "args": "", "value": 30, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21703, "type": 1, "rogueEntryType": "武器", "name": "护目镜8级提升", "rarity": 1, "repeatable": false, "desc": "被动：升8级时体力额外+45", "effect": {"name": "护目镜8级提升", "type": "AtkUP", "args": "", "value": 45, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21704, "type": 1, "rogueEntryType": "武器", "name": "护目镜10级提升", "rarity": 1, "repeatable": false, "desc": "被动：升10级时体力额外+60", "effect": {"name": "护目镜10级提升", "type": "AtkUP", "args": "", "value": 60, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21801, "type": 1, "rogueEntryType": "武器", "name": "帽子3级提升", "rarity": 1, "repeatable": false, "desc": "被动：升3级时体力额外+15", "effect": {"name": "帽子3级提升", "type": "AtkUP", "args": "", "value": 15, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21802, "type": 1, "rogueEntryType": "武器", "name": "帽子5级提升", "rarity": 1, "repeatable": false, "desc": "被动：升5级时体力额外+30", "effect": {"name": "帽子5级提升", "type": "AtkUP", "args": "", "value": 30, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21803, "type": 1, "rogueEntryType": "武器", "name": "帽子8级提升", "rarity": 1, "repeatable": false, "desc": "被动：升8级时体力额外+45", "effect": {"name": "帽子8级提升", "type": "AtkUP", "args": "", "value": 45, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21804, "type": 1, "rogueEntryType": "武器", "name": "帽子10级提升", "rarity": 1, "repeatable": false, "desc": "被动：升10级时体力额外+60", "effect": {"name": "帽子10级提升", "type": "AtkUP", "args": "", "value": 60, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21901, "type": 1, "rogueEntryType": "武器", "name": "围巾3级提升", "rarity": 1, "repeatable": false, "desc": "被动：升3级时体力额外+12", "effect": {"name": "围巾3级提升", "type": "AtkUP", "args": "", "value": 12, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21902, "type": 1, "rogueEntryType": "武器", "name": "围巾5级提升", "rarity": 1, "repeatable": false, "desc": "被动：升5级时体力额外+25", "effect": {"name": "围巾5级提升", "type": "AtkUP", "args": "", "value": 25, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21903, "type": 1, "rogueEntryType": "武器", "name": "围巾8级提升", "rarity": 1, "repeatable": false, "desc": "被动：升8级时体力额外+38", "effect": {"name": "围巾8级提升", "type": "AtkUP", "args": "", "value": 38, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 21904, "type": 1, "rogueEntryType": "武器", "name": "围巾10级提升", "rarity": 1, "repeatable": false, "desc": "被动：升10级时体力额外+50", "effect": {"name": "围巾10级提升", "type": "AtkUP", "args": "", "value": 50, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 22001, "type": 1, "rogueEntryType": "武器", "name": "勋章3级提升", "rarity": 1, "repeatable": false, "desc": "被动：升3级时体力额外+6", "effect": {"name": "勋章3级提升", "type": "AtkUP", "args": "", "value": 6, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 22002, "type": 1, "rogueEntryType": "武器", "name": "勋章5级提升", "rarity": 1, "repeatable": false, "desc": "被动：升5级时体力额外+12", "effect": {"name": "勋章5级提升", "type": "AtkUP", "args": "", "value": 12, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 22003, "type": 1, "rogueEntryType": "武器", "name": "勋章8级提升", "rarity": 1, "repeatable": false, "desc": "被动：升8级时体力额外+18", "effect": {"name": "勋章8级提升", "type": "AtkUP", "args": "", "value": 18, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}, {"id": 22004, "type": 1, "rogueEntryType": "武器", "name": "勋章10级提升", "rarity": 1, "repeatable": false, "desc": "被动：升10级时体力额外+24", "effect": {"name": "勋章10级提升", "type": "AtkUP", "args": "", "value": 24, "upgradeValue": 0}, "icon": "雪人体力提高", "appearCondition": {"type": 1, "values": ["1", "999"]}}]