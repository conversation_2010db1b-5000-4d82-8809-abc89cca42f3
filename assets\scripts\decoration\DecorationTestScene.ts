/**
 * 装饰系统测试场景
 * 用于测试集成后的装饰系统功能
 */

import { Component, _decorator, Vec2, log } from "cc";
import { DecorationMgr } from "./DecorationMgr";
import { FurnitureManager } from "./FurnitureManager";
import { Rotation } from "./DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('DecorationTestScene')
export class DecorationTestScene extends Component {
    private decorationMgr!: DecorationMgr;
    private furnitureManager!: FurnitureManager;

    protected async onLoad() {
        // 初始化装饰管理器
        this.decorationMgr = DecorationMgr.getInstance();
        this.furnitureManager = FurnitureManager.getInstance();
        
        await this.decorationMgr.init();
        
        log("装饰系统测试场景已加载");
    }

    protected start() {
        this.runTests();
    }

    /**
     * 运行所有测试
     */
    private async runTests() {
        log("=== 开始装饰系统功能测试 ===");
        
        // 等待初始化完成
        await this.waitForInit();
        
        // 测试基本功能
        this.testBasicFunctionality();
        
        // 测试高级验证
        this.testAdvancedValidation();
        
        // 测试随机变化
        this.testRandomization();
        
        // 测试评分计算
        this.testScoreCalculation();
        
        // 测试挂饰功能
        this.testWallDecorations();
        
        log("=== 装饰系统功能测试完成 ===");
    }

    /**
     * 等待初始化完成
     */
    private async waitForInit(): Promise<void> {
        return new Promise(resolve => {
            setTimeout(resolve, 100);
        });
    }

    /**
     * 测试基本功能
     */
    private testBasicFunctionality() {
        log("--- 测试基本功能 ---");
        
        // 获取家具模板
        const templates = this.decorationMgr.getAllFurnitureTemplates();
        log(`加载了 ${templates.length} 个家具模板`);
        
        // 测试放置家具
        const placeResult = this.decorationMgr.placeFurniture(1, new Vec2(3, 3), Rotation.Deg0);
        log("放置椅子结果:", placeResult.success ? "成功" : placeResult.errorMessage);
        
        // 获取已放置的家具
        const placedFurnitures = this.decorationMgr.getPlacedFurnitures();
        log(`当前已放置 ${placedFurnitures.length} 个家具`);
        
        // 测试移除家具
        if (placedFurnitures.length > 0) {
            const removeResult = this.decorationMgr.removeFurniture(placedFurnitures[0].id);
            log("移除家具结果:", removeResult ? "成功" : "失败");
        }
    }

    /**
     * 测试高级验证功能
     */
    private testAdvancedValidation() {
        log("--- 测试高级验证功能 ---");
        
        // 测试位置验证
        const testPositions = [
            { pos: new Vec2(0, 0), desc: "左上角" },
            { pos: new Vec2(5, 3), desc: "中央" },
            { pos: new Vec2(11, 7), desc: "右下角" },
            { pos: new Vec2(-1, 0), desc: "边界外" }
        ];
        
        for (const test of testPositions) {
            const validation = this.furnitureManager.validatePlacementAdvanced(
                1, test.pos, Rotation.Deg0
            );
            log(`${test.desc} (${test.pos.x},${test.pos.y}): ${validation.isValid ? "可放置" : validation.reason}`);
        }
        
        // 测试自动修正
        const correctionResult = this.furnitureManager.autoCorrectFurniturePosition(
            1, new Vec2(-1, -1), Rotation.Deg0, 20
        );
        log("自动修正结果:", correctionResult.success ? 
            `成功，修正到 (${correctionResult.correctedPosition?.x},${correctionResult.correctedPosition?.y})，尝试 ${correctionResult.attempts} 次` :
            `失败，尝试 ${correctionResult.attempts} 次`);
    }

    /**
     * 测试随机变化功能
     */
    private testRandomization() {
        log("--- 测试随机变化功能 ---");
        
        // 先放置一个家具
        const placeResult = this.decorationMgr.placeFurniture(1, new Vec2(5, 4), Rotation.Deg0);
        if (!placeResult.success) {
            log("无法放置测试家具，跳过随机变化测试");
            return;
        }
        
        const placedFurnitures = this.decorationMgr.getPlacedFurnitures();
        if (placedFurnitures.length === 0) {
            log("没有家具可以进行随机变化测试");
            return;
        }
        
        const testFurniture = placedFurnitures[placedFurnitures.length - 1];
        const originalPos = testFurniture.position.clone();
        const originalRot = testFurniture.rotation;
        
        // 测试随机变化
        const randomizeResult = this.furnitureManager.randomizeFurniture(testFurniture.id);
        if (randomizeResult) {
            log("随机变化结果:", randomizeResult.success ? 
                `成功，变化类型: ${randomizeResult.changeType}，尝试 ${randomizeResult.attempts} 次` :
                `失败: ${randomizeResult.reason}`);
            
            if (randomizeResult.success) {
                const applied = this.furnitureManager.applyRandomizeResult(randomizeResult);
                log("应用随机变化结果:", applied ? "成功" : "失败");
                
                if (applied && randomizeResult.newFurniture) {
                    const newPos = randomizeResult.newFurniture.position;
                    const newRot = randomizeResult.newFurniture.rotation;
                    log(`位置变化: (${originalPos.x},${originalPos.y}) -> (${newPos.x},${newPos.y})`);
                    log(`旋转变化: ${originalRot} -> ${newRot}`);
                }
            }
        } else {
            log("随机变化失败: 无法获取结果");
        }
    }

    /**
     * 测试评分计算
     */
    private testScoreCalculation() {
        log("--- 测试评分计算 ---");
        
        // 清空所有家具
        this.decorationMgr.clearAllFurniture();
        
        // 放置一些家具进行评分测试
        const furnitureTests = [
            { id: 1, pos: new Vec2(2, 2), name: "椅子" },
            { id: 2, pos: new Vec2(4, 2), name: "床" },
            { id: 3, pos: new Vec2(6, 4), name: "桌子" }
        ];
        
        for (const test of furnitureTests) {
            const result = this.decorationMgr.placeFurniture(test.id, test.pos, Rotation.Deg0);
            log(`放置${test.name}:`, result.success ? "成功" : result.errorMessage);
        }
        
        // 计算评分
        const scoreDetails = this.decorationMgr.getRoomScoreDetails();
        if (scoreDetails) {
            log("=== 房间评分详情 ===");
            log(`总评分: ${scoreDetails.totalScore}`);
            log(`主题评分: ${scoreDetails.themeScore.toFixed(1)}`);
            log(`数量评分: ${scoreDetails.quantityScore.toFixed(1)}`);
            log(`价值评分: ${scoreDetails.valueScore.toFixed(1)}`);
            log(`布局评分: ${scoreDetails.layoutScore.toFixed(1)}`);
            log(`主导主题: ${this.decorationMgr.getDominantThemeName()}`);
            log(`评分等级: ${this.decorationMgr.getScoreGrade()}`);
        } else {
            log("无法获取评分详情");
        }
    }

    /**
     * 测试挂饰功能
     */
    private testWallDecorations() {
        log("--- 测试挂饰功能 ---");
        
        // 测试贴墙检查
        const wallTestPositions = [
            { pos: new Vec2(0, 1), desc: "左边界" },
            { pos: new Vec2(11, 1), desc: "右边界" },
            { pos: new Vec2(1, 0), desc: "上边界" },
            { pos: new Vec2(1, 7), desc: "下边界" },
            { pos: new Vec2(5, 3), desc: "中央位置" }
        ];
        
        for (const test of wallTestPositions) {
            const isWall = this.furnitureManager.isAdjacentToWall(test.pos);
            log(`${test.desc} (${test.pos.x},${test.pos.y}): ${isWall ? "贴墙" : "不贴墙"}`);
        }
        
        // 尝试放置挂饰类家具（如果有的话）
        const templates = this.decorationMgr.getAllFurnitureTemplates();
        const wallDecorationTemplate = templates.find(t => t.properties.isWallDecoration);
        
        if (wallDecorationTemplate) {
            log(`测试放置挂饰: ${wallDecorationTemplate.name}`);
            
            // 尝试在中央放置（应该失败）
            const centerResult = this.decorationMgr.placeFurniture(
                wallDecorationTemplate.id, 
                new Vec2(5, 3), 
                Rotation.Deg0
            );
            log("中央位置放置挂饰:", centerResult.success ? "成功" : centerResult.errorMessage);
            
            // 尝试在边界放置（应该成功）
            const wallResult = this.decorationMgr.placeFurniture(
                wallDecorationTemplate.id, 
                new Vec2(0, 2), 
                Rotation.Deg0
            );
            log("边界位置放置挂饰:", wallResult.success ? "成功" : wallResult.errorMessage);
        } else {
            log("没有找到挂饰类家具模板");
        }
        
        // 获取所有挂饰
        const wallDecorations = this.furnitureManager.getWallDecorations();
        log(`当前挂饰数量: ${wallDecorations.length}`);
    }

    /**
     * 手动触发测试（可在编辑器中调用）
     */
    public manualTest() {
        this.runTests();
    }

    /**
     * 清理测试数据
     */
    public cleanup() {
        this.decorationMgr.clearAllFurniture();
        log("测试数据已清理");
    }
}
