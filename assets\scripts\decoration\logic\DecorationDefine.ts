/**
 * 装饰系统相关数据定义
 */

import { Vec2 } from "cc";

/**
 * 房间格子状态
 */
export enum GridState {
    Empty = 0,          // 空地（可摆放）
    WallAdjacent = 1,   // 邻接障碍或墙壁的格子（可摆放，挂饰类家具只能摆放在这里）
    Obstacle = 2,       // 障碍（不可摆放）
    Occupied = 3        // 已占用（被家具占用）
}

/**
 * 家具类型
 */
export enum FurnitureType {
    Small = 1,      // 1×1 小型家具
    Medium = 2,     // 2×1 中型家具
    Large = 3,      // 2×2 大型家具
    WallDecoration = 4  // 挂饰类家具（必须贴墙）
}

/**
 * 旋转角度
 */
export enum Rotation {
    Deg0 = 0,
    Deg90 = 90,
    Deg180 = 180,
    Deg270 = 270
}

/**
 * 家具模板数据
 */
export interface FurnitureTemplate {
    id: number;
    name: string;
    type: FurnitureType;
    baseSize: Vec2;         // 基础尺寸 (width, height)
    spriteFrame: string;    // 图片资源路径
    description: string;    // 描述
    properties: FurnitureProperties;  // 家具属性
}

/**
 * 家具主题类型
 */
export enum FurnitureTheme {
    Modern = 1,     // 现代风格
    Classic = 2,    // 古典风格
    Natural = 3,    // 自然风格
    Industrial = 4, // 工业风格
    Minimalist = 5  // 简约风格
}

/**
 * 家具属性
 */
export interface FurnitureProperties {
    theme: FurnitureTheme;  // 主题
    level: number;          // 等级
    value: number;          // 价值
    beauty: number;         // 美观度
    isWallDecoration?: boolean;  // 是否为挂饰类家具
}

/**
 * 已放置的家具实例
 */
export interface PlacedFurniture {
    id: string;             // 唯一ID
    templateId: number;     // 模板ID
    position: Vec2;         // 位置 (x, y)
    rotation: Rotation;     // 旋转角度
    currentSize: Vec2;      // 当前旋转后的尺寸
    placedTime: number;     // 放置时间
    roomId?: string;        // 所属房间ID (多房间系统)
}

/**
 * 房间数据
 */
export interface RoomData {
    id: number;
    name: string;
    gridLayout: number[][];     // 房间格子布局
    placedFurnitures: PlacedFurniture[];  // 已放置的家具
    lastModified: number;       // 最后修改时间
}

/**
 * 多房间配置
 */
export interface MultiRoomConfig {
    rooms: Map<string, SingleRoomConfig>;
    sharedScore: boolean;       // 是否共享评分系统
}

/**
 * 单个房间配置
 */
export interface SingleRoomConfig {
    id: string;
    name: string;
    size: Vec2;                 // 房间尺寸
    layout: number[][];         // 房间布局模板 (0=空地, 1=邻墙, 2=障碍)
    placedFurnitures: PlacedFurniture[];  // 已放置的家具
    lastModified: number;       // 最后修改时间
}

/**
 * 默认房间模板
 */
export interface RoomTemplate {
    id: string;
    name: string;
    size: Vec2;
    layout: number[][];
    description?: string;
}

/**
 * 摆放验证结果
 */
export interface PlacementResult {
    success: boolean;
    errorMessage?: string;
    conflictPositions?: Vec2[];  // 冲突位置
}

/**
 * 家具操作类型
 */
export enum FurnitureOperation {
    Place = "place",
    Move = "move",
    Rotate = "rotate",
    Remove = "remove"
}

/**
 * 家具操作记录
 */
export interface FurnitureOperationRecord {
    operation: FurnitureOperation;
    furnitureId: string;
    oldPosition?: Vec2;
    newPosition?: Vec2;
    oldRotation?: Rotation;
    newRotation?: Rotation;
    timestamp: number;
}

/**
 * 房间评分详情
 */
export interface RoomScoreDetails {
    themeScore: number;     // 主题匹配度得分
    quantityScore: number;  // 家具数量得分
    valueScore: number;     // 家具价值得分
    layoutScore: number;    // 布局美观得分
    adjacentScore?: number; // 相邻加成得分 (可选，用于向后兼容)
    totalScore: number;     // 总分
    dominantTheme: FurnitureTheme | null;  // 主导主题
}

/**
 * 评分权重配置
 */
export interface ScoreWeights {
    themeWeight: number;    // 主题权重
    valueWeight: number;    // 价值权重
    layoutWeight: number;   // 布局权重
    adjacentWeight?: number; // 相邻加成权重 (可选，用于向后兼容)
}

/**
 * 工具函数：获取旋转后的尺寸
 */
export function getRotatedSize(baseSize: Vec2, rotation: Rotation): Vec2 {
    switch (rotation) {
        case Rotation.Deg0:
        case Rotation.Deg180:
            return new Vec2(baseSize.x, baseSize.y);
        case Rotation.Deg90:
        case Rotation.Deg270:
            return new Vec2(baseSize.y, baseSize.x);
        default:
            return new Vec2(baseSize.x, baseSize.y);
    }
}

/**
 * 工具函数：获取家具占用的所有格子位置
 */
export function getFurnitureOccupiedPositions(furniture: PlacedFurniture): Vec2[] {
    const positions: Vec2[] = [];
    const { position, currentSize } = furniture;
    
    for (let x = 0; x < currentSize.x; x++) {
        for (let y = 0; y < currentSize.y; y++) {
            positions.push(new Vec2(position.x + x, position.y + y));
        }
    }
    
    return positions;
}

/**
 * 工具函数：检查位置是否在房间范围内
 */
export function isPositionInRoom(position: Vec2, roomSize: Vec2): boolean {
    return position.x >= 0 && position.x < roomSize.x && 
           position.y >= 0 && position.y < roomSize.y;
}

/**
 * 工具函数：生成唯一ID
 */
export function generateFurnitureId(): string {
    return `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}
