{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16@6c48a", "displayName": "合成用-帽子", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16@f9941", "displayName": "合成用-帽子", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 295, "height": 140, "rawWidth": 295, "rawHeight": 140, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-147.5, -70, 0, 147.5, -70, 0, -147.5, 70, 0, 147.5, 70, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 140, 295, 140, 0, 0, 295, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-147.5, -70, 0], "maxPos": [147.5, 70, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f5f3e8f9-3855-441a-8634-8d9d2bddaf16@6c48a"}}