{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e@6c48a", "displayName": "迷你晨之星礼盒", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e@f9941", "displayName": "迷你晨之星礼盒", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0.5, "trimX": 0, "trimY": 0, "width": 99, "height": 67, "rawWidth": 99, "rawHeight": 68, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-49.5, -33.5, 0, 49.5, -33.5, 0, -49.5, 33.5, 0, 49.5, 33.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 68, 99, 68, 0, 1, 99, 1], "nuv": [0, 0.014705882352941176, 1, 0.014705882352941176, 0, 1, 1, 1], "minPos": [-49.5, -33.5, 0], "maxPos": [49.5, 33.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0eacead6-b43a-43aa-9a48-6fe7b1d3c22e@6c48a", "compressSettings": {"useCompressTexture": true, "presetId": "20bmNzS6VEmYy9ZsOKO6RA"}}}