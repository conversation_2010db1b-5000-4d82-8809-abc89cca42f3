{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "214ea964-826f-4169-9ce6-bae9de752e0b", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "214ea964-826f-4169-9ce6-bae9de752e0b@6c48a", "displayName": "合成用-镐4", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "214ea964-826f-4169-9ce6-bae9de752e0b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "214ea964-826f-4169-9ce6-bae9de752e0b@f9941", "displayName": "合成用-镐4", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 275, "height": 300, "rawWidth": 275, "rawHeight": 300, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-137.5, -150, 0, 137.5, -150, 0, -137.5, 150, 0, 137.5, 150, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 300, 275, 300, 0, 0, 275, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-137.5, -150, 0], "maxPos": [137.5, 150, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "214ea964-826f-4169-9ce6-bae9de752e0b@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "214ea964-826f-4169-9ce6-bae9de752e0b@6c48a"}}