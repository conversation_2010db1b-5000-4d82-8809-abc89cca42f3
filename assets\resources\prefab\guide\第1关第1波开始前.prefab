[{"__type__": "cc.Prefab", "_name": "第1关第1波开始前", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "第1关第1波开始前", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 21}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "01d4aUjQpZMip0tR3tmI9iX", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "guides": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 19}], "waitPre": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dda3ttovpJQacY/hAaqHPE"}, {"__type__": "SceneGuide", "name": "等待页面", "switchType": 2, "type": 9, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 26, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1对话", "switchType": 2, "type": 12, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "不好了，被困在矿洞了，先看看有什么道具可以用上！", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤2框", "switchType": 2, "type": 3, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": {"__uuid__": "2afe8f64-9023-42db-985c-a72ae8ec4bf4", "__expectedType__": "cc.Prefab"}, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤2对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "拖动装备，放入背包内", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤2引导", "switchType": 2, "type": 13, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "1", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "等待事件", "switchType": 2, "type": 15, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "SelectItemEmpty"}, {"__type__": "SceneGuide", "name": "步骤3对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "刷新商店，获得更多装备", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤3引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 12}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击刷新", "waitPageId": 0, "targetNodePath": "mineUI/ui/afterBg/bottom/arrangeUI/bg/freshBtn", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤4框", "switchType": 2, "type": 3, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": {"__uuid__": "2afe8f64-9023-42db-985c-a72ae8ec4bf4", "__expectedType__": "cc.Prefab"}, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤4对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "相同的装备合成升级", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤4引导", "switchType": 2, "type": 13, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "2", "isWaitAnimation": false, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "等待事件", "switchType": 2, "type": 15, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "SelectItemEmpty"}, {"__type__": "SceneGuide", "name": "步骤5对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "石头会落下来！我们要尽快的向下挖到出口才能逃出去。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [{"__id__": 18}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "开始游戏", "waitPageId": 0, "targetNodePath": "", "force": false, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤5引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 20}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "开始挖掘", "waitPageId": 26, "targetNodePath": "mineUI/ui/afterBg/bottom/arrangeUI/bg/finishBtn", "force": true, "nodeGuide": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]