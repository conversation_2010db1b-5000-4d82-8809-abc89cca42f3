{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "81ef805b-2143-42e2-8353-2e464d8c1187", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "81ef805b-2143-42e2-8353-2e464d8c1187@6c48a", "displayName": "合成用-钢铁之拳", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "81ef805b-2143-42e2-8353-2e464d8c1187", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "81ef805b-2143-42e2-8353-2e464d8c1187@f9941", "displayName": "合成用-钢铁之拳", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 114, "height": 132, "rawWidth": 114, "rawHeight": 132, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-57, -66, 0, 57, -66, 0, -57, 66, 0, 57, 66, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 132, 114, 132, 0, 0, 114, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-57, -66, 0], "maxPos": [57, 66, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "81ef805b-2143-42e2-8353-2e464d8c1187@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "81ef805b-2143-42e2-8353-2e464d8c1187@6c48a"}}