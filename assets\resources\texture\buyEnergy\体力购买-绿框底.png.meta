{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7@6c48a", "displayName": "体力购买-绿框底", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7@f9941", "displayName": "体力购买-绿框底", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 280, "height": 327, "rawWidth": 280, "rawHeight": 327, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-140, -163.5, 0, 140, -163.5, 0, -140, 163.5, 0, 140, 163.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 327, 280, 327, 0, 0, 280, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-140, -163.5, 0], "maxPos": [140, 163.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "74eddb4b-e05a-42e2-92f0-5064fb08d1c7@6c48a"}}