<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-旋风陀螺.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{252,128}</string>
                <key>spriteSourceSize</key>
                <string>{252,128}</string>
                <key>textureRect</key>
                <string>{{1,1},{252,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-旋风陀螺2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{252,128}</string>
                <key>spriteSourceSize</key>
                <string>{252,128}</string>
                <key>textureRect</key>
                <string>{{1,131},{252,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-旋风陀螺3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{252,128}</string>
                <key>spriteSourceSize</key>
                <string>{252,128}</string>
                <key>textureRect</key>
                <string>{{1,261},{252,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-旋风陀螺4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{252,128}</string>
                <key>spriteSourceSize</key>
                <string>{252,128}</string>
                <key>textureRect</key>
                <string>{{1,391},{252,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-旋风陀螺5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{252,128}</string>
                <key>spriteSourceSize</key>
                <string>{252,128}</string>
                <key>textureRect</key>
                <string>{{1,521},{252,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng13.png</string>
            <key>size</key>
            <string>{254,650}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:2392c6807cfae912ae4774ae929c6ada:e8c2469717175c3122903a88f05f5918:026b0e11982e4894123966f3d4fca300$</string>
            <key>textureFileName</key>
            <string>hecheng13.png</string>
        </dict>
    </dict>
</plist>
