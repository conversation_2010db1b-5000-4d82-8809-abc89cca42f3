{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7121b1f3-df77-47fa-a7b5-e35c7998a201", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "7121b1f3-df77-47fa-a7b5-e35c7998a201@6c48a", "displayName": "体力购买-黄框", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7121b1f3-df77-47fa-a7b5-e35c7998a201", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7121b1f3-df77-47fa-a7b5-e35c7998a201@f9941", "displayName": "体力购买-黄框", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 289, "height": 417, "rawWidth": 289, "rawHeight": 417, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-144.5, -208.5, 0, 144.5, -208.5, 0, -144.5, 208.5, 0, 144.5, 208.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 417, 289, 417, 0, 0, 289, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-144.5, -208.5, 0], "maxPos": [144.5, 208.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7121b1f3-df77-47fa-a7b5-e35c7998a201@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7121b1f3-df77-47fa-a7b5-e35c7998a201@6c48a"}}