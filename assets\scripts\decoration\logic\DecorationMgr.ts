/**
 * 装饰管理器
 * 整合家具摆放系统的所有功能，提供统一的接口
 */

import { Vec2 } from "cc";
import {
    FurnitureTemplate,
    PlacedFurniture,
    Rotation,
    PlacementResult,
    RoomScoreDetails,
    FurnitureTheme,
    SingleRoomConfig
} from "./DecorationDefine";
import { FurnitureManager } from "./FurnitureManager";
import { RoomGrid } from "./RoomGrid";
import { RoomScoreCalculator } from "./RoomScoreCalculator";
import { RandomizeResult } from "./FurnitureRandomizer";
import { MultiRoomManager } from "./MultiRoomManager";
import { IDataLoader } from "../../data_loader";
import { EventTargetMgr } from "../../EventTargetMgr";
import { EventTag } from "../../EventId";



export class DecorationMgr {
    private static _instance: DecorationMgr;

    public static getInstance(): DecorationMgr {
        return this._instance || (this._instance = new DecorationMgr());
    }

    private furnitureManager: FurnitureManager;
    private multiRoomManager: MultiRoomManager;
    private scoreCalculator: RoomScoreCalculator;
    private currentScore: RoomScoreDetails | null = null;
    private useMultiRoom: boolean = false; // 是否启用多房间模式

    private constructor() {
        this.furnitureManager = FurnitureManager.getInstance();//家具
        this.multiRoomManager = MultiRoomManager.getInstance();//房间
        this.scoreCalculator = new RoomScoreCalculator();//评分
    }

    /**
     * 初始化装饰管理器
     */
    async init() {
        await this.furnitureManager.init();
        this.calculateScore();
    }

    /**
     * 获取所有家具模板
     */
    getAllFurnitureTemplates(): FurnitureTemplate[] {
        return this.furnitureManager.getAllFurnitureTemplates();
    }

    /**
     * 获取家具模板
     * @param templateId 家具模板ID
     * @returns 家具模板对象，如果不存在则返回null
     */
    getFurnitureTemplate(templateId: number): FurnitureTemplate | null {
        return this.furnitureManager.getFurnitureTemplate(templateId);
    }

    /**
     * 放置家具
     * @param templateId 家具模板ID
     * @param position 放置位置坐标
     * @param rotation 旋转角度，默认为0度
     * @returns 放置结果，包含成功状态和错误信息
     */
    placeFurniture(templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): PlacementResult {
        const result = this.furnitureManager.placeFurniture(templateId, position, rotation);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.FurniturePlaced, templateId, position, rotation);
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 移除家具
     * @param furnitureId 家具实例ID
     * @returns 是否成功移除
     */
    removeFurniture(furnitureId: string): boolean {
        const success = this.furnitureManager.removeFurniture(furnitureId);

        if (success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.FurnitureRemoved, furnitureId);
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }

        return success;
    }

    /**
     * 移动家具
     * @param furnitureId 家具实例ID
     * @param newPosition 新的位置坐标
     * @returns 移动结果，包含成功状态和错误信息
     */
    moveFurniture(furnitureId: string, newPosition: Vec2): PlacementResult {
        const result = this.furnitureManager.moveFurniture(furnitureId, newPosition);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.FurnitureMoved, furnitureId, newPosition);
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 旋转家具
     * @param furnitureId 家具实例ID
     * @param newRotation 新的旋转角度
     * @returns 旋转结果，包含成功状态和错误信息
     */
    rotateFurniture(furnitureId: string, newRotation: Rotation): PlacementResult {
        const result = this.furnitureManager.rotateFurniture(furnitureId, newRotation);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.FurnitureRotated, furnitureId, newRotation);
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 获取已放置的家具
     */
    getPlacedFurnitures(): PlacedFurniture[] {
        return this.furnitureManager.getPlacedFurnitures();
    }

    /**
     * 获取房间格子
     */
    getRoomGrid(): RoomGrid | null {
        return this.furnitureManager.getRoomGrid();
    }

    /**
     * 计算当前房间评分
     */
    private calculateScore() {
        if (this.useMultiRoom) {
            // 多房间模式：使用多房间管理器计算评分
            this.currentScore = this.multiRoomManager.calculateScore();
        } else {
            // 单房间模式：使用原有逻辑
            const placedFurnitures = this.furnitureManager.getPlacedFurnitures();
            const furnitureTemplates = this.furnitureManager.getFurnitureTemplatesMap();

            const roomGrid = this.furnitureManager.getRoomGrid();
            if (roomGrid) {
                const roomSize = roomGrid.getRoomSize();
                this.currentScore = this.scoreCalculator.calculateRoomScore(
                    placedFurnitures,
                    furnitureTemplates,
                    roomSize
                );
            } else {
                this.currentScore = null;
            }
        }
    }

    /**
     * 获取当前房间评分详情
     */
    getRoomScoreDetails(): RoomScoreDetails | null {
        return this.currentScore;
    }

    /**
     * 获取总评分
     */
    getTotalScore(): number {
        return this.currentScore?.totalScore || 0;
    }

    /**
     * 获取主导主题
     */
    getDominantTheme(): FurnitureTheme | null {
        return this.currentScore?.dominantTheme || null;
    }

    /**
     * 获取主题名称
     */
    getDominantThemeName(): string {
        const theme = this.getDominantTheme();
        return theme ? this.scoreCalculator.getThemeName(theme) : "无主题";
    }

    /**
     * 检查家具是否可以放置
     * @param templateId 家具模板ID
     * @param position 放置位置坐标
     * @param rotation 旋转角度，默认为0度
     * @returns 是否可以放置
     */
    canPlaceFurniture(templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): boolean {
        const template = this.furnitureManager.getFurnitureTemplate(templateId);
        if (!template) return false;

        const roomGrid = this.furnitureManager.getRoomGrid();
        if (!roomGrid) return false;

        // 这里可以添加更复杂的验证逻辑
        return true;
    }

    /**
     * 获取推荐的摆放位置
     * @param templateId 家具模板ID
     * @param preferredPosition 首选位置坐标
     * @returns 推荐的位置坐标，如果没有合适位置则返回null
     */
    getRecommendedPosition(templateId: number, preferredPosition: Vec2): Vec2 | null {
        // TODO: 实现推荐位置算法
        return null;
    }

    /**
     * 启用多房间模式
     */
    async enableMultiRoomMode(): Promise<void> {
        if (this.useMultiRoom) return;

        this.useMultiRoom = true;
        const templates = this.furnitureManager.getFurnitureTemplatesMap();
        await this.multiRoomManager.init(templates);
        this.calculateScore();
    }

    /**
     * 禁用多房间模式
     */
    disableMultiRoomMode(): void {
        this.useMultiRoom = false;
        this.calculateScore();
    }

    /**
     * 在指定房间放置家具（多房间模式）
     * @param roomId 房间ID
     * @param templateId 家具模板ID
     * @param position 放置位置坐标
     * @param rotation 旋转角度，默认为0度
     * @returns 放置结果，包含成功状态和错误信息
     */
    placeFurnitureInRoom(roomId: string, templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): PlacementResult {
        if (!this.useMultiRoom) {
            return { success: false, errorMessage: "多房间模式未启用" };
        }

        const result = this.multiRoomManager.placeFurniture(roomId, templateId, position, rotation);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.FurniturePlaced, templateId, position, rotation);
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 获取所有房间ID
     * @returns 所有房间ID的数组，如果未启用多房间模式则返回空数组
     */
    getAllRoomIds(): string[] {
        if (!this.useMultiRoom) return [];
        return this.multiRoomManager.getAllRoomIds();
    }

    /**
     * 获取房间配置
     * @param roomId 房间ID
     * @returns 房间配置对象，如果未启用多房间模式或房间不存在则返回null
     */
    getRoomConfig(roomId: string): SingleRoomConfig | null {
        if (!this.useMultiRoom) return null;
        return this.multiRoomManager.getRoomConfig(roomId);
    }

    /**
     * 获取指定房间的家具
     * @param roomId 房间ID
     * @returns 房间内的家具数组，如果未启用多房间模式则返回空数组
     */
    getRoomFurnitures(roomId: string): PlacedFurniture[] {
        if (!this.useMultiRoom) return [];
        return this.multiRoomManager.getRoomFurnitures(roomId);
    }

    /**
     * 清空指定房间
     * @param roomId 房间ID
     * @returns 是否成功清空房间
     */
    clearRoom(roomId: string): boolean {
        if (!this.useMultiRoom) return false;

        const result = this.multiRoomManager.clearRoom(roomId);
        if (result) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
        }
        return result;
    }

    /**
     * 清空所有房间
     */
    clearAllRooms(): void {
        if (this.useMultiRoom) {
            this.multiRoomManager.clearAllRooms();
        } else {
            this.furnitureManager.clearAllFurnitures();
        }

        this.calculateScore();
        EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
    }

    /**
     * 检查是否启用了多房间模式
     */
    isMultiRoomMode(): boolean {
        return this.useMultiRoom;
    }

    /**
     * 随机变化家具位置和角度
     * @param furnitureId 家具实例ID
     * @returns 随机变化结果，包含成功状态、变化类型和新的家具信息，如果失败则返回null
     */
    randomizeFurniture(furnitureId: string): RandomizeResult | null {
        const result = this.furnitureManager.randomizeFurniture(furnitureId);

        if (result && result.success) {
            // 应用随机变化结果
            const applied = this.furnitureManager.applyRandomizeResult(result);
            if (applied) {
                this.calculateScore();
                // 发送家具变化事件（根据变化类型发送不同事件）
                if (result.changeType === 'position' || result.changeType === 'both') {
                    EventTargetMgr.instance.sendEvent(EventTag.FurnitureMoved, furnitureId, result.newFurniture?.position);
                }
                if (result.changeType === 'rotation' || result.changeType === 'both') {
                    EventTargetMgr.instance.sendEvent(EventTag.FurnitureRotated, furnitureId, result.newFurniture?.rotation);
                }
                EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
            }
        }

        return result;
    }

    /**
     * 清空所有家具
     */
    clearAllFurniture(): void {
        const placedFurnitures = this.furnitureManager.getPlacedFurnitures();

        if (placedFurnitures.length === 0) {
            return; // 没有家具需要清空
        }

        const furnitureIds = placedFurnitures.map(f => f.id);

        // 批量移除家具，避免每次都触发事件
        furnitureIds.forEach(id => {
            this.furnitureManager.removeFurniture(id);
        });

        this.calculateScore();

        // 发送批量移除事件
        EventTargetMgr.instance.sendEvent(EventTag.FurnitureRemoved, furnitureIds);
        EventTargetMgr.instance.sendEvent(EventTag.ScoreChanged, this.currentScore);
    }

    /**
     * 获取房间使用率
     */
    getRoomUsageRate(): number {
        const roomGrid = this.furnitureManager.getRoomGrid();
        if (!roomGrid) return 0;
        
        const roomSize = roomGrid.getRoomSize();
        const totalGrids = roomSize.x * roomSize.y;
        const occupiedPositions = roomGrid.getOccupiedPositions();
        
        return occupiedPositions.length / totalGrids;
    }

    /**
     * 获取评分等级描述
     */
    getScoreGrade(): string {
        const score = this.getTotalScore();
        if (score >= 90) return "完美";
        if (score >= 80) return "优秀";
        if (score >= 70) return "良好";
        if (score >= 60) return "一般";
        if (score >= 40) return "较差";
        return "糟糕";
    }

    /**
     * 获取评分颜色（用于UI显示）
     */
    getScoreColor(): string {
        const score = this.getTotalScore();
        if (score >= 90) return "#FFD700"; // 金色
        if (score >= 80) return "#FF6B35"; // 橙色
        if (score >= 70) return "#4ECDC4"; // 青色
        if (score >= 60) return "#45B7D1"; // 蓝色
        if (score >= 40) return "#96CEB4"; // 绿色
        return "#FFEAA7"; // 黄色
    }

    /**
     * 导出房间布局（用于分享或保存模板）
     * @returns 包含房间布局数据的JSON字符串
     */
    exportRoomLayout(): string {
        const placedFurnitures = this.furnitureManager.getPlacedFurnitures();
        const layoutData = {
            furnitures: placedFurnitures.map(f => ({
                templateId: f.templateId,
                position: { x: f.position.x, y: f.position.y },
                rotation: f.rotation
            })),
            timestamp: Date.now()
        };

        return JSON.stringify(layoutData);
    }

    /**
     * 导入房间布局
     * @param layoutJson 包含房间布局数据的JSON字符串
     * @returns 是否成功导入所有家具
     */
    importRoomLayout(layoutJson: string): boolean {
        try {
            const layoutData = JSON.parse(layoutJson);
            
            // 清空当前家具
            this.clearAllFurniture();
            
            // 放置新家具
            let successCount = 0;
            layoutData.furnitures.forEach((furnitureData: any) => {
                const result = this.placeFurniture(
                    furnitureData.templateId,
                    new Vec2(furnitureData.position.x, furnitureData.position.y),
                    furnitureData.rotation
                );
                if (result.success) {
                    successCount++;
                }
            });
            
            console.log(`导入房间布局完成，成功放置 ${successCount}/${layoutData.furnitures.length} 个家具`);
            return successCount === layoutData.furnitures.length;
            
        } catch (error) {
            console.error('导入房间布局失败:', error);
            return false;
        }
    }

    /**
     * 数据加载器
     */
    loader: IDataLoader = {
        name: "DecorationMgr",
        total: async () => 1,
        load: async (update) => {
            await this.init();
            update(1);
        },
    };
}
