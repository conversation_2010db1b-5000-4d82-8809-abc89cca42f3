import { _decorator, Component, Enum, Node } from 'cc';
const { ccclass, property } = _decorator;
const DecorationEnum = Enum({
    可以摆放除了挂饰以外的家具: 0,//中间空地, 或贴近窗口的位置(窗口虽然属于"墙", 但是不允许在窗子放挂饰)
    可以摆放家具或向左摆放挂饰: 1,//右侧邻墙位置, 包含地图最顶部的格子(左右邻墙交汇处的格子, 划分给右侧邻墙管理), 挂饰拖拽到这个格子时会自动调整为朝向左
    可以摆放家具或向右摆放挂饰: 2,//左侧邻墙位置, 不包含地图最顶部的格子, 挂饰拖拽到这个格子时会自动调整为朝向右
    可以摆放任何家具:3
});

@ccclass('GridComp')
export class GridComp extends Component {

    //枚举
    @property({ type: Enum(DecorationEnum), displayName: '设置格子的摆放属性' })
    objectA = DecorationEnum.可以摆放任何家具;

    start() {

    }

    update(deltaTime: number) {

    }
}


