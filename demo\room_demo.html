<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间摆放效果演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .demo-area {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }

        .room-container {
            flex: 2;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }

        .room-title {
            text-align: center;
            margin-bottom: 20px;
            color: #495057;
            font-size: 1.3em;
            font-weight: bold;
        }

        .room-grid {
            display: grid;
            grid-template-columns: repeat(18, 35px);
            grid-template-rows: repeat(10, 35px);
            gap: 1px;
            justify-content: center;
            background: #dee2e6;
            padding: 10px;
            border-radius: 8px;
            border: 3px solid #6c757d;
        }

        .grid-cell {
            width: 35px;
            height: 35px;
            border: 1px solid #adb5bd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .empty {
            background: #f8f9fa;
            color: #6c757d;
        }

        .obstacle {
            background: linear-gradient(45deg, #343a40, #495057);
            color: #adb5bd;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .chair {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .bed {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .table {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .sofa {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .bookshelf {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .painting {
            background: linear-gradient(45deg, #fd7e14, #e83e8c);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 2px solid #fff;
        }

        .photo {
            background: linear-gradient(45deg, #20c997, #17a2b8);
            color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 1px solid #fff;
        }

        .gap {
            background: #e9ecef;
            border: none;
        }

        .control-panel {
            flex: 1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }

        .score-section {
            margin-bottom: 25px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .score-title {
            font-size: 1.2em;
            color: #495057;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .score-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }

        .score-label {
            color: #6c757d;
        }

        .score-value {
            font-weight: bold;
            color: #28a745;
        }

        .total-score {
            border-top: 2px solid #e9ecef;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 1.1em;
        }

        .controls {
            margin-bottom: 25px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            margin-bottom: 5px;
            color: #495057;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .legend {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }

        .legend-title {
            font-size: 1.1em;
            color: #495057;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .legend-symbol {
            width: 25px;
            height: 25px;
            margin-right: 10px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .step-info {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
            border-radius: 8px;
            border: 1px solid #2196f3;
        }

        .step-title {
            font-size: 1.2em;
            color: #1976d2;
            font-weight: bold;
        }

        .animation-controls {
            text-align: center;
            margin-top: 20px;
        }

        @keyframes placeAnimation {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .placing {
            animation: placeAnimation 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 房间摆放效果演示</h1>
            <p>基于装饰系统的家具摆放和评分计算演示</p>
        </div>

        <div class="step-info">
            <div class="step-title" id="stepTitle">步骤0: 空房间</div>
        </div>

        <div class="demo-area">
            <div class="room-container">
                <div class="room-title">双房间布局 - 房间A (10×10) + 房间B (6×6)</div>
                <div class="room-grid" id="roomGrid"></div>
            </div>

            <div class="control-panel">
                <div class="score-section">
                    <div class="score-title">📊 房间评分</div>
                    <div class="score-item">
                        <span class="score-label">主题得分:</span>
                        <span class="score-value" id="themeScore">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">数量得分:</span>
                        <span class="score-value" id="quantityScore">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">价值得分:</span>
                        <span class="score-value" id="valueScore">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">布局得分:</span>
                        <span class="score-value" id="layoutScore">0</span>
                    </div>
                    <div class="score-item total-score">
                        <span class="score-label">总分:</span>
                        <span class="score-value" id="totalScore">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">主导主题:</span>
                        <span class="score-value" id="dominantTheme">无</span>
                    </div>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <label class="control-label">演示控制:</label>
                        <button class="btn btn-success" onclick="startDemo()">▶️ 开始演示</button>
                        <button class="btn btn-warning" onclick="pauseDemo()">⏸️ 暂停</button>
                        <button class="btn btn-secondary" onclick="resetDemo()">🔄 重置</button>
                        <button class="btn" onclick="testScoring()" style="background: linear-gradient(45deg, #28a745, #20c997);">🧪 测试评分</button>
                    </div>
                    <div class="control-group">
                        <label class="control-label">手动操作:</label>
                        <button class="btn" onclick="nextStep()">➡️ 下一步</button>
                        <button class="btn" onclick="prevStep()">⬅️ 上一步</button>
                    </div>
                    <div class="control-group">
                        <label class="control-label">调试功能:</label>
                        <button class="btn" onclick="testWallAdjacency()" style="background: linear-gradient(45deg, #6f42c1, #5a32a3);">🖼️ 测试贴墙</button>
                        <button class="btn" onclick="testAutoCorrection()" style="background: linear-gradient(45deg, #e83e8c, #dc3545);">🔧 测试修正</button>
                        <button class="btn" onclick="validateAllSteps()" style="background: linear-gradient(45deg, #28a745, #20c997);">✅ 验证步骤</button>
                    </div>
                    <div class="control-group">
                        <label class="control-label">随机测试:</label>
                        <button class="btn" onclick="randomizeFurniture()" style="background: linear-gradient(45deg, #fd7e14, #e83e8c);">🎲 随机变化</button>
                    </div>
                </div>

                <div class="legend">
                    <div class="legend-title">🎨 图例说明</div>
                    <div class="legend-item">
                        <div class="legend-symbol chair">C</div>
                        <span>椅子 (现代风格)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol bed">B</div>
                        <span>床铺 (古典风格)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol table">T</div>
                        <span>桌子 (工业风格)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol sofa">S</div>
                        <span>沙发 (现代风格)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol bookshelf">K</div>
                        <span>书架 (自然风格)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol painting">P</div>
                        <span>壁画 (古典风格) *贴墙</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol photo">F</div>
                        <span>照片墙 (现代风格) *贴墙</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol empty">·</div>
                        <span>空地</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-symbol obstacle">█</div>
                        <span>障碍物</span>
                    </div>
                </div>

                <div class="legend" style="margin-top: 15px;">
                    <div class="legend-title">🎲 随机变化状态</div>
                    <div id="randomizeStatus" style="color: #6c757d; font-size: 14px; padding: 10px;">
                        当前步骤无家具可随机变化
                    </div>
                </div>
            </div>
        </div>

        <div class="animation-controls">
            <p style="color: #666; margin-top: 10px;">
                💡 提示: 点击"开始演示"观看自动摆放过程，或使用手动控制按钮逐步查看
            </p>
        </div>
    </div>

    <script>
        // 房间配置
        var ROOM_A_WIDTH = 10;
        var ROOM_A_HEIGHT = 10;
        var ROOM_B_WIDTH = 6;
        var ROOM_B_HEIGHT = 6;
        var ROOM_GAP = 2; // 房间间隔

        // 房间A布局 (0=空地可摆放, 1=障碍不可摆放)
        var ROOM_A_LAYOUT = [
            [1,1,1,0,0,0,0,1,1,1],
            [1,1,0,0,0,0,0,0,1,1],
            [1,0,0,0,0,0,0,0,0,1],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [1,0,0,0,0,0,0,0,0,1],
            [1,1,0,0,0,0,0,0,1,1],
            [1,1,1,0,0,0,0,1,1,1]
        ];

        // 房间B布局
        var ROOM_B_LAYOUT = [
            [1,1,0,0,1,1],
            [1,0,0,0,0,1],
            [0,0,0,0,0,0],
            [0,0,0,0,0,0],
            [1,0,0,0,0,1],
            [1,1,0,0,1,1]
        ];

        // 总布局尺寸
        var TOTAL_WIDTH = ROOM_A_WIDTH + ROOM_GAP + ROOM_B_WIDTH;
        var TOTAL_HEIGHT = Math.max(ROOM_A_HEIGHT, ROOM_B_HEIGHT);
        
        // 验证家具布局是否有重叠
        function validateStepFurniture(stepFurniture, stepName) {
            console.log('验证步骤:', stepName);
            var conflicts = [];

            for (var i = 0; i < stepFurniture.length; i++) {
                var furniture1 = stepFurniture[i];
                for (var j = i + 1; j < stepFurniture.length; j++) {
                    var furniture2 = stepFurniture[j];

                    // 检查是否重叠
                    if (!(furniture1.x + furniture1.w <= furniture2.x ||
                          furniture2.x + furniture2.w <= furniture1.x ||
                          furniture1.y + furniture1.h <= furniture2.y ||
                          furniture2.y + furniture2.h <= furniture1.y)) {
                        conflicts.push({
                            furniture1: furniture1.type + '(' + furniture1.x + ',' + furniture1.y + ')',
                            furniture2: furniture2.type + '(' + furniture2.x + ',' + furniture2.y + ')'
                        });
                    }
                }
            }

            if (conflicts.length > 0) {
                console.error('发现重叠:', conflicts);
                return false;
            } else {
                console.log('✓ 无重叠');
                return true;
            }
        }

        // 重新生成经过验证的演示步骤数据
        var demoSteps = [
            {
                title: "空房间",
                furniture: [],
                description: "初始状态：房间A (左侧) 和房间B (右侧)，包含障碍物边界"
            },
            {
                title: "房间A - 放置椅子",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'}
                ],
                description: "在房间A中央放置现代风格椅子"
            },
            {
                title: "房间A - 添加床铺",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'}
                ],
                description: "在房间A上方添加古典风格床铺"
            },
            {
                title: "房间B - 放置沙发",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'}
                ],
                description: "在房间B放置现代风格沙发"
            },
            {
                title: "房间A - 添加桌子",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'},
                    {x: 6, y: 5, w: 2, h: 2, type: 'table', theme: 'industrial', room: 'A'}
                ],
                description: "在房间A添加工业风格桌子"
            },
            {
                title: "房间B - 添加书架",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'},
                    {x: 6, y: 5, w: 2, h: 2, type: 'table', theme: 'industrial', room: 'A'},
                    {x: 15, y: 3, w: 1, h: 2, type: 'bookshelf', theme: 'natural', room: 'B'}
                ],
                description: "在房间B添加自然风格书架"
            },
            {
                title: "添加更多椅子",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'},
                    {x: 6, y: 5, w: 2, h: 2, type: 'table', theme: 'industrial', room: 'A'},
                    {x: 15, y: 3, w: 1, h: 2, type: 'bookshelf', theme: 'natural', room: 'B'},
                    {x: 2, y: 6, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 16, y: 2, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'B'}
                ],
                description: "在两个房间添加更多椅子"
            },
            {
                title: "添加挂饰装饰",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'},
                    {x: 6, y: 5, w: 2, h: 2, type: 'table', theme: 'industrial', room: 'A'},
                    {x: 15, y: 3, w: 1, h: 2, type: 'bookshelf', theme: 'natural', room: 'B'},
                    {x: 2, y: 6, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 16, y: 2, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'B'},
                    {x: 2, y: 2, w: 1, h: 1, type: 'painting', theme: 'classic', room: 'A', wallAdjacent: true},
                    {x: 17, y: 1, w: 1, h: 1, type: 'photo', theme: 'modern', room: 'B', wallAdjacent: true}
                ],
                description: "添加贴墙挂饰：壁画和照片墙"
            },
            {
                title: "完整双房间布局",
                furniture: [
                    {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'},
                    {x: 13, y: 2, w: 2, h: 1, type: 'sofa', theme: 'modern', room: 'B'},
                    {x: 6, y: 5, w: 2, h: 2, type: 'table', theme: 'industrial', room: 'A'},
                    {x: 15, y: 3, w: 1, h: 2, type: 'bookshelf', theme: 'natural', room: 'B'},
                    {x: 2, y: 6, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'},
                    {x: 16, y: 2, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'B'},
                    {x: 2, y: 2, w: 1, h: 1, type: 'painting', theme: 'classic', room: 'A', wallAdjacent: true},
                    {x: 17, y: 1, w: 1, h: 1, type: 'photo', theme: 'modern', room: 'B', wallAdjacent: true},
                    {x: 5, y: 2, w: 1, h: 1, type: 'photo', theme: 'modern', room: 'A', wallAdjacent: true},
                    {x: 1, y: 7, w: 1, h: 1, type: 'painting', theme: 'classic', room: 'A', wallAdjacent: true},
                    {x: 4, y: 7, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'A'}
                ],
                description: "最终布局：充分利用空间，展示多样化家具搭配"
            }
        ];

        // 验证所有步骤
        function validateAllSteps() {
            if (isAnyOperationInProgress()) {
                console.warn('有操作正在进行中，无法执行验证');
                return;
            }
            console.log('=== 验证所有演示步骤 ===');
            var allValid = true;

            for (var i = 0; i < demoSteps.length; i++) {
                var step = demoSteps[i];
                var isValid = validateStepFurniture(step.furniture, step.title);
                if (!isValid) {
                    allValid = false;
                }
            }

            if (allValid) {
                console.log('✅ 所有步骤验证通过，无重叠冲突');
            } else {
                console.error('❌ 发现步骤中有重叠冲突');
            }

            return allValid;
        }

        // 更新所有按钮状态
        function updateAllButtonsState(disabled, reason) {
            var allButtons = document.querySelectorAll('button');
            for (var i = 0; i < allButtons.length; i++) {
                var button = allButtons[i];
                button.disabled = disabled;

                if (disabled) {
                    button.style.opacity = '0.4';
                    button.style.cursor = 'not-allowed';
                    if (!button.hasAttribute('data-original-text')) {
                        button.setAttribute('data-original-text', button.textContent);
                    }
                    if (button.onclick && button.onclick.toString().includes('randomizeFurniture')) {
                        button.textContent = '🔄 重新摆放中...';
                    }
                } else {
                    button.style.opacity = '1';
                    button.style.cursor = 'pointer';
                    if (button.hasAttribute('data-original-text')) {
                        button.textContent = button.getAttribute('data-original-text');
                        button.removeAttribute('data-original-text');
                    }
                }
            }

            // 更新状态显示
            var statusElement = document.getElementById('randomizeStatus');
            if (statusElement) {
                if (disabled) {
                    statusElement.innerHTML = '<span style="color: #fd7e14;">🔄 ' + (reason || '正在操作中...') + '</span>';
                } else {
                    updateRandomizeStatus(currentStep, demoSteps[currentStep] ? demoSteps[currentStep].furniture : []);
                }
            }
        }

        // 检查是否有任何操作正在进行
        function isAnyOperationInProgress() {
            return isRandomizing || isStepChanging || isPlaying;
        }

        // 更新随机变化按钮状态（保留用于兼容性）
        function updateRandomizeButton(disabled, text) {
            updateAllButtonsState(disabled, text);
        }

        // 清空房间显示
        function clearRoomDisplay() {
            for (var y = 0; y < TOTAL_HEIGHT; y++) {
                for (var x = 0; x < TOTAL_WIDTH; x++) {
                    var cell = document.getElementById('cell-' + x + '-' + y);
                    if (!cell) continue;

                    var cellType = 'empty';
                    var cellChar = '·';

                    // 房间A区域
                    if (x < ROOM_A_WIDTH && y < ROOM_A_HEIGHT) {
                        if (ROOM_A_LAYOUT[y][x] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 间隔区域
                    else if (x >= ROOM_A_WIDTH && x < ROOM_A_WIDTH + ROOM_GAP) {
                        cellType = 'gap';
                        cellChar = '';
                    }
                    // 房间B区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y < ROOM_B_HEIGHT) {
                        var bx = x - ROOM_A_WIDTH - ROOM_GAP;
                        if (bx < ROOM_B_WIDTH && ROOM_B_LAYOUT[y][bx] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 房间B外部区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y >= ROOM_B_HEIGHT) {
                        cellType = 'gap';
                        cellChar = '';
                    }

                    cell.className = 'grid-cell ' + cellType;
                    cell.textContent = cellChar;
                }
            }
        }

        // 随机变化家具位置和角度
        function randomizeFurniture() {
            // 检查是否有任何操作正在进行
            if (isAnyOperationInProgress()) {
                console.warn('有操作正在进行中，请等待完成');
                return;
            }

            console.log('=== 随机变化家具测试 ===');

            if (currentStep === 0 || !demoSteps[currentStep] || demoSteps[currentStep].furniture.length === 0) {
                console.warn('当前步骤没有家具可以随机变化');
                return;
            }

            // 设置随机变化状态
            isRandomizing = true;
            updateAllButtonsState(true, '正在重新摆放家具，请稍候...');

            var currentFurniture = demoSteps[currentStep].furniture.slice(); // 复制当前家具列表
            var lastFurniture = currentFurniture[currentFurniture.length - 1]; // 获取最新摆放的家具

            console.log('原始家具:', lastFurniture.type, '位置:', lastFurniture.x, lastFurniture.y, '尺寸:', lastFurniture.w + 'x' + lastFurniture.h);

            // 生成随机位置和角度
            var attempts = 0;
            var maxAttempts = 100;
            var newFurniture = null;

            while (attempts < maxAttempts && !newFurniture) {
                attempts++;

                // 随机生成新位置
                var newX, newY, newW, newH;

                // 根据原家具的房间属性确定搜索范围
                if (lastFurniture.room === 'A') {
                    newX = Math.floor(Math.random() * (ROOM_A_WIDTH - 2)) + 1;
                    newY = Math.floor(Math.random() * (ROOM_A_HEIGHT - 2)) + 1;
                } else if (lastFurniture.room === 'B') {
                    newX = Math.floor(Math.random() * (ROOM_B_WIDTH - 2)) + ROOM_A_WIDTH + ROOM_GAP + 1;
                    newY = Math.floor(Math.random() * (ROOM_B_HEIGHT - 2)) + 1;
                } else {
                    // 全房间随机
                    if (Math.random() < 0.5) {
                        newX = Math.floor(Math.random() * (ROOM_A_WIDTH - 2)) + 1;
                        newY = Math.floor(Math.random() * (ROOM_A_HEIGHT - 2)) + 1;
                    } else {
                        newX = Math.floor(Math.random() * (ROOM_B_WIDTH - 2)) + ROOM_A_WIDTH + ROOM_GAP + 1;
                        newY = Math.floor(Math.random() * (ROOM_B_HEIGHT - 2)) + 1;
                    }
                }

                // 随机旋转（50%概率）
                var shouldRotate = Math.random() < 0.5;
                if (shouldRotate && lastFurniture.w !== lastFurniture.h) {
                    newW = lastFurniture.h;
                    newH = lastFurniture.w;
                } else {
                    newW = lastFurniture.w;
                    newH = lastFurniture.h;
                }

                // 创建测试家具对象
                var testFurniture = {
                    x: newX,
                    y: newY,
                    w: newW,
                    h: newH,
                    type: lastFurniture.type,
                    theme: lastFurniture.theme,
                    room: lastFurniture.room,
                    wallAdjacent: lastFurniture.wallAdjacent
                };

                // 创建不包含当前家具的家具列表用于冲突检测
                var otherFurniture = currentFurniture.slice(0, -1);
                updateCurrentStepFurniture(otherFurniture);

                // 验证新位置
                if (validateFurniturePlacement(testFurniture)) {
                    newFurniture = testFurniture;
                    console.log('找到有效位置:', newX, newY, '尺寸:', newW + 'x' + newH, '(尝试次数:', attempts, ')');
                    if (shouldRotate && lastFurniture.w !== lastFurniture.h) {
                        console.log('家具已旋转');
                    }
                }
            }

            if (!newFurniture) {
                console.error('无法找到有效的随机位置 (尝试了', maxAttempts, '次)');
                updateCurrentStepFurniture(currentFurniture); // 恢复原始家具列表
                return;
            }

            // 更新家具列表
            var updatedFurniture = currentFurniture.slice(0, -1);
            updatedFurniture.push(newFurniture);

            // 更新演示步骤数据
            demoSteps[currentStep].furniture = updatedFurniture;
            updateCurrentStepFurniture(updatedFurniture);

            // 开始重新摆放动画
            console.log('开始重新摆放房间家具...');

            // 第一步：清空房间（延迟500ms）
            setTimeout(function() {
                clearRoomDisplay();
                console.log('房间已清空');

                // 第二步：逐个重新摆放家具（每个家具间隔300ms）
                setTimeout(function() {
                    replaceFurnitureWithAnimation(updatedFurniture, 0, lastFurniture, newFurniture);
                }, 500);
            }, 500);
        }

        // 带动画的重新摆放家具
        function replaceFurnitureWithAnimation(furnitureList, index, originalFurniture, newFurniture) {
            if (index >= furnitureList.length) {
                // 所有家具摆放完成
                finishRandomization(originalFurniture, newFurniture);
                return;
            }

            var furniture = furnitureList[index];
            console.log('摆放家具', index + 1, '/', furnitureList.length, ':', furniture.type);

            // 摆放当前家具
            var success = placeFurniture(furniture);
            if (!success) {
                console.error('家具摆放失败:', furniture.type);
            }

            // 继续摆放下一个家具
            setTimeout(function() {
                replaceFurnitureWithAnimation(furnitureList, index + 1, originalFurniture, newFurniture);
            }, 300);
        }

        // 完成随机变化
        function finishRandomization(originalFurniture, newFurniture) {
            // 重新计算评分
            setTimeout(function() {
                updateScore(demoSteps[currentStep].furniture);
            }, 200);

            // 显示变化结果
            console.log('✅ 家具随机变化完成');
            console.log('变化详情:');
            console.log('  原位置:', originalFurniture.x, originalFurniture.y, '尺寸:', originalFurniture.w + 'x' + originalFurniture.h);
            console.log('  新位置:', newFurniture.x, newFurniture.y, '尺寸:', newFurniture.w + 'x' + newFurniture.h);

            var positionChanged = (originalFurniture.x !== newFurniture.x || originalFurniture.y !== newFurniture.y);
            var sizeChanged = (originalFurniture.w !== newFurniture.w || originalFurniture.h !== newFurniture.h);

            if (positionChanged && sizeChanged) {
                console.log('  变化类型: 位置 + 旋转');
            } else if (positionChanged) {
                console.log('  变化类型: 仅位置');
            } else if (sizeChanged) {
                console.log('  变化类型: 仅旋转');
            }

            // 恢复所有按钮状态
            isRandomizing = false;
            updateAllButtonsState(false);
        }

        var currentStep = 0;
        var isPlaying = false;
        var playInterval = null;
        var isRandomizing = false; // 随机变化进行中标志
        var isStepChanging = false; // 步骤切换进行中标志

        // 初始化双房间网格
        function initializeRoom() {
            var roomGrid = document.getElementById('roomGrid');
            roomGrid.innerHTML = '';

            for (var y = 0; y < TOTAL_HEIGHT; y++) {
                for (var x = 0; x < TOTAL_WIDTH; x++) {
                    var cell = document.createElement('div');
                    var cellType = 'empty';
                    var cellChar = '·';

                    // 房间A区域
                    if (x < ROOM_A_WIDTH && y < ROOM_A_HEIGHT) {
                        if (ROOM_A_LAYOUT[y][x] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 间隔区域
                    else if (x >= ROOM_A_WIDTH && x < ROOM_A_WIDTH + ROOM_GAP) {
                        cellType = 'gap';
                        cellChar = '';
                    }
                    // 房间B区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y < ROOM_B_HEIGHT) {
                        var bx = x - ROOM_A_WIDTH - ROOM_GAP;
                        if (bx < ROOM_B_WIDTH && ROOM_B_LAYOUT[y][bx] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 房间B外部区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y >= ROOM_B_HEIGHT) {
                        cellType = 'gap';
                        cellChar = '';
                    }

                    cell.className = 'grid-cell ' + cellType;
                    cell.textContent = cellChar;
                    cell.id = 'cell-' + x + '-' + y;
                    roomGrid.appendChild(cell);
                }
            }
        }

        // 渲染当前步骤
        function renderStep(stepIndex, isManualStep) {
            try {
                console.log('=== 渲染步骤', stepIndex, '===');
                var step = demoSteps[stepIndex];

                if (!step) {
                    console.error('步骤数据不存在:', stepIndex);
                    return;
                }

                // 如果是手动步骤切换，设置步骤切换状态
                if (isManualStep) {
                    isStepChanging = true;
                    updateAllButtonsState(true, '正在切换步骤，摆放家具中...');
                }

                // 更新步骤标题
                document.getElementById('stepTitle').textContent = '步骤' + stepIndex + ': ' + step.title;
                console.log('步骤标题:', step.title);

            // 清空双房间 (保持障碍物和间隔)
            for (var y = 0; y < TOTAL_HEIGHT; y++) {
                for (var x = 0; x < TOTAL_WIDTH; x++) {
                    var cell = document.getElementById('cell-' + x + '-' + y);
                    var cellType = 'empty';
                    var cellChar = '·';

                    // 房间A区域
                    if (x < ROOM_A_WIDTH && y < ROOM_A_HEIGHT) {
                        if (ROOM_A_LAYOUT[y][x] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 间隔区域
                    else if (x >= ROOM_A_WIDTH && x < ROOM_A_WIDTH + ROOM_GAP) {
                        cellType = 'gap';
                        cellChar = '';
                    }
                    // 房间B区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y < ROOM_B_HEIGHT) {
                        var bx = x - ROOM_A_WIDTH - ROOM_GAP;
                        if (bx < ROOM_B_WIDTH && ROOM_B_LAYOUT[y][bx] === 1) {
                            cellType = 'obstacle';
                            cellChar = '█';
                        }
                    }
                    // 房间B外部区域
                    else if (x >= ROOM_A_WIDTH + ROOM_GAP && y >= ROOM_B_HEIGHT) {
                        cellType = 'gap';
                        cellChar = '';
                    }

                    cell.className = 'grid-cell ' + cellType;
                    cell.textContent = cellChar;
                }
            }
            
            // 更新当前步骤的家具列表
            updateCurrentStepFurniture(step.furniture);

            // 放置家具
            step.furniture.forEach(function(furniture, index) {
                setTimeout(function() {
                    var success = placeFurniture(furniture);
                    if (!success) {
                        console.error('家具摆放失败:', furniture.type);
                    }
                }, index * 200);
            });

            // 更新评分 (修复延迟时间计算)
            var scoreUpdateDelay = Math.max(100, step.furniture.length * 200 + 100);
            console.log('评分更新延迟:', scoreUpdateDelay + 'ms');
            setTimeout(function() {
                updateScore(step.furniture);

                // 如果是手动步骤切换，在评分更新后恢复按钮状态
                if (isManualStep) {
                    setTimeout(function() {
                        isStepChanging = false;
                        updateAllButtonsState(false);
                        console.log('步骤切换完成，按钮状态已恢复');
                    }, 200);
                }
            }, scoreUpdateDelay);

            // 更新随机变化状态
            updateRandomizeStatus(stepIndex, step.furniture);

            } catch (error) {
                console.error('渲染步骤出错:', error);
                // 出错时也要恢复按钮状态
                if (isManualStep) {
                    isStepChanging = false;
                    updateAllButtonsState(false);
                }
            }
        }

        // 更新随机变化状态显示
        function updateRandomizeStatus(stepIndex, furniture) {
            var statusElement = document.getElementById('randomizeStatus');
            if (!statusElement) return;

            if (stepIndex === 0 || furniture.length === 0) {
                statusElement.innerHTML = '当前步骤无家具可随机变化';
                statusElement.style.color = '#6c757d';
            } else {
                var lastFurniture = furniture[furniture.length - 1];
                var statusText = '可随机变化: <strong>' + lastFurniture.type + '</strong><br>' +
                                '当前位置: (' + lastFurniture.x + ',' + lastFurniture.y + ')<br>' +
                                '尺寸: ' + lastFurniture.w + '×' + lastFurniture.h + '<br>' +
                                '房间: ' + (lastFurniture.room || '未指定');
                statusElement.innerHTML = statusText;
                statusElement.style.color = '#495057';
            }
        }

        // 检查位置是否可用 (不是障碍物且未被占用)
        function isPositionAvailable(x, y, excludeFurniture) {
            // 检查边界
            if (x < 0 || x >= TOTAL_WIDTH || y < 0 || y >= TOTAL_HEIGHT) {
                return false;
            }

            // 检查房间A区域
            if (x < ROOM_A_WIDTH && y < ROOM_A_HEIGHT) {
                if (ROOM_A_LAYOUT[y][x] === 1) {
                    return false; // 是障碍物
                }
            }
            // 检查间隔区域
            else if (x >= ROOM_A_WIDTH && x < ROOM_A_WIDTH + ROOM_GAP) {
                return false; // 间隔区域不可摆放
            }
            // 检查房间B区域
            else if (x >= ROOM_A_WIDTH + ROOM_GAP && y < ROOM_B_HEIGHT) {
                var bx = x - ROOM_A_WIDTH - ROOM_GAP;
                if (bx >= ROOM_B_WIDTH || ROOM_B_LAYOUT[y][bx] === 1) {
                    return false; // 超出房间B范围或是障碍物
                }
            }
            // 房间B外部区域
            else if (x >= ROOM_A_WIDTH + ROOM_GAP && y >= ROOM_B_HEIGHT) {
                return false; // 房间B外部不可摆放
            }

            // 检查是否被其他家具占用
            var currentFurniture = getCurrentPlacedFurniture();
            for (var i = 0; i < currentFurniture.length; i++) {
                var f = currentFurniture[i];
                if (excludeFurniture && f.id === excludeFurniture.id) continue;

                // 检查是否与现有家具重叠
                if (x >= f.x && x < f.x + f.w && y >= f.y && y < f.y + f.h) {
                    return false;
                }
            }

            return true;
        }

        // 检查是否贴墙 (挂饰类家具专用)
        function isAdjacentToWall(x, y) {
            // 检查四个方向是否有障碍物
            var directions = [
                {dx: -1, dy: 0}, // 左
                {dx: 1, dy: 0},  // 右
                {dx: 0, dy: -1}, // 上
                {dx: 0, dy: 1}   // 下
            ];

            for (var i = 0; i < directions.length; i++) {
                var dir = directions[i];
                var checkX = x + dir.dx;
                var checkY = y + dir.dy;

                // 检查房间A区域
                if (checkX >= 0 && checkX < ROOM_A_WIDTH && checkY >= 0 && checkY < ROOM_A_HEIGHT) {
                    if (ROOM_A_LAYOUT[checkY][checkX] === 1) {
                        return true; // 贴着房间A的障碍物
                    }
                }
                // 检查房间B区域
                else if (checkX >= ROOM_A_WIDTH + ROOM_GAP && checkY >= 0 && checkY < ROOM_B_HEIGHT) {
                    var bx = checkX - ROOM_A_WIDTH - ROOM_GAP;
                    if (bx >= 0 && bx < ROOM_B_WIDTH && ROOM_B_LAYOUT[checkY][bx] === 1) {
                        return true; // 贴着房间B的障碍物
                    }
                }
                // 检查边界（视为墙壁）
                else if (checkX < 0 || checkX >= TOTAL_WIDTH || checkY < 0 || checkY >= TOTAL_HEIGHT) {
                    return true; // 贴着边界
                }
            }
            return false;
        }

        // 获取当前已摆放的家具列表
        var currentStepFurniture = []; // 全局变量存储当前步骤的家具

        function getCurrentPlacedFurniture() {
            return currentStepFurniture;
        }

        // 更新当前步骤的家具列表
        function updateCurrentStepFurniture(furniture) {
            currentStepFurniture = furniture.slice(); // 复制数组
        }

        // 验证家具摆放位置
        function validateFurniturePlacement(furniture) {
            console.log('验证家具摆放:', furniture.type, '位置:', furniture.x, furniture.y);

            // 检查所有占用格子是否可用
            for (var dy = 0; dy < furniture.h; dy++) {
                for (var dx = 0; dx < furniture.w; dx++) {
                    var x = furniture.x + dx;
                    var y = furniture.y + dy;

                    if (!isPositionAvailable(x, y, furniture)) {
                        console.warn('位置不可用:', x, y);
                        return false;
                    }
                }
            }

            // 挂饰类家具需要贴墙检查
            var isWallDecoration = furniture.type === 'painting' || furniture.type === 'photo';
            if (isWallDecoration && !isAdjacentToWall(furniture.x, furniture.y)) {
                console.warn('挂饰类家具必须贴墙摆放');
                return false;
            }

            return true;
        }

        // 自动修正家具位置
        function autoCorrectFurniturePosition(furniture) {
            console.log('尝试自动修正位置:', furniture.type);

            var isWallDecoration = furniture.type === 'painting' || furniture.type === 'photo';
            var maxAttempts = 50;
            var attempt = 0;

            // 确定搜索范围
            var searchRanges = [];
            if (furniture.room === 'A') {
                searchRanges.push({minX: 0, maxX: ROOM_A_WIDTH, minY: 0, maxY: ROOM_A_HEIGHT});
            } else if (furniture.room === 'B') {
                searchRanges.push({minX: ROOM_A_WIDTH + ROOM_GAP, maxX: TOTAL_WIDTH, minY: 0, maxY: ROOM_B_HEIGHT});
            } else {
                // 搜索所有可用区域
                searchRanges.push({minX: 0, maxX: ROOM_A_WIDTH, minY: 0, maxY: ROOM_A_HEIGHT});
                searchRanges.push({minX: ROOM_A_WIDTH + ROOM_GAP, maxX: TOTAL_WIDTH, minY: 0, maxY: ROOM_B_HEIGHT});
            }

            // 优先尝试原位置附近
            var searchOrder = [];
            var originalX = furniture.x;
            var originalY = furniture.y;

            // 生成搜索顺序：从原位置开始螺旋搜索
            for (var radius = 0; radius <= 3; radius++) {
                for (var dx = -radius; dx <= radius; dx++) {
                    for (var dy = -radius; dy <= radius; dy++) {
                        if (Math.abs(dx) === radius || Math.abs(dy) === radius) {
                            searchOrder.push({x: originalX + dx, y: originalY + dy});
                        }
                    }
                }
            }

            // 尝试搜索顺序中的位置
            for (var i = 0; i < searchOrder.length && attempt < maxAttempts; i++) {
                var pos = searchOrder[i];
                attempt++;

                // 检查是否在有效范围内
                var inValidRange = false;
                for (var r = 0; r < searchRanges.length; r++) {
                    var range = searchRanges[r];
                    if (pos.x >= range.minX && pos.x + furniture.w <= range.maxX &&
                        pos.y >= range.minY && pos.y + furniture.h <= range.maxY) {
                        inValidRange = true;
                        break;
                    }
                }

                if (!inValidRange) continue;

                // 创建测试家具对象
                var testFurniture = {
                    x: pos.x,
                    y: pos.y,
                    w: furniture.w,
                    h: furniture.h,
                    type: furniture.type,
                    theme: furniture.theme,
                    room: furniture.room
                };

                if (validateFurniturePlacement(testFurniture)) {
                    console.log('找到可用位置:', pos.x, pos.y, '(尝试次数:', attempt, ')');
                    return testFurniture;
                }
            }

            console.error('无法找到合适的摆放位置:', furniture.type);
            return null;
        }

        // 放置家具 (支持双房间、挂饰类家具和自动修正)
        function placeFurniture(furniture) {
            var chars = {
                'chair': 'C',
                'bed': 'B',
                'table': 'T',
                'sofa': 'S',
                'bookshelf': 'K',
                'painting': 'P',
                'photo': 'F'
            };
            var char = chars[furniture.type] || '?';

            // 验证摆放位置
            var validatedFurniture = furniture;
            if (!validateFurniturePlacement(furniture)) {
                console.warn('原位置不符合摆放标准，尝试自动修正...');
                validatedFurniture = autoCorrectFurniturePosition(furniture);

                if (!validatedFurniture) {
                    console.error('无法摆放家具:', furniture.type);
                    return false;
                }

                if (validatedFurniture.x !== furniture.x || validatedFurniture.y !== furniture.y) {
                    console.log('位置已修正:', furniture.type,
                        '从 (' + furniture.x + ',' + furniture.y + ') 到 (' +
                        validatedFurniture.x + ',' + validatedFurniture.y + ')');
                }
            }

            // 使用验证后的家具位置进行摆放
            for (var dy = 0; dy < validatedFurniture.h; dy++) {
                for (var dx = 0; dx < validatedFurniture.w; dx++) {
                    var x = validatedFurniture.x + dx;
                    var y = validatedFurniture.y + dy;

                    var cell = document.getElementById('cell-' + x + '-' + y);
                    if (cell) {
                        cell.className = 'grid-cell ' + validatedFurniture.type + ' placing';
                        cell.textContent = char;

                        // 移除动画类
                        setTimeout(function() {
                            cell.classList.remove('placing');
                        }, 500);
                    }
                }
            }

            return true;
        }

        // 更新评分 (支持新家具类型)
        function updateScore(furniture) {
            try {
                console.log('=== 开始更新评分 ===');
                console.log('家具数量:', furniture.length);
                console.log('家具列表:', furniture);

            var themeCount = {};
            var totalValue = 0;
            var totalBeauty = 0;

            // 家具属性配置
            var furnitureProps = {
                'chair': { value: 8, beauty: 6 },
                'bed': { value: 15, beauty: 8 },
                'table': { value: 12, beauty: 5 },
                'sofa': { value: 20, beauty: 10 },
                'bookshelf': { value: 18, beauty: 7 },
                'painting': { value: 25, beauty: 15 }, // 挂饰类家具高美观度
                'photo': { value: 12, beauty: 12 }     // 挂饰类家具高美观度
            };

            // 计算基础数据
            furniture.forEach(function(f) {
                themeCount[f.theme] = (themeCount[f.theme] || 0) + 1;
                var props = furnitureProps[f.type] || { value: 10, beauty: 5 };
                totalValue += props.value;
                totalBeauty += props.beauty;
            });

            // 计算各项得分
            var quantityScore = Math.min(75, furniture.length * 10);
            var valueScore = Math.min(75, totalValue * 1.5);
            var layoutScore = Math.min(100, 40 + totalBeauty * 2);

            // 主题得分 (考虑主题多样性)
            var themes = Object.keys(themeCount);
            var themeScore = 50; // 基础分

            if (themes.length === 0) {
                themeScore = 0;
            } else if (themes.length === 1) {
                themeScore = 90; // 单一主题高分
            } else if (themes.length === 2) {
                themeScore = 75; // 双主题搭配
            } else if (themes.length === 3) {
                themeScore = 60; // 三主题混搭
            } else {
                themeScore = Math.max(30, 70 - themes.length * 8); // 过多主题扣分
            }

            // 双房间布局奖励
            var roomAFurniture = furniture.filter(function(f) { return f.x < ROOM_A_WIDTH; });
            var roomBFurniture = furniture.filter(function(f) { return f.x >= ROOM_A_WIDTH + ROOM_GAP; });
            if (roomAFurniture.length > 0 && roomBFurniture.length > 0) {
                layoutScore += 10; // 双房间使用奖励
            }

            // 挂饰类家具奖励
            var wallDecorations = furniture.filter(function(f) {
                return f.type === 'painting' || f.type === 'photo';
            });
            if (wallDecorations.length > 0) {
                layoutScore += wallDecorations.length * 8; // 每个挂饰+8分
                console.log('挂饰奖励:', wallDecorations.length * 8, '分');
            }

            var totalScore = Math.round(
                themeScore * 0.3 +
                quantityScore * 0.25 +
                valueScore * 0.25 +
                Math.min(100, layoutScore) * 0.2
            );

            console.log('评分计算结果:', {
                themeScore: themeScore.toFixed(1),
                quantityScore: quantityScore.toFixed(1),
                valueScore: valueScore.toFixed(1),
                layoutScore: Math.min(100, layoutScore).toFixed(1),
                totalScore: totalScore,
                themeCount: themeCount
            });

            // 更新显示
            document.getElementById('themeScore').textContent = themeScore.toFixed(1);
            document.getElementById('quantityScore').textContent = quantityScore.toFixed(1);
            document.getElementById('valueScore').textContent = valueScore.toFixed(1);
            document.getElementById('layoutScore').textContent = Math.min(100, layoutScore).toFixed(1);
            document.getElementById('totalScore').textContent = totalScore;

            var themeNames = {
                'modern': '现代风格',
                'classic': '古典风格',
                'industrial': '工业风格',
                'natural': '自然风格',
                'minimalist': '简约风格'
            };

            var dominantTheme = themes.length > 0 ?
                themes.reduce(function(a, b) { return themeCount[a] > themeCount[b] ? a : b; }) : null;
            document.getElementById('dominantTheme').textContent =
                dominantTheme ? themeNames[dominantTheme] : '无';

            console.log('=== 评分更新完成 ===');
            } catch (error) {
                console.error('评分计算出错:', error);
                // 设置默认值
                document.getElementById('themeScore').textContent = '0';
                document.getElementById('quantityScore').textContent = '0';
                document.getElementById('valueScore').textContent = '0';
                document.getElementById('layoutScore').textContent = '0';
                document.getElementById('totalScore').textContent = '0';
                document.getElementById('dominantTheme').textContent = '无';
            }
        }

        // 测试评分系统
        function testScoring() {
            if (isAnyOperationInProgress()) {
                console.warn('有操作正在进行中，无法执行测试');
                return;
            }
            console.log('=== 测试评分系统 ===');
            var testFurniture = [
                {x: 4, y: 4, w: 1, h: 1, type: 'chair', theme: 'modern'},
                {x: 3, y: 2, w: 2, h: 1, type: 'bed', theme: 'classic'},
                {x: 2, y: 2, w: 1, h: 1, type: 'painting', theme: 'classic', wallAdjacent: true},
                {x: 16, y: 1, w: 1, h: 1, type: 'photo', theme: 'modern', wallAdjacent: true}
            ];
            updateScore(testFurniture);
        }

        // 测试贴墙检查
        function testWallAdjacency() {
            if (isAnyOperationInProgress()) {
                console.warn('有操作正在进行中，无法执行测试');
                return;
            }
            console.log('=== 测试贴墙检查 ===');
            var testPositions = [
                {x: 2, y: 2, desc: '房间A内部贴墙位置'},
                {x: 4, y: 4, desc: '房间A中央位置'},
                {x: 16, y: 1, desc: '房间B边缘位置'},
                {x: 14, y: 3, desc: '房间B内部位置'}
            ];

            testPositions.forEach(function(pos) {
                var isWall = isAdjacentToWall(pos.x, pos.y);
                console.log(pos.desc + ' (' + pos.x + ',' + pos.y + '):', isWall ? '贴墙' : '不贴墙');
            });
        }

        // 测试自动修正功能
        function testAutoCorrection() {
            if (isAnyOperationInProgress()) {
                console.warn('有操作正在进行中，无法执行测试');
                return;
            }
            console.log('=== 测试自动修正功能 ===');

            // 测试冲突位置的家具
            var conflictFurniture = [
                {x: 0, y: 0, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'}, // 障碍物位置
                {x: 11, y: 3, w: 1, h: 1, type: 'chair', theme: 'modern', room: 'A'}, // 间隔区域
                {x: 20, y: 5, w: 2, h: 1, type: 'bed', theme: 'classic', room: 'B'}, // 超出边界
                {x: 5, y: 5, w: 1, h: 1, type: 'painting', theme: 'classic', room: 'A'} // 不贴墙的挂饰
            ];

            conflictFurniture.forEach(function(furniture, index) {
                console.log('测试家具', index + 1, ':', furniture.type, '原位置:', furniture.x, furniture.y);
                var corrected = autoCorrectFurniturePosition(furniture);
                if (corrected) {
                    console.log('修正后位置:', corrected.x, corrected.y);
                } else {
                    console.log('无法修正');
                }
                console.log('---');
            });
        }

        // 控制函数
        function startDemo() {
            if (isAnyOperationInProgress()) return;

            isPlaying = true;
            playInterval = setInterval(function() {
                if (currentStep < demoSteps.length - 1) {
                    currentStep++;
                    renderStep(currentStep, false); // 自动播放不是手动步骤
                } else {
                    pauseDemo();
                }
            }, 3000);
        }

        function pauseDemo() {
            if (isRandomizing || isStepChanging) return;
            isPlaying = false;
            if (playInterval) {
                clearInterval(playInterval);
                playInterval = null;
            }
        }

        function resetDemo() {
            if (isAnyOperationInProgress()) return;
            pauseDemo();
            currentStep = 0;
            renderStep(currentStep, false);
        }

        function nextStep() {
            if (isAnyOperationInProgress()) return;
            if (currentStep < demoSteps.length - 1) {
                currentStep++;
                renderStep(currentStep, true); // 手动步骤切换
            }
        }

        function prevStep() {
            if (isAnyOperationInProgress()) return;
            if (currentStep > 0) {
                currentStep--;
                renderStep(currentStep, true); // 手动步骤切换
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeRoom();
            renderStep(0);
        });
    </script>
</body>
</html>
