[{"__type__": "cc.Prefab", "_name": "挖矿", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "挖矿", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 14}, {"__id__": 20}, {"__id__": 37}, {"__id__": 54}, {"__id__": 60}, {"__id__": 93}, {"__id__": 126}, {"__id__": 132}, {"__id__": 248}, {"__id__": 254}, {"__id__": 260}, {"__id__": 277}, {"__id__": 294}, {"__id__": 310}, {"__id__": 333}, {"__id__": 356}], "_active": true, "_components": [{"__id__": 379}, {"__id__": 381}, {"__id__": 383}], "_prefab": {"__id__": 385}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "展示图1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 113.572, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "白边锁", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.9, "y": 1.9, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb3QWef25OkLj++fGSI9Yy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8af4841a-beba-4a2e-83fe-c576ad4817b2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4djn2WCJVE/I5CRLe0u2T4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ac48smuf1KkJJLnCfypKYr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 724, "height": 698}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67N+ukKy5BIrMSaloXU8Ci"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 12}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dc0d2b19-8efb-44ae-aeee-dea539efeae9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89sFQejE1I4ZbTHvikrpuj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bu//SKixLJ5GOYySpxTfL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "花", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 177.371, "y": 6.21, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 262, "height": 295}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33k1aYGRFHHbNZiJk8H8gU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f236ba91-cd7c-4bd1-bac2-eebef98b6db7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29wP/3DVVD24Uz9UXq9NdX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebHUrSfAFFfZ6wmeghLfCP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "进度条-", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 21}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": -444.12, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.4, "y": 1.4, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 22.836, "y": 30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7142857142857144, "y": 0.7142857142857144, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bf2zkemd9CkqT8lOmz7vof"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ehgWnkwdGt4Ngu3CXXjlp"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 27}, "type": "MINE_STAGE_BEFORE_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ae9p1Y23dLJq+6mAWqGI/Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcq5YZ4GxDwLGZ/jnaj9Rg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 30}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b37Ks4fD1HSaemP/egRX03"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 32}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87e8e070-a3a5-4a2c-9ddd-6d690cb6899a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5QjHDIN9EyqBSpbkAuLPJ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 34}, "clickEvents": [{"__id__": 35}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 20}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3PyfsxyBMnr4QlgJ34kon"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onLastStageBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92oX4YRklAAqCIoAsILcQ5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "进度条--001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 38}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 48}, {"__id__": 50}], "_prefab": {"__id__": 53}, "_lpos": {"__type__": "cc.Vec3", "x": 443.542, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.4, "y": 1.4, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 37}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 41}, {"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 12.874, "y": 30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7142857142857144, "y": 0.7142857142857144, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09YFF5P1VDVLD9gHV1pvf9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 42}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7b0aP2/L9L+rqbZ3h43+Vq"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 44}, "type": "MINE_STAGE_AFTER_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b4AohAylPNYIKoTzNGkFw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cebUgrA25NkI4Yb9jkW6Jk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 47}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acgjNo5BJGf40ZYkmiN7Vb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 49}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "22fccb47-1a76-445a-99d0-56b018f4f76f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91OKoOCVZOJ4KqGzMwPWR0"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 51}, "clickEvents": [{"__id__": 52}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2kZoNwA9ED6obPbX8XIWY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onNextStageBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecRwT54uJG+IGVO8CQ/Ayu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "矿洞关卡页-宝箱虚线", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -354.423, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 907, "height": 21}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3AfHCcxhLHKLrhJNJ2K/P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 58}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4477ab49-6539-4833-9df2-4a1721c095f3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7FnkboqBOq4fI0Z6Ox/PJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e9shhRwF9Ni68dDHTzCV2D", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "扫荡按钮", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 61}, {"__id__": 67}], "_active": false, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": -328.871, "y": -594.833, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 26.847999999999956, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 167.4482421875, "height": 102.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a7FxxCHhOC5SLtz4b0FdK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "扫 荡", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 70, "_fontSize": 70, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 75, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 81, "g": 117, "b": 155, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4hl2yDxZKzLsreRUjBGP8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4I8pf7C5O8p3rFL0ZDaF0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 68}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -17.059, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "体力", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": -19.10205078125, "y": -24.855, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 28.563965, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7mccykrBDJZr4vsdrJ+kl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 72}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "35078e9d-9ad8-4640-8781-54788cf45942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdZLoP24dFroBkE1vy4G/A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ce/Bxw5eNB6L+pHmw+vMkR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": -4.82006828125, "y": -23.725, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 38.2041015625, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cezHMm3sNCObhhA+50LfrA"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "×5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 81, "g": 117, "b": 155, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91ObP0PBdK67JYZk0TeUD/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cbTCXSF9DLKLa4n9BbD6b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 66.7680665625, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "af3upqKClL15EZCtZMCkLH"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 83}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83eMqf4iRHq6eQKNHl6YQW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38j4hepZ9GB5moYtmuVWAo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 233, "height": 193}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01Y0/HUYJJYaP+NE00JfQl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 88}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f9697179-46f1-493f-9ecf-8fa855e5ca22@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fajtn8kexIUp5Nb/5ph3mS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 90}, "clickEvents": [{"__id__": 91}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a60FDsYTBL7K7ur+lknztm"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onSweepBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58NZTD2YhOhasec7KC3ain", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_common_b_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 94}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 120}, {"__id__": 122}], "_prefab": {"__id__": 125}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -594.833, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.3, "y": 1.3, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 26.848, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 133.28076171875, "height": 102.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5uBUhe7dIeLU3bdMPRECP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 98}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "开 始", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 55, "_fontSize": 55, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 75, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 176, "g": 94, "b": 27, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5vyeYQfJBTJivkRMgRqP3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9chrmjFMJFrIAmS8GyEqx7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [{"__id__": 101}, {"__id__": 107}], "_active": true, "_components": [{"__id__": 113}, {"__id__": 115}], "_prefab": {"__id__": 117}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -3.389, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "体力", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": -19.10205078125, "y": -24.855, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 23.803304, "height": 39.166667}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10/0fDGPBIr7zaSwpKo2dT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "35078e9d-9ad8-4640-8781-54788cf45942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bblouXRFZPUb5INO2nGEPV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2wjj+qVRMFJ+JvVMh6d2D", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [{"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": -7.20039878125, "y": -23.725, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 109}, "_contentSize": {"__type__": "cc.Size", "width": 38.2041015625, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8UYuLP+1OeKvWwwL5+7Nr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 111}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "×5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 176, "g": 94, "b": 27, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0860mF40RAgIocdXFziHRT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bd0lpbYi9Erp+/zSq5ug8Q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 62.0074055625, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bTaJJNyZLDJkeR9NgyO+t"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 100}, "_enabled": true, "__prefab": {"__id__": 116}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5XzNAWa5CYYcWowVT5s95"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3Z9QSzTpMy7YxTXUc/u/O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 119}, "_contentSize": {"__type__": "cc.Size", "width": 299, "height": 149}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbct0FLfFIDLBfH6mTGXsR"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 121}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f9338132-a819-4892-866d-ef1bf15fbb13@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "153OolZMlJZ7AZ627+p+5J"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 123}, "clickEvents": [{"__id__": 124}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34oBDZ3aVIv48CTq88XBxL"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onStartBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edVzBo4DBByqnR1W6NE1vp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": -0.2859999999999996, "y": -566.078, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 128}, "_contentSize": {"__type__": "cc.Size", "width": 486.73828125, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43psu1eGJHFKv24DmhDp/N"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 130}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 74, "g": 112, "b": 152, "a": 255}, "_string": "通关20章节后解锁", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23NhZ+i4lC8I/stltycdDj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52D0uSxGtJQYCOJZk1g138", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "宝箱", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 133}, {"__id__": 191}, {"__id__": 218}], "_active": true, "_components": [{"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 134}, {"__id__": 140}], "_active": true, "_components": [{"__id__": 188}], "_prefab": {"__id__": 190}, "_lpos": {"__type__": "cc.Vec3", "x": -273.099, "y": -406.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": 16.338, "y": -30.148, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 120.04296875, "height": 58.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "221WYRbAhMsoRk6CGQ3gLC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "第30层", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 63, "b": 97, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6W5hDAEdODab+HoCQOvR5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aew9YgkChJ1oDlZwhlFWVf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 133}, "_prefab": {"__id__": 141}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 140}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 142}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5135UXKrVMH6M6WFOxLlF5", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 143}], "mountedComponents": [{"__id__": 166}], "propertyOverrides": [{"__id__": 173}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}, {"__id__": 178}, {"__id__": 180}, {"__id__": 182}, {"__id__": 184}, {"__id__": 186}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 144}, "nodes": [{"__id__": 145}]}, {"__type__": "cc.TargetInfo", "localID": ["a85Qq7QftCR54nI3cuK3sY"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 146}, "_prefab": {"__id__": 162}, "__editorExtras__": {"mountedRoot": {"__id__": 140}}}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 147}, "_children": [{"__id__": 145}], "_active": true, "_components": [{"__id__": 153}, {"__id__": 155}, {"__id__": 157}, {"__id__": 159}], "_prefab": {"__id__": 161}, "_lpos": {"__type__": "cc.Vec3", "x": -0.04999999999998295, "y": 14, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "蓝气泡框", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 140}, "_children": [{"__id__": 146}], "_active": false, "_components": [{"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 113.539, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 422, "height": 182}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bHQJoaLVDbbdDAQu4VlZE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9c520907-4bf9-40b4-9604-875c1ed32ee4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fUFb4lkVNi6AcuufDQb5K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01zpWZegRDC7vtY2wn6dKd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 154}, "_contentSize": {"__type__": "cc.Size", "width": 117, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7+wkAazdN15MO2DtaJE2I"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 156}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 13, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20thZv9LZC5pIoJ2TXbFSg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 158}, "_alignFlags": 5, "_target": null, "_left": 160.95000000000002, "_right": 161.05, "_top": 17, "_bottom": 45, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a29AeVWuBA27Sg5I+eAdTC"}, {"__type__": "d07ccyF8WFPfo9WZbgP0CeG", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 160}, "itemPrefab": {"__uuid__": "1d8def44-6202-4334-ab96-8d481edfafce", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67Pme3XWZCYoxPFVXudR6u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a85Qq7QftCR54nI3cuK3sY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 145}, "asset": {"__uuid__": "1d8def44-6202-4334-ab96-8d481edfafce", "__expectedType__": "cc.Prefab"}, "fileId": "8axwSUbxdIYYTTS0nk5Tgn", "instance": {"__id__": 163}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "12ClJUmihAqpmQFsxv5amH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 164}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 149.552734375, "height": 58.4}}, {"__type__": "cc.TargetInfo", "localID": ["cc5moX9kxOLLXFgcvrXKD0"]}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 167}, "components": [{"__id__": 168}, {"__id__": 170}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "320c7ZHQVxOEYO2EMrmPf7W", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 140}}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 169}, "bg": {"__id__": 1}, "roundTitle": {"__id__": 137}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60q+EfX7FP+pNOb374xSW8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 140}}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 171}, "clickEvents": [{"__id__": 172}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f23m8GgRxKOrivdfJslCLx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 140}, "component": "", "_componentId": "320c7ZHQVxOEYO2EMrmPf7W", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_name"], "value": "treasureBox"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 71, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 174}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1.3, "y": 1.3, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["0fdg20YDVLUbhStHz4Zt98"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 117, "height": 120}}, {"__type__": "cc.TargetInfo", "localID": ["c7+wkAazdN15MO2DtaJE2I"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 189}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4+bmXPE1L9qHJ/TGMHgJj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a40HX+vp9M06TjuAdVAQ6e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 192}, {"__id__": 198}], "_active": true, "_components": [{"__id__": 215}], "_prefab": {"__id__": 217}, "_lpos": {"__type__": "cc.Vec3", "x": -0.647, "y": -406.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 191}, "_children": [], "_active": true, "_components": [{"__id__": 193}, {"__id__": 195}], "_prefab": {"__id__": 197}, "_lpos": {"__type__": "cc.Vec3", "x": 6.019, "y": -30.148, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 192}, "_enabled": true, "__prefab": {"__id__": 194}, "_contentSize": {"__type__": "cc.Size", "width": 120.04296875, "height": 58.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8BZRpysBMZ4syNOPXCE52"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 192}, "_enabled": true, "__prefab": {"__id__": 196}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "第30层", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 63, "b": 97, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eyCcLDrVLepxejmaIhG4r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "448E19v3FPIqdSuQP0SYQc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 191}, "_prefab": {"__id__": 199}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 198}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 200}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8fj9QUD+dAt6E2Zjbjr1ck", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 201}], "propertyOverrides": [{"__id__": 208}, {"__id__": 210}, {"__id__": 211}, {"__id__": 212}, {"__id__": 213}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 202}, "components": [{"__id__": 203}, {"__id__": 205}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "320c7ZHQVxOEYO2EMrmPf7W", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 198}}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 204}, "bg": {"__id__": 1}, "roundTitle": {"__id__": 195}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aewmNruDtOL7jW8sWJrLdh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 198}}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 206}, "clickEvents": [{"__id__": 207}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8HKZdh29MublhZ2jquezN"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 198}, "component": "", "_componentId": "320c7ZHQVxOEYO2EMrmPf7W", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 209}, "propertyPath": ["_name"], "value": "treasureBox"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 209}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 71, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 209}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 209}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 214}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1.3, "y": 1.3, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["0fdg20YDVLUbhStHz4Zt98"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 216}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63w2z5NipLj4n9YYMgfbYg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfk1lHP5lPwpsWBrV20fSz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [{"__id__": 219}, {"__id__": 225}], "_active": true, "_components": [{"__id__": 242}], "_prefab": {"__id__": 244}, "_lpos": {"__type__": "cc.Vec3", "x": 261.766, "y": -406.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 2.579, "y": -30.148, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 120.04296875, "height": 58.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9c4JBBTg1PqKHV2LvjmmIM"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "第30层", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 63, "b": 97, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0K13yVcxDVr5wV87olnAZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4TOMC6IBOfqk6KwyjklRB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 218}, "_prefab": {"__id__": 226}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 225}, "asset": {"__uuid__": "21a02b4d-241c-4339-af81-8ae9283b4e42", "__expectedType__": "cc.Prefab"}, "fileId": "19W2dna69JN6WfrsbTsiUi", "instance": {"__id__": 227}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7f7fwcq9JC3ZZ1d9l6NSCV", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 228}], "propertyOverrides": [{"__id__": 235}, {"__id__": 237}, {"__id__": 238}, {"__id__": 239}, {"__id__": 240}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 229}, "components": [{"__id__": 230}, {"__id__": 232}]}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "320c7ZHQVxOEYO2EMrmPf7W", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 225}}, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 231}, "bg": {"__id__": 1}, "roundTitle": {"__id__": 222}, "treasureBox": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0302stkdBF3qsu5wuhGn76"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 225}}, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 233}, "clickEvents": [{"__id__": 234}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56WcqlBUlJHKr8pNQM8Wnh"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 225}, "component": "", "_componentId": "320c7ZHQVxOEYO2EMrmPf7W", "handler": "onclick", "customEventData": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_name"], "value": "treasureBox"}, {"__type__": "cc.TargetInfo", "localID": ["19W2dna69JN6WfrsbTsiUi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 71, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1.3, "y": 1.3, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["0fdg20YDVLUbhStHz4Zt98"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 243}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3D+6s4uxC2r++KZ4kE/li"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "19PmwiLrhB5aBKzBChV9/Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 246}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98zJzB7tRGZ7MkR6R6zXwV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12wwItlvxKOqrxtakYVDSg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "标题", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}], "_prefab": {"__id__": 253}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 655.214, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 250}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 83.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eGI8eziFAWYFXMY0VpzC2"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 252}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "浅层矿洞", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 75, "_fontSize": 75, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 63, "b": 97, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5D9DQLGlB85ylY+OoNIQM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62WQBg28BHuLmy3rcJjfwn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "最高记录", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 255}, {"__id__": 257}], "_prefab": {"__id__": 259}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 556.399, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 256}, "_contentSize": {"__type__": "cc.Size", "width": 295.115234375, "height": 83.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03mzjNhmhAyLWj0waUf/w3"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 258}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "最高纪录: 第几层", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 33, "g": 63, "b": 97, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecZiPQNX1FXb/is7ip8HpT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42pbnNxWtImbabmF4eZSmV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "邀请有礼", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 261}], "_active": true, "_components": [{"__id__": 269}, {"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 276}, "_lpos": {"__type__": "cc.Vec3", "x": -428.207, "y": 621.466, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "提醒红点", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 260}, "_children": [], "_active": true, "_components": [{"__id__": 262}, {"__id__": 264}, {"__id__": 266}], "_prefab": {"__id__": 268}, "_lpos": {"__type__": "cc.Vec3", "x": 58.321, "y": 61.768, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 263}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2diOVr8ZJC/KFh1fDdz7t7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 265}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8douHk+6NNM53uW6T0JXYs"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 267}, "type": "MINE_SHARE_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3NDsYSkROh7yH4Zuro4Cj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "233TaSABZLoqjtKbk2PYGZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": {"__id__": 270}, "_contentSize": {"__type__": "cc.Size", "width": 184, "height": 174}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05wvQBqWZCaKBUPx48PYfv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": {"__id__": 272}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2e1dd594-f4ef-4f7b-8907-3a2658fb43ea@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "769flP6HxJ3pgc5QTeU2Js"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 260}, "_enabled": true, "__prefab": {"__id__": 274}, "clickEvents": [{"__id__": 275}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 260}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3BCwskeVLw4mO49067C8W"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onShareBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beXfX0ZdBC7aVvPt83RmNO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "游戏圈", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 278}], "_active": true, "_components": [{"__id__": 286}, {"__id__": 288}, {"__id__": 290}], "_prefab": {"__id__": 293}, "_lpos": {"__type__": "cc.Vec3", "x": -428.207, "y": 428.466, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "提醒红点", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 277}, "_children": [], "_active": true, "_components": [{"__id__": 279}, {"__id__": 281}, {"__id__": 283}], "_prefab": {"__id__": 285}, "_lpos": {"__type__": "cc.Vec3", "x": 58.321, "y": 61.768, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 278}, "_enabled": true, "__prefab": {"__id__": 280}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcU9trm1ZCVqu5+RyyiIxC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 278}, "_enabled": true, "__prefab": {"__id__": 282}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41OAiqPldCLYfeF93hVWzZ"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 278}, "_enabled": true, "__prefab": {"__id__": 284}, "type": "MINE_SHARE_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50MujaLZtA9o8kw0jxanX3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66kEOfKeBF4qQRBgf2+dVz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": {"__id__": 287}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 174}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfrmjbcKJOXqETK+7EAxRA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": {"__id__": 289}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4533073e-35b5-4fa6-90fb-a4e15ebb1202@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6+31HtsBKNaKoPyCtrkDQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 277}, "_enabled": true, "__prefab": {"__id__": 291}, "clickEvents": [{"__id__": 292}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 277}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5bQD2fp5JILovTTDdmsK5"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onGameCircleBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e3hcPzw9OzIgmch1GEHVf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "排行-选中", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 295}], "_active": false, "_components": [{"__id__": 303}, {"__id__": 305}, {"__id__": 307}], "_prefab": {"__id__": 309}, "_lpos": {"__type__": "cc.Vec3", "x": -427.726, "y": 233.293, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "提醒红点", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 294}, "_children": [], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "_prefab": {"__id__": 302}, "_lpos": {"__type__": "cc.Vec3", "x": 58.321, "y": 61.768, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3oWm3Zi1Kcbej1NOutVVW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 299}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efbnXCkaZHNbtyLIUhf3er"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 295}, "_enabled": true, "__prefab": {"__id__": 301}, "type": "MINE_SHARE_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2CKr/GNlKT6Q12mZtDFH5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77ywbh/uhBqrzFchBmuN8o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 304}, "_contentSize": {"__type__": "cc.Size", "width": 141, "height": 175}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fVfm0KH1ER7rxDy3msDh6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 306}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ee436dd4-e81b-4bbd-9990-d11a87350885@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c41jvR6C1HmLS8sMT53nqp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 294}, "_enabled": true, "__prefab": {"__id__": 308}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9lguyKFhA/KlyE6yukYI7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbQh3OKcVM6rxq9Y9fXwPx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "发光", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 311}], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 450.933, "y": 575.261, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "签到", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 310}, "_children": [{"__id__": 312}], "_active": true, "_components": [{"__id__": 320}, {"__id__": 322}, {"__id__": 324}], "_prefab": {"__id__": 327}, "_lpos": {"__type__": "cc.Vec3", "x": 8, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 311}, "_children": [], "_active": true, "_components": [{"__id__": 313}, {"__id__": 315}, {"__id__": 317}], "_prefab": {"__id__": 319}, "_lpos": {"__type__": "cc.Vec3", "x": 46.124, "y": 50.216, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": {"__id__": 314}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aVcEVWZNFQqpNqGDogwd1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": {"__id__": 316}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52+3q2OXJJH6QScnMqOGEl"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 312}, "_enabled": true, "__prefab": {"__id__": 318}, "type": "MINE_CHECKIN_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dlG6ca3FA5YZiF0h7sOhH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67Wlpg4HFJKJZ6dRVc/N2C", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 321}, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 159}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1HpU8uzxC0qdloSB4fmx0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 323}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73230169-7ba5-46b4-97f6-4235b8d0e1cd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8wLH/z9VErLaayU3iApLo"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 325}, "clickEvents": [{"__id__": 326}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 311}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79I2mCnd9MG7r9AuSF6Cpu"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onCheckInBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5jQjwygtLupl4NicOaa9Q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 310}, "_enabled": true, "__prefab": {"__id__": 329}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 198}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21977aXpJACYdfFlzIDac/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 310}, "_enabled": true, "__prefab": {"__id__": 331}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf2c4b25-0731-487f-b303-3929263b6a03@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95V9FWlNRBtbACGWyrL0Oc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "332YuDqf1IvbYH+w8MJ8cy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "发光-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 334}], "_active": true, "_components": [{"__id__": 351}, {"__id__": 353}], "_prefab": {"__id__": 355}, "_lpos": {"__type__": "cc.Vec3", "x": 450.933, "y": 394.283, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "任务", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 333}, "_children": [{"__id__": 335}], "_active": true, "_components": [{"__id__": 343}, {"__id__": 345}, {"__id__": 347}], "_prefab": {"__id__": 350}, "_lpos": {"__type__": "cc.Vec3", "x": 7, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 334}, "_children": [], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}, {"__id__": 340}], "_prefab": {"__id__": 342}, "_lpos": {"__type__": "cc.Vec3", "x": 42.857, "y": 47.143, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bGZToF9dMO7TOCd2zVvk7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 339}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cKW2ddptD1JKo00E6lp/E"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 335}, "_enabled": true, "__prefab": {"__id__": 341}, "type": "MINE_TASK_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cCJpH4O9HdphXWFsKFevA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6FW8X3PxGv6JgdnoeRad5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 334}, "_enabled": true, "__prefab": {"__id__": 344}, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3uK9TdNhJj5KO+HKwET2O"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 334}, "_enabled": true, "__prefab": {"__id__": 346}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "64aea241-0d26-45d0-9f6a-d1506cd8197e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "775G+/9jtMtbWdFfA1uM+S"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 334}, "_enabled": true, "__prefab": {"__id__": 348}, "clickEvents": [{"__id__": 349}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 334}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42qicESFpKbY9hPysuBpxK"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onMissionBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e55zTJHkVK+qfoEVyrXH4N", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": {"__id__": 352}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 198}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7XHI6ZPNGlKukLKMfbqn3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 333}, "_enabled": true, "__prefab": {"__id__": 354}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf2c4b25-0731-487f-b303-3929263b6a03@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eimXgW3dMHpPQu9PGAOp0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffEFs7ksNHjLnBLr7OIMWl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "发光-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 357}, {"__id__": 366}], "_active": true, "_components": [{"__id__": 374}, {"__id__": 376}], "_prefab": {"__id__": 378}, "_lpos": {"__type__": "cc.Vec3", "x": 450.111, "y": 214.029, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "转盘", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 358}, {"__id__": 360}, {"__id__": 362}], "_prefab": {"__id__": 365}, "_lpos": {"__type__": "cc.Vec3", "x": 7, "y": 1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 359}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "245DJMmhRD7YTtljpsU/3T"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 361}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "acb5e98a-a5e0-4e5b-98f2-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14bftB1DlNcKcpRVrNUBad"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 363}, "clickEvents": [{"__id__": 364}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 357}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71K8vqPNNOEbF5rBZHA/j0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9ff0dj2bpxKpZCs3sFMfQRs", "handler": "onGamblingBtnClick", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8H4eACY5KqLl8MDnnR4JP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "提醒标志", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 356}, "_children": [], "_active": true, "_components": [{"__id__": 367}, {"__id__": 369}, {"__id__": 371}], "_prefab": {"__id__": 373}, "_lpos": {"__type__": "cc.Vec3", "x": 48, "y": 56, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 368}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1+GIWzE9L+pnTUqAlAxjo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 370}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e8c106c8-76af-4fad-9b30-2a727f4b39c4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40eo3fI19B0oMuHhCyamDV"}, {"__type__": "06781PPa0RPNqe/INfo6Yta", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 372}, "type": "MINE_WHEEL_ICON", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d88ojFhgZB8ZZvUpArDUsa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cJvf8xoVFgalS4VBuvuBO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 375}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 198}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b/pJEPt5AKJgR0vKj8Bjk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 356}, "_enabled": true, "__prefab": {"__id__": 377}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cf2c4b25-0731-487f-b303-3929263b6a03@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44QUqLfR1F2qvllts1zc0L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28uKLN4idPuprJcAE7PA91", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 380}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920.0000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3Ngxmz55FSJHwuaAmLKGE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 382}, "_alignFlags": 5, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 1684.736, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d04hfkaYpP57GTbOhy5y8j"}, {"__type__": "9ff0dj2bpxKpZCs3sFMfQRs", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 384}, "start_btn": {"__id__": 93}, "lockLabel": {"__id__": 129}, "sweep_btn": {"__id__": 60}, "stageNameLabel": {"__id__": 251}, "stageMaxRoundLabel": {"__id__": 257}, "rewardBoxs": [{"__id__": 168}, {"__id__": 203}, {"__id__": 230}], "lastStageNode": {"__id__": 20}, "nextStageNode": {"__id__": 37}, "stageIcon": {"__id__": 11}, "stageIconLock": {"__id__": 6}, "snowman": {"__id__": 17}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2CbGuS/hKJbQraGMrGp9O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99nHTmqWlKWJQ88Rygc9hY", "instance": null, "targetOverrides": [{"__id__": 386}, {"__id__": 389}, {"__id__": 392}, {"__id__": 395}, {"__id__": 398}, {"__id__": 401}, {"__id__": 404}, {"__id__": 407}, {"__id__": 410}, {"__id__": 413}, {"__id__": 416}, {"__id__": 419}, {"__id__": 422}, {"__id__": 425}, {"__id__": 428}, {"__id__": 431}, {"__id__": 434}, {"__id__": 437}, {"__id__": 440}, {"__id__": 442}, {"__id__": 444}, {"__id__": 446}, {"__id__": 448}, {"__id__": 450}], "nestedPrefabInstanceRoots": [{"__id__": 225}, {"__id__": 198}, {"__id__": 140}, {"__id__": 145}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 387}, "propertyPath": ["close"], "target": {"__id__": 140}, "targetInfo": {"__id__": 388}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 390}, "propertyPath": ["open"], "target": {"__id__": 140}, "targetInfo": {"__id__": 391}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 393}, "propertyPath": ["receive"], "target": {"__id__": 140}, "targetInfo": {"__id__": 394}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 396}, "propertyPath": ["reddot"], "target": {"__id__": 140}, "targetInfo": {"__id__": 397}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 399}, "propertyPath": ["tip"], "target": {"__id__": 140}, "targetInfo": {"__id__": 400}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 140}, "sourceInfo": {"__id__": 402}, "propertyPath": ["tipItems"], "target": {"__id__": 140}, "targetInfo": {"__id__": 403}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["67Pme3XWZCYoxPFVXudR6u"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 405}, "propertyPath": ["close"], "target": {"__id__": 198}, "targetInfo": {"__id__": 406}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 408}, "propertyPath": ["open"], "target": {"__id__": 198}, "targetInfo": {"__id__": 409}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 411}, "propertyPath": ["receive"], "target": {"__id__": 198}, "targetInfo": {"__id__": 412}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 414}, "propertyPath": ["reddot"], "target": {"__id__": 198}, "targetInfo": {"__id__": 415}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 417}, "propertyPath": ["tip"], "target": {"__id__": 198}, "targetInfo": {"__id__": 418}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 198}, "sourceInfo": {"__id__": 420}, "propertyPath": ["tipItems"], "target": {"__id__": 198}, "targetInfo": {"__id__": 421}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["67Pme3XWZCYoxPFVXudR6u"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 423}, "propertyPath": ["close"], "target": {"__id__": 225}, "targetInfo": {"__id__": 424}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["faPGNJ+QxPxqvGoyuranKy"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 426}, "propertyPath": ["open"], "target": {"__id__": 225}, "targetInfo": {"__id__": 427}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["31kj1d/ylFBq6irdO3nhRI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 429}, "propertyPath": ["receive"], "target": {"__id__": 225}, "targetInfo": {"__id__": 430}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["d3gZ80QvVPMqwG9VNroViX"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 432}, "propertyPath": ["reddot"], "target": {"__id__": 225}, "targetInfo": {"__id__": 433}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["caGGeRcWFEKLvnzdTYGzZD"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 435}, "propertyPath": ["tip"], "target": {"__id__": 225}, "targetInfo": {"__id__": 436}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["01zpWZegRDC7vtY2wn6dKd"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 225}, "sourceInfo": {"__id__": 438}, "propertyPath": ["tipItems"], "target": {"__id__": 225}, "targetInfo": {"__id__": 439}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetInfo", "localID": ["67Pme3XWZCYoxPFVXudR6u"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 140}, "targetInfo": {"__id__": 441}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 198}, "targetInfo": {"__id__": 443}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 225}, "targetInfo": {"__id__": 445}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 168}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 140}, "targetInfo": {"__id__": 447}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 198}, "targetInfo": {"__id__": 449}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 230}, "sourceInfo": null, "propertyPath": ["treasureBox"], "target": {"__id__": 225}, "targetInfo": {"__id__": 451}}, {"__type__": "cc.TargetInfo", "localID": ["f0X7Ur7dxKX6Na1p86JDHI"]}]