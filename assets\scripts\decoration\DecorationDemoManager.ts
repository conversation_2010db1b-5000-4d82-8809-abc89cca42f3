/**
 * 装饰演示管理器
 * 整合家具摆放、验证、评分、随机变化等功能
 */

import { Component, Vec2, _decorator } from "cc";
import {
    PlacedFurniture,
    FurnitureTemplate,
    FurnitureType,
    FurnitureTheme,
    Rotation,
    RoomScoreDetails
} from "./DecorationDefine";
import { FurniturePlacementValidator, ValidationResult } from "./FurniturePlacementValidator";
import { FurnitureRandomizer, RandomizeResult } from "./FurnitureRandomizer";
import { RoomScoreCalculator } from "./RoomScoreCalculator";
import { RoomGrid } from "./RoomGrid";
import { RoomLayoutManager, LayoutOperationResult } from "./RoomLayoutManager";
import { SavedRoomLayout, RoomLayoutStorage } from "./RoomLayoutStorage";

const { ccclass, property } = _decorator;

/**
 * 演示步骤
 */
export interface DemoStep {
    title: string;
    description: string;
    furnitures: PlacedFurniture[];
}

/**
 * 演示状态
 */
export enum DemoState {
    Idle = "idle",
    Playing = "playing",
    Randomizing = "randomizing",
    StepChanging = "step_changing"
}

@ccclass('DecorationDemoManager')
export class DecorationDemoManager extends Component {
    @property({ type: Vec2, tooltip: "房间尺寸" })
    roomSize: Vec2 = new Vec2(12, 8);

    @property({ tooltip: "自动播放间隔(秒)" })
    autoPlayInterval: number = 3;

    @property({ tooltip: "家具摆放动画间隔(毫秒)" })
    placementInterval: number = 300;

    // 核心组件
    private roomGrid!: RoomGrid;
    private validator!: FurniturePlacementValidator;
    private randomizer!: FurnitureRandomizer;
    private scoreCalculator!: RoomScoreCalculator;
    private layoutManager!: RoomLayoutManager;

    // 演示数据
    private demoSteps: DemoStep[] = [];
    private furnitureTemplates: Map<number, FurnitureTemplate> = new Map();
    private currentStep: number = 0;
    private currentState: DemoState = DemoState.Idle;

    // 事件回调
    private onStepChanged?: (step: number, stepData: DemoStep) => void;
    private onScoreUpdated?: (score: RoomScoreDetails) => void;
    private onStateChanged?: (state: DemoState) => void;
    private onFurniturePlaced?: (furniture: PlacedFurniture) => void;
    private onLayoutSaved?: (result: LayoutOperationResult) => void;
    private onLayoutLoaded?: (result: LayoutOperationResult) => void;

    protected onLoad() {
        this.initializeComponents();
        this.setupDemoData();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 创建默认的全空房间布局
        const defaultLayout = this.createDefaultLayout();
        this.roomGrid = new RoomGrid(new Vec2(defaultLayout[0].length, defaultLayout.length), defaultLayout);

        this.validator = new FurniturePlacementValidator(this.roomGrid, []);
        this.randomizer = new FurnitureRandomizer(this.roomGrid);
        this.scoreCalculator = new RoomScoreCalculator();

        // 初始化布局管理器
        this.layoutManager = this.node.addComponent(RoomLayoutManager);
        this.setupLayoutManagerCallbacks();
    }

    /**
     * 创建默认房间布局
     * 使用新的格子属性定义：
     * 0: 空地（可摆放）
     * 1: 邻接障碍或墙壁的格子（可摆放，挂饰类家具只能摆放在这里）
     * 2: 障碍（不可摆放）
     */
    private createDefaultLayout(): number[][] {
        // 使用用户提供的房间布局
        return [
            [2,2,2,1,1,1,1,2,2,2],
            [2,2,1,0,0,0,0,1,2,2],
            [2,1,0,0,0,0,0,0,1,2],
            [1,0,0,0,0,0,0,0,0,1],
            [1,0,0,0,0,0,0,0,0,1],
            [1,0,0,0,0,0,0,0,0,1],
            [1,0,0,0,0,0,0,0,0,1],
            [2,1,0,0,0,0,0,0,1,2],
            [2,2,1,0,0,0,0,1,2,2],
            [2,2,2,1,1,1,1,2,2,2]
        ];
    }

    /**
     * 设置演示数据
     */
    setupDemoData(): void {
        // 创建家具模板
        this.createFurnitureTemplates();
        
        // 创建演示步骤
        this.createDemoSteps();
    }

    /**
     * 创建家具模板
     */
    private createFurnitureTemplates(): void {
        // 椅子模板
        this.furnitureTemplates.set(1, {
            id: 1,
            name: "现代椅子",
            type: FurnitureType.Small,
            baseSize: new Vec2(1, 1),
            spriteFrame: "texture/furniture/modern_chair",
            description: "简洁现代的椅子，舒适实用",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 1,
                value: 8,
                beauty: 6
            }
        });

        // 床铺模板
        this.furnitureTemplates.set(2, {
            id: 2,
            name: "古典床铺",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "texture/furniture/classic_bed",
            description: "优雅的古典风格床铺",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 2,
                value: 15,
                beauty: 8
            }
        });

        // 桌子模板
        this.furnitureTemplates.set(3, {
            id: 3,
            name: "工业桌子",
            type: FurnitureType.Large,
            baseSize: new Vec2(2, 2),
            spriteFrame: "texture/furniture/industrial_table",
            description: "粗犷的工业风格桌子",
            properties: {
                theme: FurnitureTheme.Industrial,
                level: 2,
                value: 12,
                beauty: 5
            }
        });

        // 沙发模板
        this.furnitureTemplates.set(4, {
            id: 4,
            name: "现代沙发",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "texture/furniture/modern_sofa",
            description: "舒适的现代风格沙发",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 3,
                value: 20,
                beauty: 10
            }
        });

        // 书架模板
        this.furnitureTemplates.set(5, {
            id: 5,
            name: "自然书架",
            type: FurnitureType.Medium,
            baseSize: new Vec2(1, 2),
            spriteFrame: "texture/furniture/natural_bookshelf",
            description: "天然材质的书架",
            properties: {
                theme: FurnitureTheme.Natural,
                level: 2,
                value: 18,
                beauty: 7
            }
        });

        // 壁画模板
        this.furnitureTemplates.set(6, {
            id: 6,
            name: "古典壁画",
            type: FurnitureType.WallDecoration,
            baseSize: new Vec2(1, 1),
            spriteFrame: "texture/furniture/classic_painting",
            description: "典雅的古典壁画",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 3,
                value: 25,
                beauty: 15,
                isWallDecoration: true
            }
        });

        // 照片墙模板
        this.furnitureTemplates.set(7, {
            id: 7,
            name: "现代照片墙",
            type: FurnitureType.WallDecoration,
            baseSize: new Vec2(1, 1),
            spriteFrame: "texture/furniture/modern_photo_wall",
            description: "现代风格的照片墙",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 2,
                value: 12,
                beauty: 12,
                isWallDecoration: true
            }
        });
    }

    /**
     * 创建演示步骤
     */
    private createDemoSteps(): void {
        this.demoSteps = [
            {
                title: "空房间",
                description: "初始状态的空房间",
                furnitures: []
            },
            {
                title: "放置椅子",
                description: "在房间中央放置现代风格椅子",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0)
                ]
            },
            {
                title: "添加床铺",
                description: "添加古典风格床铺",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0),
                    this.createPlacedFurniture(2, new Vec2(3, 2), Rotation.Deg0)
                ]
            },
            {
                title: "放置沙发",
                description: "添加现代风格沙发",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0),
                    this.createPlacedFurniture(2, new Vec2(3, 2), Rotation.Deg0),
                    this.createPlacedFurniture(4, new Vec2(7, 2), Rotation.Deg0)
                ]
            },
            {
                title: "添加桌子",
                description: "添加工业风格桌子",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0),
                    this.createPlacedFurniture(2, new Vec2(3, 2), Rotation.Deg0),
                    this.createPlacedFurniture(4, new Vec2(7, 2), Rotation.Deg0),
                    this.createPlacedFurniture(3, new Vec2(6, 5), Rotation.Deg0)
                ]
            },
            {
                title: "添加书架",
                description: "添加自然风格书架",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0),
                    this.createPlacedFurniture(2, new Vec2(3, 2), Rotation.Deg0),
                    this.createPlacedFurniture(4, new Vec2(7, 2), Rotation.Deg0),
                    this.createPlacedFurniture(3, new Vec2(6, 5), Rotation.Deg0),
                    this.createPlacedFurniture(5, new Vec2(9, 3), Rotation.Deg0)
                ]
            },
            {
                title: "添加挂饰",
                description: "添加贴墙挂饰装饰",
                furnitures: [
                    this.createPlacedFurniture(1, new Vec2(4, 4), Rotation.Deg0),
                    this.createPlacedFurniture(2, new Vec2(3, 2), Rotation.Deg0),
                    this.createPlacedFurniture(4, new Vec2(7, 2), Rotation.Deg0),
                    this.createPlacedFurniture(3, new Vec2(6, 5), Rotation.Deg0),
                    this.createPlacedFurniture(5, new Vec2(9, 3), Rotation.Deg0),
                    this.createPlacedFurniture(6, new Vec2(2, 2), Rotation.Deg0),
                    this.createPlacedFurniture(7, new Vec2(10, 1), Rotation.Deg0)
                ]
            }
        ];
    }

    /**
     * 创建已摆放家具
     */
    private createPlacedFurniture(templateId: number, position: Vec2, rotation: Rotation): PlacedFurniture {
        const template = this.furnitureTemplates.get(templateId);
        if (!template) {
            throw new Error(`找不到家具模板: ${templateId}`);
        }

        const size = rotation === Rotation.Deg90 || rotation === Rotation.Deg270
            ? new Vec2(template.baseSize.y, template.baseSize.x)
            : new Vec2(template.baseSize.x, template.baseSize.y);

        return {
            id: `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            templateId,
            position: new Vec2(position.x, position.y),
            rotation,
            currentSize: size,
            placedTime: Date.now()
        };
    }

    /**
     * 验证家具摆放
     */
    validateFurniturePlacement(
        templateId: number,
        position: Vec2,
        rotation: Rotation,
        excludeFurnitureId?: string
    ): ValidationResult {
        const template = this.furnitureTemplates.get(templateId);
        if (!template) {
            return {
                isValid: false,
                reason: "找不到家具模板"
            };
        }

        const currentFurnitures = this.getCurrentStepFurnitures();
        this.validator = new FurniturePlacementValidator(this.roomGrid, currentFurnitures);
        
        return this.validator.validatePlacement(template, position, rotation, excludeFurnitureId);
    }

    /**
     * 随机变化家具
     */
    randomizeFurniture(furnitureId: string): RandomizeResult | null {
        const currentFurnitures = this.getCurrentStepFurnitures();
        const targetFurniture = currentFurnitures.find(f => f.id === furnitureId);
        
        if (!targetFurniture) {
            return null;
        }

        const template = this.furnitureTemplates.get(targetFurniture.templateId);
        if (!template) {
            return null;
        }

        return this.randomizer.randomizeFurniture(targetFurniture, template, currentFurnitures);
    }

    /**
     * 计算当前步骤评分
     */
    calculateCurrentScore(): RoomScoreDetails {
        const currentFurnitures = this.getCurrentStepFurnitures();
        return this.scoreCalculator.calculateRoomScore(currentFurnitures, this.furnitureTemplates, this.roomSize);
    }

    /**
     * 获取当前步骤家具
     */
    getCurrentStepFurnitures(): PlacedFurniture[] {
        if (this.currentStep >= 0 && this.currentStep < this.demoSteps.length) {
            return [...this.demoSteps[this.currentStep].furnitures];
        }
        return [];
    }

    /**
     * 切换到指定步骤
     */
    goToStep(stepIndex: number): void {
        if (stepIndex < 0 || stepIndex >= this.demoSteps.length) {
            return;
        }

        this.currentStep = stepIndex;
        this.setState(DemoState.StepChanging);
        
        const stepData = this.demoSteps[stepIndex];
        this.onStepChanged?.(stepIndex, stepData);
        
        // 计算并更新评分
        setTimeout(() => {
            const score = this.calculateCurrentScore();
            this.onScoreUpdated?.(score);
            this.setState(DemoState.Idle);
        }, this.placementInterval * stepData.furnitures.length + 200);
    }

    /**
     * 下一步
     */
    nextStep(): void {
        if (this.currentState !== DemoState.Idle) return;
        
        if (this.currentStep < this.demoSteps.length - 1) {
            this.goToStep(this.currentStep + 1);
        }
    }

    /**
     * 上一步
     */
    prevStep(): void {
        if (this.currentState !== DemoState.Idle) return;
        
        if (this.currentStep > 0) {
            this.goToStep(this.currentStep - 1);
        }
    }

    /**
     * 重置演示
     */
    resetDemo(): void {
        if (this.currentState !== DemoState.Idle) return;
        
        this.goToStep(0);
    }

    /**
     * 设置状态
     */
    private setState(state: DemoState): void {
        if (this.currentState !== state) {
            this.currentState = state;
            this.onStateChanged?.(state);
        }
    }

    // 事件回调设置方法
    setOnStepChanged(callback: (step: number, stepData: DemoStep) => void): void {
        this.onStepChanged = callback;
    }

    setOnScoreUpdated(callback: (score: RoomScoreDetails) => void): void {
        this.onScoreUpdated = callback;
    }

    setOnStateChanged(callback: (state: DemoState) => void): void {
        this.onStateChanged = callback;
    }

    setOnFurniturePlaced(callback: (furniture: PlacedFurniture) => void): void {
        this.onFurniturePlaced = callback;
    }

    /**
     * 设置布局管理器回调
     */
    private setupLayoutManagerCallbacks(): void {
        if (!this.layoutManager) return;

        this.layoutManager.setOnLayoutSaved((result: LayoutOperationResult) => {
            this.onLayoutSaved?.(result);
        });

        this.layoutManager.setOnLayoutLoaded((result: LayoutOperationResult) => {
            this.onLayoutLoaded?.(result);
            // 加载完成后重新计算评分
            if (result.success) {
                setTimeout(() => {
                    const score = this.calculateCurrentScore();
                    this.onScoreUpdated?.(score);
                }, 100);
            }
        });
    }

    /**
     * 快速保存当前布局
     */
    async quickSaveCurrentLayout(name?: string): Promise<LayoutOperationResult> {
        if (!this.layoutManager) {
            return {
                success: false,
                message: "布局管理器未初始化"
            };
        }

        const score = this.calculateCurrentScore();
        return await this.layoutManager.quickSave(name, {
            autoGenerateName: !name,
            includeScore: true,
            includeThumbnail: false,
            tags: [this.getCurrentStepName()]
        });
    }

    /**
     * 保存当前布局
     */
    async saveCurrentLayout(
        name: string,
        description?: string,
        tags?: string[]
    ): Promise<LayoutOperationResult> {
        if (!this.layoutManager) {
            return {
                success: false,
                message: "布局管理器未初始化"
            };
        }

        const score = this.calculateCurrentScore();
        return await this.layoutManager.quickSave(name, {
            includeScore: true,
            includeThumbnail: false,
            tags: tags || [this.getCurrentStepName()]
        });
    }

    /**
     * 加载指定布局
     */
    async loadLayout(layoutId: string): Promise<LayoutOperationResult> {
        if (!this.layoutManager) {
            return {
                success: false,
                message: "布局管理器未初始化"
            };
        }

        // 暂停当前演示
        this.setState(DemoState.Idle);

        return await this.layoutManager.loadLayout(layoutId);
    }

    /**
     * 获取保存的布局列表
     */
    async getSavedLayouts(): Promise<SavedRoomLayout[]> {
        if (!this.layoutManager) {
            return [];
        }

        return await this.layoutManager.getRecentLayouts(20);
    }

    /**
     * 删除指定布局
     */
    async deleteLayout(layoutId: string): Promise<boolean> {
        if (!this.layoutManager) {
            return false;
        }

        return await this.layoutManager.deleteLayout(layoutId);
    }

    /**
     * 导出当前布局
     */
    async exportCurrentLayout(): Promise<string | null> {
        if (!this.layoutManager) {
            return null;
        }

        // 先保存当前布局
        const saveResult = await this.quickSaveCurrentLayout("临时导出");
        if (!saveResult.success || !saveResult.layoutId) {
            return null;
        }

        // 导出布局
        const exportData = await this.layoutManager.exportLayout(saveResult.layoutId);

        // 删除临时保存的布局
        await this.layoutManager.deleteLayout(saveResult.layoutId);

        return exportData;
    }

    /**
     * 导入布局
     */
    async importLayout(jsonData: string, name?: string): Promise<LayoutOperationResult> {
        if (!this.layoutManager) {
            return {
                success: false,
                message: "布局管理器未初始化"
            };
        }

        return await this.layoutManager.importLayout(jsonData, name);
    }

    /**
     * 获取当前步骤名称
     */
    private getCurrentStepName(): string {
        if (this.currentStep >= 0 && this.currentStep < this.demoSteps.length) {
            return this.demoSteps[this.currentStep].title;
        }
        return "未知步骤";
    }

    /**
     * 从保存的布局创建演示步骤
     */
    async createStepFromLayout(layoutId: string, stepTitle?: string): Promise<boolean> {
        if (!this.layoutManager) {
            return false;
        }

        try {
            const savedLayout = await RoomLayoutStorage.getInstance().loadLayout(layoutId);
            if (!savedLayout) {
                return false;
            }

            // 转换保存的家具数据为PlacedFurniture格式
            const furnitures: PlacedFurniture[] = savedLayout.furnitures.map(f =>
                this.createPlacedFurniture(f.templateId, new Vec2(f.position.x, f.position.y), f.rotation)
            );

            // 创建新的演示步骤
            const newStep: DemoStep = {
                title: stepTitle || savedLayout.name,
                description: savedLayout.description || `从布局 "${savedLayout.name}" 创建`,
                furnitures
            };

            // 添加到演示步骤列表
            this.demoSteps.push(newStep);

            console.log(`从布局 "${savedLayout.name}" 创建了新的演示步骤`);
            return true;

        } catch (error) {
            console.error("从布局创建步骤失败:", error);
            return false;
        }
    }

    /**
     * 获取布局管理器
     */
    getLayoutManager(): RoomLayoutManager | null {
        return this.layoutManager;
    }

    // 事件回调设置方法（新增）
    setOnLayoutSaved(callback: (result: LayoutOperationResult) => void): void {
        this.onLayoutSaved = callback;
    }

    setOnLayoutLoaded(callback: (result: LayoutOperationResult) => void): void {
        this.onLayoutLoaded = callback;
    }

    // Getter方法
    getCurrentStep(): number { return this.currentStep; }
    getCurrentState(): DemoState { return this.currentState; }
    getDemoSteps(): DemoStep[] { return [...this.demoSteps]; }
    getFurnitureTemplates(): Map<number, FurnitureTemplate> { return new Map(this.furnitureTemplates); }
}
