[{"type": 1, "rarity": 2, "order": 1, "seat": 5, "width": 83, "rankRatio": [1, 1.35, 1.8, 2.2, 3.5], "rankRangeValues": ["3:2", "3:3", "4:3", "4:4", "5:4"], "desc": "扔出炸弹攻击范围内的矿石", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [30, 40, 52, 69, 91, 120, 159, 209, 277, 365, 482], "levelSpeed": [0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 20101}, {"level": 2, "entryId": 20102}, {"level": 3, "entryId": 20103}, {"level": 4, "entryId": 20104}], "rankResName": ["炸弹", "炸弹2", "炸弹3", "炸弹4", "炸弹5"], "levelUpItemId": 1001, "showInfos": [{"type": "范围", "title": "范围", "value": "附近"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 2, "rarity": 1, "order": 2, "seat": 4, "width": 104, "rankRatio": [1, 1, 1.2, 1.5, 2.4], "rankRangeValues": ["1", "2", "3", "4", "5"], "desc": "召唤钻机攻击整列矿石", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [15, 20, 25, 33, 43, 56, 72, 94, 122, 159, 207], "levelSpeed": [0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16, 0.16], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 1, "entryId": 20201}, {"level": 2, "entryId": 20202}, {"level": 3, "entryId": 20203}, {"level": 4, "entryId": 20204}], "rankResName": ["竖钻机", "竖钻机2", "竖钻机3", "竖钻机4", "竖钻机5"], "levelUpItemId": 1002, "showInfos": [{"type": "范围", "title": "范围", "value": "整列"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 3, "rarity": 1, "order": 3, "seat": 2, "width": 140, "rankRatio": [1, 1, 1.2, 1.5, 2.4], "rankRangeValues": ["1", "2", "3", "4", "5"], "desc": "召唤钻机攻击整行矿石", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [18, 23, 30, 40, 51, 67, 87, 113, 147, 191, 248], "levelSpeed": [0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22, 0.22], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 1, "entryId": 20301}, {"level": 2, "entryId": 20302}, {"level": 3, "entryId": 20303}, {"level": 4, "entryId": 20304}], "rankResName": ["横钻机", "横钻机2", "横钻机3", "横钻机4", "横钻机5"], "levelUpItemId": 1003, "showInfos": [{"type": "范围", "title": "范围", "value": "整行"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 4, "rarity": 1, "order": 4, "seat": 1, "width": 113, "rankRatio": [1, 1.35, 1.8, 2.4, 4], "rankRangeValues": ["1:3", "2:4", "3:5", "4:6", "5:7"], "desc": "随机攻击多个矿石", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [10, 13, 17, 22, 29, 37, 48, 63, 82, 106, 138], "levelSpeed": [0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 1, "entryId": 20401}, {"level": 2, "entryId": 20402}, {"level": 3, "entryId": 20403}, {"level": 4, "entryId": 20404}], "rankResName": ["激光笔", "激光笔2", "激光笔3", "激光笔4", "激光笔5"], "levelUpItemId": 1004, "showInfos": [{"type": "范围", "title": "范围", "value": "随机"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 5, "rarity": 2, "order": 5, "seat": 4, "width": 98, "rankRatio": [1, 2, 2, 3, 4], "rankRangeValues": ["1", "1", "2", "2", "3"], "desc": "射出可以穿透矿石的冰锥", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [40, 53, 70, 92, 121, 160, 212, 279, 369, 487, 642], "levelSpeed": [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 20501}, {"level": 2, "entryId": 20502}, {"level": 3, "entryId": 20503}, {"level": 4, "entryId": 20504}], "rankResName": ["冰冻枪", "冰冻枪2", "冰冻枪3", "冰冻枪4", "冰冻枪5"], "levelUpItemId": 1005, "showInfos": [{"type": "范围", "title": "范围", "value": "向下"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 6, "rarity": 3, "order": 6, "seat": 4, "width": 98, "rankRatio": [1, 2, 2.4, 3.6, 4.8], "rankRangeValues": ["2:2", "2:2", "3:2", "3:2", "3:3"], "desc": "高伤害的小范围攻击", "unlockTip": "通过第10关", "equipEffect": 1, "levelEffectValue": [45, 61, 82, 111, 149, 202, 272, 368, 496, 670, 905], "levelSpeed": [0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2], "levelUpCost": [1000, 1500, 2500, 5000, 7500, 11000, 15000, 20000, 25000, 32500], "levelUpPiece": [2, 3, 5, 10, 15, 22, 30, 40, 50, 65], "levelUnlockEntry": [{"level": 1, "entryId": 20601}, {"level": 2, "entryId": 20602}, {"level": 3, "entryId": 20603}, {"level": 4, "entryId": 20604}], "rankResName": ["时光布", "时光布2", "时光布3", "时光布4", "时光布5"], "levelUpItemId": 1006, "showInfos": [{"type": "范围", "title": "范围", "value": "附近"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 7, "rarity": 2, "order": 7, "seat": 1, "width": 101, "rankRatio": [1, 1, 1.2, 1.5, 2.4], "rankRangeValues": ["1", "2", "3", "4", "5"], "desc": "从高空掉落可以反弹多次的小球", "unlockTip": "邀请好友解锁", "equipEffect": 1, "levelEffectValue": [8, 11, 14, 18, 24, 32, 42, 56, 74, 97, 128], "levelSpeed": [0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 20701}, {"level": 2, "entryId": 20702}, {"level": 3, "entryId": 20703}, {"level": 4, "entryId": 20704}], "rankResName": ["弹弹球", "弹弹球2", "弹弹球3", "弹弹球4", "弹弹球5"], "levelUpItemId": 1007, "showInfos": [{"type": "范围", "title": "范围", "value": "碰撞"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 8, "rarity": 2, "order": 8, "seat": 2, "width": 150, "rankRatio": [1, 1.5, 2.15, 3, 5], "rankRangeValues": ["3:5", "4:5", "5:5", "6:5", "7:5"], "desc": "扔出多个持续一段时间的反弹小球", "unlockTip": "通过第2关", "equipEffect": 1, "levelEffectValue": [10, 13, 17, 23, 30, 40, 53, 70, 92, 122, 161], "levelSpeed": [0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18, 0.18], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 20801}, {"level": 2, "entryId": 20802}, {"level": 3, "entryId": 20803}, {"level": 4, "entryId": 20804}], "rankResName": ["橡皮球", "橡皮球2", "橡皮球3", "橡皮球4", "橡皮球5"], "levelUpItemId": 1008, "showInfos": [{"type": "范围", "title": "范围", "value": "碰撞"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 9, "rarity": 1, "order": 9, "seat": 1, "width": 117, "rankRatio": [1, 2, 2, 3, 4], "rankRangeValues": ["1", "1", "2", "2", "3"], "desc": "使用铁拳攻击随机方向的矿石", "unlockTip": "通过第5关", "equipEffect": 1, "levelEffectValue": [14, 18, 24, 31, 40, 52, 68, 88, 114, 148, 193], "levelSpeed": [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 1, "entryId": 20901}, {"level": 2, "entryId": 20902}, {"level": 3, "entryId": 20903}, {"level": 4, "entryId": 20904}], "rankResName": ["钢铁之拳", "钢铁之拳2", "钢铁之拳3", "钢铁之拳4", "钢铁之拳5"], "levelUpItemId": 1009, "showInfos": [{"type": "范围", "title": "范围", "value": "随机"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 10, "rarity": 3, "order": 10, "seat": 5, "width": 78, "rankRatio": [1, 2, 2, 3, 4], "rankRangeValues": ["1", "1", "2", "2", "3"], "desc": "降下雪花攻击经过的矿石", "unlockTip": "通过第10关", "equipEffect": 1, "levelEffectValue": [20, 27, 36, 49, 66, 90, 121, 163, 221, 298, 402], "levelSpeed": [0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2], "levelUpCost": [1000, 1500, 2500, 5000, 7500, 11000, 15000, 20000, 25000, 32500], "levelUpPiece": [2, 3, 5, 10, 15, 22, 30, 40, 50, 65], "levelUnlockEntry": [{"level": 1, "entryId": 21001}, {"level": 2, "entryId": 21002}, {"level": 3, "entryId": 21003}, {"level": 4, "entryId": 21004}], "rankResName": ["魔法棒", "魔法棒2", "魔法棒3", "魔法棒4", "魔法棒5"], "levelUpItemId": 1010, "showInfos": [{"type": "范围", "title": "范围", "value": "随机"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 11, "rarity": 2, "order": 11, "seat": 4, "width": 100, "rankRatio": [1, 2, 2.4, 2.65, 4], "rankRangeValues": ["2:2:2", "2:2:2", "3:2:2", "3:3:2", "4:3:2"], "desc": "扔出多个小范围攻击的手榴弹", "unlockTip": "", "equipEffect": 1, "levelEffectValue": [17, 22, 30, 39, 52, 68, 90, 119, 157, 207, 273], "levelSpeed": [0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33, 0.33], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 21101}, {"level": 2, "entryId": 21102}, {"level": 3, "entryId": 21103}, {"level": 4, "entryId": 21104}], "rankResName": ["手榴弹", "手榴弹2", "手榴弹3", "手榴弹4", "手榴弹5"], "levelUpItemId": 1011, "showInfos": [{"type": "范围", "title": "范围", "value": "随机"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 12, "rarity": 3, "order": 12, "seat": 5, "width": 59, "rankRatio": [1, 2, 2, 3, 4], "rankRangeValues": ["1", "1", "2", "2", "3"], "desc": "射出可以穿透矿石的箭矢", "unlockTip": "邀请好友解锁", "equipEffect": 1, "levelEffectValue": [90, 122, 164, 221, 299, 404, 545, 735, 993, 1340, 1810], "levelSpeed": [0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2], "levelUpCost": [1000, 1500, 2500, 5000, 7500, 11000, 15000, 20000, 25000, 32500], "levelUpPiece": [2, 3, 5, 10, 15, 22, 30, 40, 50, 65], "levelUnlockEntry": [{"level": 1, "entryId": 21201}, {"level": 2, "entryId": 21202}, {"level": 3, "entryId": 21203}, {"level": 4, "entryId": 21204}], "rankResName": ["穿云箭", "穿云箭2", "穿云箭3", "穿云箭4", "穿云箭5"], "levelUpItemId": 1012, "showInfos": [{"type": "范围", "title": "范围", "value": "随机"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 13, "rarity": 2, "order": 13, "seat": 2, "width": 148, "rankRatio": [1, 1.35, 1.8, 2.4, 4], "rankRangeValues": ["2", "3", "4", "5", "6"], "desc": "放置可以持续攻击附近矿石的陀螺", "unlockTip": "通过第5关", "equipEffect": 1, "levelEffectValue": [15, 20, 26, 34, 46, 60, 79, 105, 138, 182, 241], "levelSpeed": [0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15, 0.15], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 21301}, {"level": 2, "entryId": 21302}, {"level": 3, "entryId": 21303}, {"level": 4, "entryId": 21304}], "rankResName": ["旋风陀螺", "旋风陀螺2", "旋风陀螺3", "旋风陀螺4", "旋风陀螺5"], "levelUpItemId": 1013, "showInfos": [{"type": "范围", "title": "范围", "value": "附近"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 14, "rarity": 2, "order": 14, "seat": 1, "width": 127, "rankRatio": [2, 4, 8, 16, 32], "rankRangeValues": ["", "", "", ""], "desc": "每回合获得额外的银币", "unlockTip": "", "equipEffect": 3, "levelEffectValue": [1, 1, 2, 2, 3, 4, 5, 7, 9, 12, 16], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [], "rankResName": ["银币钱包", "银币钱包2", "银币钱包3", "银币钱包4", "银币钱包5"], "levelUpItemId": 1014, "showInfos": [{"type": "获得银币", "title": "银币", "value": ""}]}, {"type": 15, "rarity": 2, "order": 15, "seat": 9, "width": 187, "rankRatio": [1, 1.35, 1.8, 2.4, 4], "rankRangeValues": ["60", "120", "180", "240", "360"], "desc": "扔出回旋的飞镖攻击附近的矿石", "unlockTip": "通过第2关", "equipEffect": 1, "levelEffectValue": [13, 17, 23, 30, 39, 52, 69, 91, 120, 158, 209], "levelSpeed": [0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23, 0.23], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 1, "entryId": 21501}, {"level": 2, "entryId": 21502}, {"level": 3, "entryId": 21503}, {"level": 4, "entryId": 21504}], "rankResName": ["回旋镖", "回旋镖2", "回旋镖3", "回旋镖4", "回旋镖5"], "levelUpItemId": 1015, "showInfos": [{"type": "范围", "title": "范围", "value": "附近"}, {"type": "时间", "title": "冷却", "value": ""}, {"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 16, "rarity": 2, "order": 1, "seat": 8, "width": 120, "rankRatio": [1, 1.5, 2.25, 3.4, 5.1], "rankRangeValues": ["", "", "", ""], "desc": "增加雪人的挖矿伤害", "unlockTip": "初始道具", "equipEffect": 5, "levelEffectValue": [5, 6, 12, 14, 26, 29, 31, 50, 55, 80, 87], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 3, "entryId": 21601}, {"level": 5, "entryId": 21602}, {"level": 8, "entryId": 21603}, {"level": 10, "entryId": 21604}], "rankResName": ["镐", "镐2", "镐3", "镐4", "镐5"], "levelUpItemId": 1016, "showInfos": [{"type": "攻击力", "title": "攻击", "value": ""}]}, {"type": 17, "rarity": 2, "order": 2, "seat": 2, "width": 155, "rankRatio": [1, 1.5, 2.25, 3.4, 5.1], "rankRangeValues": ["", "", "", ""], "desc": "增加雪人的体力上限", "unlockTip": "初始道具", "equipEffect": 6, "levelEffectValue": [15, 18, 37, 42, 78, 86, 94, 150, 164, 240, 260], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 3, "entryId": 21701}, {"level": 5, "entryId": 21702}, {"level": 8, "entryId": 21703}, {"level": 10, "entryId": 21704}], "rankResName": ["护目镜", "护目镜2", "护目镜3", "护目镜4", "护目镜5"], "levelUpItemId": 1017, "showInfos": [{"type": "体力", "title": "体力", "value": ""}]}, {"type": 18, "rarity": 2, "order": 5, "seat": 2, "width": 131, "rankRatio": [1, 1.5, 2.25, 3.4, 5.1], "rankRangeValues": ["", "", "", ""], "desc": "增加雪人的体力上限", "unlockTip": "通过第8关", "equipEffect": 6, "levelEffectValue": [15, 18, 37, 42, 78, 86, 94, 150, 164, 240, 260], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [500, 1000, 1500, 2000, 3000, 4000, 5500, 7000, 9000, 11000], "levelUpPiece": [5, 10, 15, 20, 30, 40, 55, 70, 90, 110], "levelUnlockEntry": [{"level": 3, "entryId": 21801}, {"level": 5, "entryId": 21802}, {"level": 8, "entryId": 21803}, {"level": 10, "entryId": 21804}], "rankResName": ["帽子", "帽子2", "帽子3", "帽子4", "帽子5"], "levelUpItemId": 1018, "showInfos": [{"type": "体力", "title": "体力", "value": ""}]}, {"type": 19, "rarity": 1, "order": 3, "seat": 4, "width": 115, "rankRatio": [1, 1.5, 2.25, 3.4, 5.1], "rankRangeValues": ["", "", "", ""], "desc": "增加雪人的体力上限", "unlockTip": "通过第3关", "equipEffect": 6, "levelEffectValue": [12, 14, 29, 33, 62, 67, 73, 118, 127, 187, 199], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 3, "entryId": 21901}, {"level": 5, "entryId": 21902}, {"level": 8, "entryId": 21903}, {"level": 10, "entryId": 21904}], "rankResName": ["围巾", "围巾2", "围巾3", "围巾4", "围巾5"], "levelUpItemId": 1019, "showInfos": [{"type": "体力", "title": "体力", "value": ""}]}, {"type": 20, "rarity": 1, "order": 4, "seat": 1, "width": 106, "rankRatio": [1, 1.5, 2.25, 3.4, 5.1], "rankRangeValues": ["", "", "", ""], "desc": "增加雪人的体力上限", "unlockTip": "通过第6关", "equipEffect": 6, "levelEffectValue": [6, 7, 15, 16, 30, 33, 36, 57, 62, 91, 97], "levelSpeed": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "levelUpCost": [200, 400, 600, 900, 1200, 1600, 2000, 2600, 3200, 4000], "levelUpPiece": [10, 20, 30, 45, 60, 80, 100, 130, 160, 200], "levelUnlockEntry": [{"level": 3, "entryId": 22001}, {"level": 5, "entryId": 22002}, {"level": 8, "entryId": 22003}, {"level": 10, "entryId": 22004}], "rankResName": ["勋章", "勋章2", "勋章3", "勋章4", "勋章5"], "levelUpItemId": 1020, "showInfos": [{"type": "体力", "title": "体力", "value": ""}]}]