[{"__type__": "cc.Prefab", "_name": "gamblingProgressBarItem", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "gamblingProgressBarItem", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 26}, {"__id__": 37}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 45}], "_prefab": {"__id__": 47}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "矿洞进度条_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": -155, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "矿洞进度条_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8M54+AP5ADa91waCYh9JF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "64a8449a-fc29-4253-b715-c415af255d9c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5faKxJv75A/JyZFFZpI9t9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceV6BtOjVPcKLBS1MI6VPZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "641Ii6SCtEqI3u3CZa5jTc"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 13}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd0iOGcrVAPodWvmuuWX9/"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19D1ZJ0yRE6oa9cmIbk3Qm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": false, "__prefab": {"__id__": 17}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c/AHdNKFKzIp1dF3ZIRxw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59OFNUKHBK16Crw3RvCpZs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5IaOzMNpFdZuzbOoL3h3H"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ef14300c-f4b8-4518-b7d4-0c234cd7321d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YoQKkCxPgamWdaKhfB13"}, {"__type__": "be29aRaSvJDiY8MyQ801dFR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 24}, "max": 100, "title": "", "bar": {"__id__": 10}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14IPgpI3RGy5Czpv9qYWmU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdTZ//UGpBZIuvYUeAnAdP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 27}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 26}, "asset": {"__uuid__": "a2d3c2b8-2bb6-4213-8160-32c64989f30f", "__expectedType__": "cc.Prefab"}, "fileId": "9af+T5dPpCWaFV17snggbO", "instance": {"__id__": 28}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "cebRRPdFVO0axXfqipRoy1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 29}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_name"], "value": "checkInBoardItem"}, {"__type__": "cc.TargetInfo", "localID": ["9af+T5dPpCWaFV17snggbO"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -0.3, "y": 3.7, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e73x4yiCxE+qZz22+DNd1K"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.68, "y": 0.67, "z": 1}}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 38}, {"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -57, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 39}, "_contentSize": {"__type__": "cc.Size", "width": 41.34765625, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7d3AO80nZNP6LHAA4+pnCK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 37}, "_enabled": true, "__prefab": {"__id__": 41}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 130, "g": 89, "b": 89, "a": 255}, "_string": "7次", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51Wl9zn4RNEqRDr8ZKOZDD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22WT7icXBJc7T4AElRxsp+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 44}, "_contentSize": {"__type__": "cc.Size", "width": 149.3, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cmzkfSUJJiJRcHgOX8CO7"}, {"__type__": "cab28kXnk1Ey4KKKmpiIdF2", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 46}, "progressBar": {"__id__": 23}, "label": {"__id__": 40}, "checkInBoardNode": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6GGigoC9JXbYE2C0U12dG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2/xOr7ftAtZI4z1PqPjvW", "instance": null, "targetOverrides": [{"__id__": 48}], "nestedPrefabInstanceRoots": [{"__id__": 26}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 45}, "sourceInfo": null, "propertyPath": ["checkInBoardNode"], "target": {"__id__": 26}, "targetInfo": {"__id__": 49}}, {"__type__": "cc.TargetInfo", "localID": ["79o+IDAZpI1b4u4CfHc0aG"]}]