{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a@6c48a", "displayName": "普通晨之星礼盒", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a@f9941", "displayName": "普通晨之星礼盒", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0.5, "trimX": 0, "trimY": 0, "width": 99, "height": 74, "rawWidth": 99, "rawHeight": 75, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-49.5, -37, 0, 49.5, -37, 0, -49.5, 37, 0, 49.5, 37, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 75, 99, 75, 0, 1, 99, 1], "nuv": [0, 0.013333333333333334, 1, 0.013333333333333334, 0, 1, 1, 1], "minPos": [-49.5, -37, 0], "maxPos": [49.5, 37, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "33ef6ce5-7721-4a06-8ee2-8ad56b455e0a@6c48a", "compressSettings": {"useCompressTexture": true, "presetId": "20bmNzS6VEmYy9ZsOKO6RA"}}}