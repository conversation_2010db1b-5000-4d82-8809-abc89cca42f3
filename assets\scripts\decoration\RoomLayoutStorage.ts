/**
 * 房间布局存储管理器
 * 负责房间布局的保存、加载和管理
 */

import { Vec2 } from "cc";
import { PlacedFurniture, FurnitureTheme, Rotation } from "./DecorationDefine";
import { DataManager } from "../DataManager";

/**
 * 保存的房间布局数据
 */
export interface SavedRoomLayout {
    id: string;                    // 布局唯一ID
    name: string;                  // 布局名称
    description?: string;          // 布局描述
    roomSize: { x: number; y: number }; // 房间尺寸
    furnitures: SavedFurniture[];  // 家具列表
    score?: number;                // 评分
    dominantTheme?: FurnitureTheme; // 主导主题
    createdTime: number;           // 创建时间
    modifiedTime: number;          // 修改时间
    tags?: string[];               // 标签
    thumbnail?: string;            // 缩略图（base64）
}

/**
 * 保存的家具数据
 */
export interface SavedFurniture {
    templateId: number;            // 模板ID
    position: { x: number; y: number }; // 位置
    rotation: Rotation;            // 旋转角度
    placedTime?: number;           // 放置时间
}

/**
 * 布局分类
 */
export enum LayoutCategory {
    UserSaved = "user_saved",      // 用户保存
    Template = "template",         // 模板布局
    AutoSave = "auto_save",        // 自动保存
    Shared = "shared"              // 分享布局
}

/**
 * 搜索过滤条件
 */
export interface LayoutFilter {
    category?: LayoutCategory;
    theme?: FurnitureTheme;
    minScore?: number;
    maxScore?: number;
    tags?: string[];
    nameKeyword?: string;
    dateRange?: {
        start: number;
        end: number;
    };
}

/**
 * 排序选项
 */
export enum SortOption {
    CreatedTimeDesc = "created_desc",
    CreatedTimeAsc = "created_asc",
    ModifiedTimeDesc = "modified_desc",
    ModifiedTimeAsc = "modified_asc",
    ScoreDesc = "score_desc",
    ScoreAsc = "score_asc",
    NameAsc = "name_asc",
    NameDesc = "name_desc"
}

export class RoomLayoutStorage {
    private static _instance: RoomLayoutStorage;
    private static readonly STORAGE_KEY = "room_layouts";
    private static readonly AUTO_SAVE_KEY = "auto_save_layout";
    private static readonly MAX_AUTO_SAVES = 10;
    private static readonly MAX_USER_SAVES = 100;

    public static getInstance(): RoomLayoutStorage {
        return this._instance || (this._instance = new RoomLayoutStorage());
    }

    private constructor() {}

    /**
     * 保存房间布局
     */
    async saveLayout(
        furnitures: PlacedFurniture[],
        roomSize: Vec2,
        layoutName: string,
        category: LayoutCategory = LayoutCategory.UserSaved,
        options?: {
            description?: string;
            tags?: string[];
            score?: number;
            dominantTheme?: FurnitureTheme;
            thumbnail?: string;
        }
    ): Promise<string> {
        const layoutId = this.generateLayoutId();
        const now = Date.now();

        const savedLayout: SavedRoomLayout = {
            id: layoutId,
            name: layoutName,
            description: options?.description,
            roomSize: { x: roomSize.x, y: roomSize.y },
            furnitures: furnitures.map(f => ({
                templateId: f.templateId,
                position: { x: f.position.x, y: f.position.y },
                rotation: f.rotation,
                placedTime: f.placedTime
            })),
            score: options?.score,
            dominantTheme: options?.dominantTheme,
            createdTime: now,
            modifiedTime: now,
            tags: options?.tags || [],
            thumbnail: options?.thumbnail
        };

        await this.saveLayoutToStorage(savedLayout, category);
        console.log(`布局 "${layoutName}" 已保存，ID: ${layoutId}`);
        
        return layoutId;
    }

    /**
     * 加载房间布局
     */
    async loadLayout(layoutId: string): Promise<SavedRoomLayout | null> {
        const allLayouts = await this.getAllLayouts();
        return allLayouts.find(layout => layout.id === layoutId) || null;
    }

    /**
     * 删除房间布局
     */
    async deleteLayout(layoutId: string): Promise<boolean> {
        try {
            const allLayouts = await this.getAllLayouts();
            const filteredLayouts = allLayouts.filter(layout => layout.id !== layoutId);
            
            if (filteredLayouts.length === allLayouts.length) {
                return false; // 没有找到要删除的布局
            }

            await this.saveAllLayouts(filteredLayouts);
            console.log(`布局 ${layoutId} 已删除`);
            return true;
        } catch (error) {
            console.error("删除布局失败:", error);
            return false;
        }
    }

    /**
     * 更新房间布局
     */
    async updateLayout(
        layoutId: string,
        updates: Partial<SavedRoomLayout>
    ): Promise<boolean> {
        try {
            const allLayouts = await this.getAllLayouts();
            const layoutIndex = allLayouts.findIndex(layout => layout.id === layoutId);
            
            if (layoutIndex === -1) {
                return false;
            }

            allLayouts[layoutIndex] = {
                ...allLayouts[layoutIndex],
                ...updates,
                modifiedTime: Date.now()
            };

            await this.saveAllLayouts(allLayouts);
            console.log(`布局 ${layoutId} 已更新`);
            return true;
        } catch (error) {
            console.error("更新布局失败:", error);
            return false;
        }
    }

    /**
     * 获取所有布局
     */
    async getAllLayouts(): Promise<SavedRoomLayout[]> {
        try {
            const data = await DataManager.instance.loadData(RoomLayoutStorage.STORAGE_KEY);
            return Array.isArray(data) ? data : [];
        } catch (error) {
            console.warn("加载布局数据失败:", error);
            return [];
        }
    }

    /**
     * 搜索和过滤布局
     */
    async searchLayouts(
        filter?: LayoutFilter,
        sort: SortOption = SortOption.ModifiedTimeDesc,
        limit?: number
    ): Promise<SavedRoomLayout[]> {
        let layouts = await this.getAllLayouts();

        // 应用过滤条件
        if (filter) {
            layouts = layouts.filter(layout => {
                // 分类过滤
                if (filter.category && !this.matchesCategory(layout, filter.category)) {
                    return false;
                }

                // 主题过滤
                if (filter.theme && layout.dominantTheme !== filter.theme) {
                    return false;
                }

                // 评分过滤
                if (filter.minScore && (layout.score || 0) < filter.minScore) {
                    return false;
                }
                if (filter.maxScore && (layout.score || 0) > filter.maxScore) {
                    return false;
                }

                // 标签过滤
                if (filter.tags && filter.tags.length > 0) {
                    const layoutTags = layout.tags || [];
                    if (!filter.tags.some(tag => layoutTags.includes(tag))) {
                        return false;
                    }
                }

                // 名称关键词过滤
                if (filter.nameKeyword) {
                    const keyword = filter.nameKeyword.toLowerCase();
                    if (!layout.name.toLowerCase().includes(keyword) &&
                        !(layout.description || "").toLowerCase().includes(keyword)) {
                        return false;
                    }
                }

                // 日期范围过滤
                if (filter.dateRange) {
                    if (layout.createdTime < filter.dateRange.start ||
                        layout.createdTime > filter.dateRange.end) {
                        return false;
                    }
                }

                return true;
            });
        }

        // 应用排序
        layouts.sort((a, b) => {
            switch (sort) {
                case SortOption.CreatedTimeDesc:
                    return b.createdTime - a.createdTime;
                case SortOption.CreatedTimeAsc:
                    return a.createdTime - b.createdTime;
                case SortOption.ModifiedTimeDesc:
                    return b.modifiedTime - a.modifiedTime;
                case SortOption.ModifiedTimeAsc:
                    return a.modifiedTime - b.modifiedTime;
                case SortOption.ScoreDesc:
                    return (b.score || 0) - (a.score || 0);
                case SortOption.ScoreAsc:
                    return (a.score || 0) - (b.score || 0);
                case SortOption.NameAsc:
                    return a.name.localeCompare(b.name);
                case SortOption.NameDesc:
                    return b.name.localeCompare(a.name);
                default:
                    return 0;
            }
        });

        // 应用限制
        if (limit && limit > 0) {
            layouts = layouts.slice(0, limit);
        }

        return layouts;
    }

    /**
     * 自动保存当前布局
     */
    async autoSave(
        furnitures: PlacedFurniture[],
        roomSize: Vec2,
        score?: number,
        dominantTheme?: FurnitureTheme
    ): Promise<void> {
        const autoSaveId = `auto_save_${Date.now()}`;
        const autoSaveName = `自动保存 ${new Date().toLocaleString()}`;

        await this.saveLayout(
            furnitures,
            roomSize,
            autoSaveName,
            LayoutCategory.AutoSave,
            { score, dominantTheme }
        );

        // 清理旧的自动保存
        await this.cleanupAutoSaves();
    }

    /**
     * 导出布局为JSON
     */
    async exportLayout(layoutId: string): Promise<string | null> {
        const layout = await this.loadLayout(layoutId);
        if (!layout) {
            return null;
        }

        return JSON.stringify(layout, null, 2);
    }

    /**
     * 从JSON导入布局
     */
    async importLayout(jsonData: string, newName?: string): Promise<string | null> {
        try {
            const layoutData = JSON.parse(jsonData) as SavedRoomLayout;
            
            // 验证数据格式
            if (!this.validateLayoutData(layoutData)) {
                throw new Error("无效的布局数据格式");
            }

            // 生成新的ID和名称
            const newId = this.generateLayoutId();
            const importedLayout: SavedRoomLayout = {
                ...layoutData,
                id: newId,
                name: newName || `${layoutData.name} (导入)`,
                createdTime: Date.now(),
                modifiedTime: Date.now()
            };

            await this.saveLayoutToStorage(importedLayout, LayoutCategory.UserSaved);
            console.log(`布局 "${importedLayout.name}" 导入成功`);
            
            return newId;
        } catch (error) {
            console.error("导入布局失败:", error);
            return null;
        }
    }

    /**
     * 获取布局统计信息
     */
    async getLayoutStats(): Promise<{
        total: number;
        byCategory: Record<LayoutCategory, number>;
        byTheme: Record<FurnitureTheme, number>;
        averageScore: number;
        totalSize: number; // 存储大小（字节）
    }> {
        const layouts = await this.getAllLayouts();
        const stats = {
            total: layouts.length,
            byCategory: {} as Record<LayoutCategory, number>,
            byTheme: {} as Record<FurnitureTheme, number>,
            averageScore: 0,
            totalSize: 0
        };

        // 初始化计数器
        Object.values(LayoutCategory).forEach(category => {
            stats.byCategory[category] = 0;
        });
        Object.values(FurnitureTheme).filter(value => typeof value === 'number').forEach(theme => {
            stats.byTheme[theme as FurnitureTheme] = 0;
        });

        let totalScore = 0;
        let scoreCount = 0;

        layouts.forEach(layout => {
            // 分类统计
            const category = this.getLayoutCategory(layout);
            stats.byCategory[category]++;

            // 主题统计
            if (layout.dominantTheme) {
                stats.byTheme[layout.dominantTheme]++;
            }

            // 评分统计
            if (layout.score) {
                totalScore += layout.score;
                scoreCount++;
            }

            // 大小统计
            stats.totalSize += JSON.stringify(layout).length;
        });

        stats.averageScore = scoreCount > 0 ? totalScore / scoreCount : 0;

        return stats;
    }

    /**
     * 清理存储空间
     */
    async cleanup(options?: {
        removeAutoSaves?: boolean;
        removeOldLayouts?: number; // 删除N天前的布局
        maxLayouts?: number; // 保留最多N个布局
    }): Promise<number> {
        let layouts = await this.getAllLayouts();
        const originalCount = layouts.length;

        if (options?.removeAutoSaves) {
            layouts = layouts.filter(layout => 
                !this.matchesCategory(layout, LayoutCategory.AutoSave)
            );
        }

        if (options?.removeOldLayouts) {
            const cutoffTime = Date.now() - (options.removeOldLayouts * 24 * 60 * 60 * 1000);
            layouts = layouts.filter(layout => layout.createdTime > cutoffTime);
        }

        if (options?.maxLayouts && layouts.length > options.maxLayouts) {
            // 按修改时间排序，保留最新的
            layouts.sort((a, b) => b.modifiedTime - a.modifiedTime);
            layouts = layouts.slice(0, options.maxLayouts);
        }

        await this.saveAllLayouts(layouts);
        const removedCount = originalCount - layouts.length;
        
        if (removedCount > 0) {
            console.log(`清理完成，删除了 ${removedCount} 个布局`);
        }

        return removedCount;
    }

    // 私有方法

    private async saveLayoutToStorage(layout: SavedRoomLayout, category: LayoutCategory): Promise<void> {
        const allLayouts = await this.getAllLayouts();
        
        // 检查存储限制
        if (category === LayoutCategory.UserSaved && 
            allLayouts.filter(l => this.matchesCategory(l, LayoutCategory.UserSaved)).length >= RoomLayoutStorage.MAX_USER_SAVES) {
            throw new Error(`用户保存的布局数量已达到上限 (${RoomLayoutStorage.MAX_USER_SAVES})`);
        }

        allLayouts.push(layout);
        await this.saveAllLayouts(allLayouts);
    }

    private async saveAllLayouts(layouts: SavedRoomLayout[]): Promise<void> {
        await DataManager.instance.saveData(RoomLayoutStorage.STORAGE_KEY, layouts);
    }

    private async cleanupAutoSaves(): Promise<void> {
        const allLayouts = await this.getAllLayouts();
        const autoSaves = allLayouts.filter(layout => 
            this.matchesCategory(layout, LayoutCategory.AutoSave)
        );

        if (autoSaves.length > RoomLayoutStorage.MAX_AUTO_SAVES) {
            // 按创建时间排序，删除最旧的
            autoSaves.sort((a, b) => a.createdTime - b.createdTime);
            const toRemove = autoSaves.slice(0, autoSaves.length - RoomLayoutStorage.MAX_AUTO_SAVES);
            
            const filteredLayouts = allLayouts.filter(layout => 
                !toRemove.some(remove => remove.id === layout.id)
            );

            await this.saveAllLayouts(filteredLayouts);
        }
    }

    private generateLayoutId(): string {
        return `layout_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private matchesCategory(layout: SavedRoomLayout, category: LayoutCategory): boolean {
        // 根据布局特征判断分类
        if (layout.name.includes("自动保存")) {
            return category === LayoutCategory.AutoSave;
        }
        if (layout.name.includes("模板")) {
            return category === LayoutCategory.Template;
        }
        if (layout.name.includes("分享")) {
            return category === LayoutCategory.Shared;
        }
        return category === LayoutCategory.UserSaved;
    }

    private getLayoutCategory(layout: SavedRoomLayout): LayoutCategory {
        if (layout.name.includes("自动保存")) return LayoutCategory.AutoSave;
        if (layout.name.includes("模板")) return LayoutCategory.Template;
        if (layout.name.includes("分享")) return LayoutCategory.Shared;
        return LayoutCategory.UserSaved;
    }

    private validateLayoutData(data: any): boolean {
        return data &&
               typeof data.id === 'string' &&
               typeof data.name === 'string' &&
               data.roomSize &&
               typeof data.roomSize.x === 'number' &&
               typeof data.roomSize.y === 'number' &&
               Array.isArray(data.furnitures) &&
               data.furnitures.every((f: any) => 
                   typeof f.templateId === 'number' &&
                   f.position &&
                   typeof f.position.x === 'number' &&
                   typeof f.position.y === 'number' &&
                   typeof f.rotation === 'number'
               );
    }
}
