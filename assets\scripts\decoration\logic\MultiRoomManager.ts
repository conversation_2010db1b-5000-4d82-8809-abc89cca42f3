/**
 * 多房间管理器
 * 管理多个房间的布局、家具摆放和评分系统
 */

import { Vec2 } from "cc";
import {
    MultiRoomConfig,
    SingleRoomConfig,
    PlacedFurniture,
    FurnitureTemplate,
    Rotation,
    PlacementResult,
    RoomScoreDetails,
    FurnitureTheme,
    RoomTemplate
} from "./DecorationDefine";
import { RoomGrid } from "./RoomGrid";
import { RoomScoreCalculator } from "./RoomScoreCalculator";
import { FurniturePlacementValidator } from "./FurniturePlacementValidator";
import { DefaultRoomTemplates } from "./DefaultRoomTemplates";
import { DataManager } from "../../DataManager";

export class MultiRoomManager {
    private static _instance: MultiRoomManager;

    public static getInstance(): MultiRoomManager {
        return this._instance || (this._instance = new MultiRoomManager());
    }

    private config: MultiRoomConfig;
    private roomGrids: Map<string, RoomGrid> = new Map();
    private scoreCalculator: RoomScoreCalculator;
    private furnitureTemplates: Map<number, FurnitureTemplate> = new Map();

    private constructor() {
        this.config = {
            rooms: new Map(),
            sharedScore: true
        };
        this.scoreCalculator = new RoomScoreCalculator();
    }

    /**
     * 初始化多房间系统
     */
    async init(furnitureTemplates: Map<number, FurnitureTemplate>) {
        this.furnitureTemplates = furnitureTemplates;
        
        // 加载保存的配置或使用默认配置
        await this.loadConfiguration();
        
        // 初始化房间网格
        this.initializeRoomGrids();
    }

    /**
     * 加载配置
     */
    private async loadConfiguration() {
        const savedConfig = await DataManager.instance.loadLocalData("multiRoomConfig");
        
        if (savedConfig) {
            // 加载保存的配置
            this.loadFromSavedConfig(savedConfig);
        } else {
            // 使用默认双房间配置
            this.setupDefaultDualRooms();
        }
    }

    /**
     * 设置默认双房间配置
     */
    private setupDefaultDualRooms() {
        const dualSetup = DefaultRoomTemplates.getRecommendedDualRoomSetup();
        
        // 添加房间A
        this.addRoom(this.createRoomFromTemplate(dualSetup.roomA));
        
        // 添加房间B
        this.addRoom(this.createRoomFromTemplate(dualSetup.roomB));
        
        this.config.sharedScore = true;
    }

    /**
     * 从模板创建房间配置
     */
    private createRoomFromTemplate(template: RoomTemplate): SingleRoomConfig {
        return {
            id: template.id,
            name: template.name,
            size: template.size.clone(),
            layout: template.layout.map(row => [...row]), // 深拷贝
            placedFurnitures: [],
            lastModified: Date.now()
        };
    }

    /**
     * 添加房间
     */
    addRoom(roomConfig: SingleRoomConfig): boolean {
        if (this.config.rooms.has(roomConfig.id)) {
            console.warn(`房间 ${roomConfig.id} 已存在`);
            return false;
        }

        this.config.rooms.set(roomConfig.id, roomConfig);
        this.initializeRoomGrid(roomConfig);
        this.saveConfiguration();
        return true;
    }

    /**
     * 移除房间
     */
    removeRoom(roomId: string): boolean {
        if (!this.config.rooms.has(roomId)) {
            return false;
        }

        this.config.rooms.delete(roomId);
        this.roomGrids.delete(roomId);
        this.saveConfiguration();
        return true;
    }

    /**
     * 获取房间配置
     */
    getRoomConfig(roomId: string): SingleRoomConfig | null {
        return this.config.rooms.get(roomId) || null;
    }

    /**
     * 获取所有房间ID
     */
    getAllRoomIds(): string[] {
        return Array.from(this.config.rooms.keys());
    }

    /**
     * 获取房间网格
     */
    getRoomGrid(roomId: string): RoomGrid | null {
        return this.roomGrids.get(roomId) || null;
    }

    /**
     * 在指定房间放置家具
     */
    placeFurniture(roomId: string, templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): PlacementResult {
        const roomConfig = this.getRoomConfig(roomId);
        const roomGrid = this.getRoomGrid(roomId);
        const template = this.furnitureTemplates.get(templateId);

        if (!roomConfig || !roomGrid || !template) {
            return { success: false, errorMessage: "房间或家具模板不存在" };
        }

        // 验证摆放位置
        const validator = new FurniturePlacementValidator(roomGrid, roomConfig.placedFurnitures);
        const validationResult = validator.validatePlacement(
            template, position, rotation
        );

        if (!validationResult.isValid) {
            return {
                success: false,
                errorMessage: validationResult.reason || "摆放位置无效"
            };
        }

        // 创建家具实例
        const furniture: PlacedFurniture = {
            id: this.generateFurnitureId(),
            templateId,
            position: position.clone(),
            rotation,
            currentSize: this.getRotatedSize(template.baseSize, rotation),
            placedTime: Date.now(),
            roomId
        };

        // 放置家具
        const isWallDecoration = template.properties.isWallDecoration || false;
        if (roomGrid.placeFurniture(furniture, isWallDecoration)) {
            roomConfig.placedFurnitures.push(furniture);
            roomConfig.lastModified = Date.now();
            this.saveConfiguration();
            return { success: true };
        }

        return { success: false, errorMessage: "放置失败" };
    }

    /**
     * 移除家具
     */
    removeFurniture(furnitureId: string): PlacementResult {
        for (const [roomId, roomConfig] of this.config.rooms) {
            const furnitureIndex = roomConfig.placedFurnitures.findIndex(f => f.id === furnitureId);
            
            if (furnitureIndex !== -1) {
                const roomGrid = this.getRoomGrid(roomId);
                if (roomGrid && roomGrid.removeFurniture(furnitureId)) {
                    roomConfig.placedFurnitures.splice(furnitureIndex, 1);
                    roomConfig.lastModified = Date.now();
                    this.saveConfiguration();
                    return { success: true };
                }
            }
        }

        return { success: false, errorMessage: "家具不存在" };
    }

    /**
     * 获取所有已放置的家具
     */
    getAllPlacedFurnitures(): PlacedFurniture[] {
        const allFurnitures: PlacedFurniture[] = [];
        for (const roomConfig of this.config.rooms.values()) {
            allFurnitures.push(...roomConfig.placedFurnitures);
        }
        return allFurnitures;
    }

    /**
     * 获取指定房间的家具
     */
    getRoomFurnitures(roomId: string): PlacedFurniture[] {
        const roomConfig = this.getRoomConfig(roomId);
        return roomConfig ? roomConfig.placedFurnitures : [];
    }

    /**
     * 计算评分
     */
    calculateScore(): RoomScoreDetails {
        if (this.config.sharedScore) {
            // 共享评分：计算所有房间的总评分
            return this.calculateSharedScore();
        } else {
            // 独立评分：返回主房间的评分
            const mainRoomId = this.getAllRoomIds()[0];
            return this.calculateRoomScore(mainRoomId);
        }
    }

    /**
     * 计算共享评分
     */
    private calculateSharedScore(): RoomScoreDetails {
        const allFurnitures = this.getAllPlacedFurnitures();
        const totalRoomArea = this.calculateTotalRoomArea();
        
        return this.scoreCalculator.calculateRoomScore(
            allFurnitures,
            this.furnitureTemplates,
            new Vec2(Math.sqrt(totalRoomArea), Math.sqrt(totalRoomArea)) // 近似总面积
        );
    }

    /**
     * 计算单个房间评分
     */
    calculateRoomScore(roomId: string): RoomScoreDetails {
        const roomConfig = this.getRoomConfig(roomId);
        if (!roomConfig) {
            return this.getEmptyScore();
        }

        return this.scoreCalculator.calculateRoomScore(
            roomConfig.placedFurnitures,
            this.furnitureTemplates,
            roomConfig.size
        );
    }

    /**
     * 清空指定房间
     */
    clearRoom(roomId: string): boolean {
        const roomConfig = this.getRoomConfig(roomId);
        const roomGrid = this.getRoomGrid(roomId);
        
        if (!roomConfig || !roomGrid) {
            return false;
        }

        // 清空家具
        roomConfig.placedFurnitures.forEach(furniture => {
            roomGrid.removeFurniture(furniture.id);
        });
        
        roomConfig.placedFurnitures = [];
        roomConfig.lastModified = Date.now();
        this.saveConfiguration();
        return true;
    }

    /**
     * 清空所有房间
     */
    clearAllRooms(): void {
        for (const roomId of this.getAllRoomIds()) {
            this.clearRoom(roomId);
        }
    }

    // 私有辅助方法
    private initializeRoomGrids() {
        for (const roomConfig of this.config.rooms.values()) {
            this.initializeRoomGrid(roomConfig);
        }
    }

    private initializeRoomGrid(roomConfig: SingleRoomConfig) {
        const roomGrid = new RoomGrid(roomConfig.size, roomConfig.layout);
        this.roomGrids.set(roomConfig.id, roomGrid);
        
        // 恢复家具占用状态
        roomConfig.placedFurnitures.forEach(furniture => {
            const template = this.furnitureTemplates.get(furniture.templateId);
            if (template) {
                const isWallDecoration = template.properties.isWallDecoration || false;
                roomGrid.placeFurniture(furniture, isWallDecoration);
            }
        });
    }

    private generateFurnitureId(): string {
        return `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private getRotatedSize(baseSize: Vec2, rotation: Rotation): Vec2 {
        if (rotation === Rotation.Deg90 || rotation === Rotation.Deg270) {
            return new Vec2(baseSize.y, baseSize.x);
        }
        return baseSize.clone();
    }

    private calculateTotalRoomArea(): number {
        let totalArea = 0;
        for (const roomConfig of this.config.rooms.values()) {
            totalArea += roomConfig.size.x * roomConfig.size.y;
        }
        return totalArea;
    }

    private getEmptyScore(): RoomScoreDetails {
        return {
            themeScore: 0,
            quantityScore: 0,
            valueScore: 0,
            layoutScore: 0,
            totalScore: 0,
            dominantTheme: null
        };
    }

    private loadFromSavedConfig(savedConfig: any) {
        // TODO: 实现从保存的配置加载
        console.log("加载保存的多房间配置");
    }

    private saveConfiguration() {
        // 转换为可序列化的格式
        const saveData = {
            rooms: Array.from(this.config.rooms.entries()),
            sharedScore: this.config.sharedScore
        };

        DataManager.instance.saveLocalData("multiRoomConfig", saveData);
    }
}
