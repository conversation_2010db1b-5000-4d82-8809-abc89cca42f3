[{"__type__": "cc.Prefab", "_name": "完成第1关", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "完成第1关", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 21}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "01d4aUjQpZMip0tR3tmI9iX", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "guides": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "waitPre": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43PAxXqfhL54dXlfThQV+l"}, {"__type__": "SceneGuide", "name": "等待页面", "switchType": 2, "type": 9, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 1, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "对话", "switchType": 2, "type": 12, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "终于带着宝藏逃出矿洞了！", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "升级装备能够更好地挖掘，前往装备页升级装备吧。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 8}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击装备", "waitPageId": 1, "targetNodePath": "mainUI/frame_maininterface_1/装备", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "点击装备卡打开装备升级页面", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 800}, "roleName": "胡萝卜卜", "guides": [{"__id__": 10}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "", "waitPageId": 0, "targetNodePath": "", "force": false, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 12}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击炸弹", "waitPageId": 1, "targetNodePath": "mainUI/pages/装备/top/胡萝卜卜/Node/sprite1", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "对话", "switchType": 2, "type": 12, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "升级装备需要消耗碎片和金币，好在我们都还足够。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1000}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤3对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "点击升级装备", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 800}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤3引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 16}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击升级", "waitPageId": 18, "targetNodePath": "upgradeUI/upgradeBtn", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "等待关闭升级", "switchType": 2, "type": 9, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 1, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤4对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "继续去进行新的矿洞挖掘吧！", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤4引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 20}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击挖矿", "waitPageId": 1, "targetNodePath": "mainUI/frame_maininterface_1/挖矿", "force": true, "nodeGuide": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]