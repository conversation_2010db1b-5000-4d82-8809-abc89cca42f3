/**
 * 布局存储功能测试
 * 测试保存/加载功能的完整性
 */

import { Component, _decorator, Vec2, log } from "cc";
import { RoomLayoutManager } from "../ui/RoomLayoutManager";
import { LayoutCategory, SortOption } from "../ui/RoomLayoutStorage";
import { Rotation, FurnitureTheme, PlacedFurniture, FurnitureTemplate, FurnitureType } from "../logic/DecorationDefine";
import { RoomGrid } from "../logic/RoomGrid";

const { ccclass, property } = _decorator;

@ccclass('LayoutStorageTest')
export class LayoutStorageTest extends Component {
    private layoutManager: RoomLayoutManager | null = null;
    private roomGrid: RoomGrid | null = null;
    private furnitureTemplates: Map<number, FurnitureTemplate> = new Map();
    private placedFurnitures: PlacedFurniture[] = [];

    protected onLoad() {
        // 组件初始化
        this.initFurnitureTemplates();
        this.roomGrid = new RoomGrid(new Vec2(10, 10));
        this.layoutManager = new RoomLayoutManager();
    }

    protected start() {
        // 延迟执行测试，确保组件初始化完成
        setTimeout(() => {
            this.runTests();
        }, 1000);
    }

    /**
     * 初始化测试用的家具模板
     */
    private initFurnitureTemplates(): void {
        // 创建一些测试用的家具模板
        this.furnitureTemplates.set(1, {
            id: 1,
            name: "测试椅子",
            type: FurnitureType.Small,
            baseSize: new Vec2(1, 1),
            spriteFrame: "test_chair",
            description: "测试用椅子",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 1,
                value: 100,
                beauty: 15
            }
        });

        this.furnitureTemplates.set(2, {
            id: 2,
            name: "测试桌子",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "test_table",
            description: "测试用桌子",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 2,
                value: 200,
                beauty: 25
            }
        });

        this.furnitureTemplates.set(3, {
            id: 3,
            name: "测试沙发",
            type: FurnitureType.Large,
            baseSize: new Vec2(2, 2),
            spriteFrame: "test_sofa",
            description: "测试用沙发",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 2,
                value: 300,
                beauty: 30
            }
        });

        this.furnitureTemplates.set(4, {
            id: 4,
            name: "测试壁画",
            type: FurnitureType.WallDecoration,
            baseSize: new Vec2(1, 1),
            spriteFrame: "test_painting",
            description: "测试用壁画",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 1,
                value: 150,
                beauty: 20,
                isWallDecoration: true
            }
        });
    }

    /**
     * 运行所有测试
     */
    private async runTests() {
        log("=== 开始布局存储功能测试 ===");
        
        try {
            // 测试基本保存/加载功能
            await this.testBasicSaveLoad();
            
            // 测试搜索和过滤功能
            await this.testSearchAndFilter();
            
            // 测试导入/导出功能
            await this.testImportExport();
            
            // 测试自动保存功能
            await this.testAutoSave();
            
            // 测试存储统计和清理
            await this.testStorageManagement();
            
            log("=== 布局存储功能测试完成 ===");
        } catch (error) {
            log("测试过程中出现错误:", error);
        }
    }

    /**
     * 测试基本保存/加载功能
     */
    private async testBasicSaveLoad() {
        log("--- 测试基本保存/加载功能 ---");

        if (!this.layoutManager) {
            log("布局管理器未初始化");
            return;
        }

        // 创建测试家具布局
        this.createTestLayout();

        // 测试快速保存
        log("测试快速保存...");
        const saveResult = await this.layoutManager.quickSave("测试布局1", {
            autoGenerateName: false,
            includeScore: true,
            includeThumbnail: false
        });
        log("快速保存结果:", saveResult.success ? "成功" : saveResult.message);

        if (saveResult.success && saveResult.layoutId) {
            // 测试加载
            log("测试加载布局...");
            const loadResult = await this.layoutManager.loadLayout(saveResult.layoutId);
            log("加载结果:", loadResult.success ? "成功" : loadResult.message);

            // 测试删除
            log("测试删除布局...");
            const deleteResult = await this.layoutManager.deleteLayout(saveResult.layoutId);
            log("删除结果:", deleteResult ? "成功" : "失败");
        }
    }

    /**
     * 创建测试家具布局
     */
    private createTestLayout(): void {
        this.placedFurnitures = [
            {
                id: "test_1",
                templateId: 1,
                position: new Vec2(2, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "test_2",
                templateId: 2,
                position: new Vec2(4, 4),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(2, 1),
                placedTime: Date.now()
            }
        ];
    }

    /**
     * 测试搜索和过滤功能
     */
    private async testSearchAndFilter() {
        log("--- 测试搜索和过滤功能 ---");

        if (!this.layoutManager) return;

        // 创建一些测试布局
        const testLayouts = [
            { name: "现代风格测试", theme: FurnitureTheme.Modern },
            { name: "古典风格测试", theme: FurnitureTheme.Classic },
            { name: "工业风格测试", theme: FurnitureTheme.Industrial }
        ];

        const savedLayoutIds: string[] = [];

        for (const testLayout of testLayouts) {
            // 创建不同的测试布局
            this.createTestLayoutWithTheme(testLayout.theme);
            await this.wait(300);

            const result = await this.layoutManager.quickSave(testLayout.name, {
                autoGenerateName: false,
                includeScore: true,
                tags: [testLayout.theme.toString()]
            });

            if (result.success && result.layoutId) {
                savedLayoutIds.push(result.layoutId);
            }
        }

        // 测试获取最近布局
        log("获取最近布局...");
        const recentLayouts = await this.layoutManager.getRecentLayouts(5);
        log(`找到 ${recentLayouts.length} 个最近布局`);

        // 测试按主题搜索
        log("按主题搜索布局...");
        const modernLayouts = await this.layoutManager.getLayoutsByTheme(FurnitureTheme.Modern, 10);
        log(`找到 ${modernLayouts.length} 个现代风格布局`);

        // 测试高分布局
        log("获取高分布局...");
        const topLayouts = await this.layoutManager.getTopScoredLayouts(5);
        log(`找到 ${topLayouts.length} 个高分布局`);

        // 清理测试数据
        for (const layoutId of savedLayoutIds) {
            await this.layoutManager.deleteLayout(layoutId);
        }
    }

    /**
     * 创建指定主题的测试布局
     */
    private createTestLayoutWithTheme(theme: FurnitureTheme): void {
        // 根据主题选择合适的家具
        let templateIds: number[] = [];

        switch (theme) {
            case FurnitureTheme.Modern:
                templateIds = [1, 3]; // 现代椅子和沙发
                break;
            case FurnitureTheme.Classic:
                templateIds = [2, 4]; // 古典桌子和壁画
                break;
            case FurnitureTheme.Industrial:
                templateIds = [1, 2]; // 混合风格
                break;
            default:
                templateIds = [1];
                break;
        }

        // 创建家具布局
        this.placedFurnitures = templateIds.map((id, index) => {
            const template = this.furnitureTemplates.get(id);
            return {
                id: `test_${theme}_${index}`,
                templateId: id,
                position: new Vec2(2 + index * 2, 2 + index),
                rotation: Rotation.Deg0,
                currentSize: template ? template.baseSize.clone() : new Vec2(1, 1),
                placedTime: Date.now()
            };
        });
    }

    /**
     * 测试导入/导出功能
     */
    private async testImportExport() {
        log("--- 测试导入/导出功能 ---");

        if (!this.layoutManager) return;

        // 创建一个测试布局
        this.createTestLayout();
        await this.wait(300);

        const saveResult = await this.layoutManager.quickSave("导出测试布局", {
            autoGenerateName: false,
            includeScore: true,
            includeThumbnail: false
        });

        if (!saveResult.success || !saveResult.layoutId) {
            log("创建测试布局失败");
            return;
        }

        // 测试导出
        log("测试导出布局...");
        const exportData = await this.layoutManager.exportLayout(saveResult.layoutId);
        if (exportData) {
            log("导出成功，数据长度:", exportData.length);

            // 测试导入
            log("测试导入布局...");
            const importResult = await this.layoutManager.importLayout(exportData, "导入测试布局");
            log("导入结果:", importResult.success ? "成功" : importResult.message);

            // 清理导入的布局
            if (importResult.success && importResult.layoutId) {
                await this.layoutManager.deleteLayout(importResult.layoutId);
            }
        } else {
            log("导出失败");
        }

        // 清理原始测试布局
        await this.layoutManager.deleteLayout(saveResult.layoutId);
    }

    /**
     * 测试自动保存功能
     */
    private async testAutoSave() {
        log("--- 测试自动保存功能 ---");

        if (!this.layoutManager) return;

        // 获取自动保存前的布局数量
        const beforeLayouts = await this.layoutManager.searchLayouts(
            { category: LayoutCategory.AutoSave }
        );
        log(`自动保存前的布局数量: ${beforeLayouts.length}`);

        // 模拟一些操作来触发自动保存
        for (let i = 0; i < 3; i++) {
            this.createTestLayoutWithTheme(FurnitureTheme.Modern + i);
            await this.wait(500);

            // 手动创建自动保存布局
            // 注意：这里假设 RoomLayoutManager 有一个内部方法来创建自动保存
            // 实际实现可能需要调整
            await this.layoutManager.quickSave(`自动保存_${i}`, {
                autoGenerateName: true,
                includeScore: false
            });
        }

        // 检查自动保存后的布局数量
        const afterLayouts = await this.layoutManager.searchLayouts(
            { category: LayoutCategory.AutoSave }
        );
        log(`自动保存后的布局数量: ${afterLayouts.length}`);

        // 清理测试的自动保存布局
        for (const layout of afterLayouts) {
            if (layout.name.startsWith("自动保存_")) {
                await this.layoutManager.deleteLayout(layout.id);
            }
        }
    }

    /**
     * 测试存储统计和清理
     */
    private async testStorageManagement() {
        log("--- 测试存储统计和清理 ---");
        
        if (!this.layoutManager) return;

        // 获取存储统计信息
        log("获取存储统计信息...");
        const stats = await this.layoutManager.getStorageStats();
        log("存储统计:", {
            总数量: stats.total,
            用户保存: stats.byCategory.user_saved || 0,
            自动保存: stats.byCategory.auto_save || 0,
            平均评分: stats.averageScore.toFixed(1),
            存储大小: `${(stats.totalSize / 1024).toFixed(1)}KB`
        });

        // 测试清理功能
        log("测试存储清理...");
        const removedCount = await this.layoutManager.cleanupStorage({
            removeAutoSaves: false, // 不删除自动保存，避免影响其他测试
            removeOldLayouts: 1, // 删除1天前的布局（测试用）
            maxLayouts: 100 // 最多保留100个布局
        });
        log(`清理完成，删除了 ${removedCount} 个布局`);

        // 再次获取统计信息
        const statsAfter = await this.layoutManager.getStorageStats();
        log("清理后存储统计:", {
            总数量: statsAfter.total,
            存储大小: `${(statsAfter.totalSize / 1024).toFixed(1)}KB`
        });
    }

    /**
     * 测试复制功能
     */
    private async testDuplication() {
        log("--- 测试布局复制功能 ---");

        if (!this.layoutManager) return;

        // 创建原始布局
        this.createTestLayout();
        await this.wait(300);

        const originalResult = await this.layoutManager.quickSave("原始布局", {
            autoGenerateName: false,
            includeScore: true,
            includeThumbnail: false
        });

        if (!originalResult.success || !originalResult.layoutId) {
            log("创建原始布局失败");
            return;
        }

        // 测试复制
        log("测试复制布局...");
        const duplicateResult = await this.layoutManager.duplicateLayout(
            originalResult.layoutId,
            "复制的布局"
        );
        log("复制结果:", duplicateResult.success ? "成功" : duplicateResult.message);

        // 清理测试数据
        await this.layoutManager.deleteLayout(originalResult.layoutId);
        if (duplicateResult.success && duplicateResult.layoutId) {
            await this.layoutManager.deleteLayout(duplicateResult.layoutId);
        }
    }

    /**
     * 测试布局验证
     */
    private async testLayoutValidation() {
        log("--- 测试布局数据验证 ---");

        // 测试无效的JSON数据
        const invalidJson = '{"invalid": "data"}';
        const importResult1 = await this.layoutManager?.importLayout(invalidJson);
        log("导入无效数据结果:", importResult1?.success ? "成功" : importResult1?.message);

        // 测试格式错误的JSON
        const malformedJson = '{"name": "test", "furnitures": "not_array"}';
        const importResult2 = await this.layoutManager?.importLayout(malformedJson);
        log("导入格式错误数据结果:", importResult2?.success ? "成功" : importResult2?.message);
    }

    /**
     * 手动触发完整测试
     */
    public async runFullTest() {
        await this.runTests();
        await this.testDuplication();
        await this.testLayoutValidation();
    }

    /**
     * 清理所有测试数据
     */
    public async cleanupTestData() {
        if (!this.layoutManager) return;

        log("清理所有测试数据...");
        const allLayouts = await this.layoutManager.searchLayouts();
        let cleanedCount = 0;

        for (const layout of allLayouts) {
            if (layout.name.includes("测试") || layout.name.includes("导入") || layout.name.includes("复制")) {
                const success = await this.layoutManager.deleteLayout(layout.id);
                if (success) {
                    cleanedCount++;
                }
            }
        }

        log(`清理完成，删除了 ${cleanedCount} 个测试布局`);
    }

    /**
     * 等待指定时间
     */
    private wait(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
