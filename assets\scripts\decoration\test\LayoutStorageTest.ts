/**
 * 布局存储功能测试
 * 测试保存/加载功能的完整性
 */

import { Component, _decorator, Vec2, log } from "cc";
import { DecorationDemoManager } from "../DecorationDemoManager";
import { RoomLayoutManager } from "../RoomLayoutManager";
import { LayoutCategory, SortOption } from "../RoomLayoutStorage";
import { Rotation, FurnitureTheme } from "../DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('LayoutStorageTest')
export class LayoutStorageTest extends Component {
    @property({ type: DecorationDemoManager })
    demoManager: DecorationDemoManager | null = null;

    private layoutManager: RoomLayoutManager | null = null;

    protected onLoad() {
        // 组件初始化
    }

    protected start() {
        if (this.demoManager) {
            this.layoutManager = this.demoManager.getLayoutManager();
        }
        
        // 延迟执行测试，确保组件初始化完成
        setTimeout(() => {
            this.runTests();
        }, 1000);
    }

    /**
     * 运行所有测试
     */
    private async runTests() {
        log("=== 开始布局存储功能测试 ===");
        
        try {
            // 测试基本保存/加载功能
            await this.testBasicSaveLoad();
            
            // 测试搜索和过滤功能
            await this.testSearchAndFilter();
            
            // 测试导入/导出功能
            await this.testImportExport();
            
            // 测试自动保存功能
            await this.testAutoSave();
            
            // 测试存储统计和清理
            await this.testStorageManagement();
            
            log("=== 布局存储功能测试完成 ===");
        } catch (error) {
            log("测试过程中出现错误:", error);
        }
    }

    /**
     * 测试基本保存/加载功能
     */
    private async testBasicSaveLoad() {
        log("--- 测试基本保存/加载功能 ---");
        
        if (!this.demoManager || !this.layoutManager) {
            log("演示管理器或布局管理器未初始化");
            return;
        }

        // 切换到有家具的步骤
        this.demoManager.goToStep(3); // 假设步骤3有一些家具
        await this.wait(500);

        // 测试快速保存
        log("测试快速保存...");
        const saveResult = await this.demoManager.quickSaveCurrentLayout("测试布局1");
        log("快速保存结果:", saveResult.success ? "成功" : saveResult.message);

        if (saveResult.success && saveResult.layoutId) {
            // 测试加载
            log("测试加载布局...");
            const loadResult = await this.demoManager.loadLayout(saveResult.layoutId);
            log("加载结果:", loadResult.success ? "成功" : loadResult.message);

            // 测试删除
            log("测试删除布局...");
            const deleteResult = await this.demoManager.deleteLayout(saveResult.layoutId);
            log("删除结果:", deleteResult ? "成功" : "失败");
        }
    }

    /**
     * 测试搜索和过滤功能
     */
    private async testSearchAndFilter() {
        log("--- 测试搜索和过滤功能 ---");
        
        if (!this.layoutManager) return;

        // 创建一些测试布局
        const testLayouts = [
            { name: "现代风格测试", theme: FurnitureTheme.Modern },
            { name: "古典风格测试", theme: FurnitureTheme.Classic },
            { name: "工业风格测试", theme: FurnitureTheme.Industrial }
        ];

        const savedLayoutIds: string[] = [];

        for (const testLayout of testLayouts) {
            // 切换到不同步骤创建不同布局
            this.demoManager?.goToStep(Math.floor(Math.random() * 4) + 1);
            await this.wait(300);

            const result = await this.layoutManager.quickSave(testLayout.name, {
                autoGenerateName: false,
                includeScore: true,
                tags: [testLayout.theme.toString()]
            });

            if (result.success && result.layoutId) {
                savedLayoutIds.push(result.layoutId);
            }
        }

        // 测试获取最近布局
        log("获取最近布局...");
        const recentLayouts = await this.layoutManager.getRecentLayouts(5);
        log(`找到 ${recentLayouts.length} 个最近布局`);

        // 测试按主题搜索
        log("按主题搜索布局...");
        const modernLayouts = await this.layoutManager.getLayoutsByTheme(FurnitureTheme.Modern, 10);
        log(`找到 ${modernLayouts.length} 个现代风格布局`);

        // 测试高分布局
        log("获取高分布局...");
        const topLayouts = await this.layoutManager.getTopScoredLayouts(5);
        log(`找到 ${topLayouts.length} 个高分布局`);

        // 清理测试数据
        for (const layoutId of savedLayoutIds) {
            await this.layoutManager.deleteLayout(layoutId);
        }
    }

    /**
     * 测试导入/导出功能
     */
    private async testImportExport() {
        log("--- 测试导入/导出功能 ---");
        
        if (!this.demoManager || !this.layoutManager) return;

        // 创建一个测试布局
        this.demoManager.goToStep(2);
        await this.wait(300);

        const saveResult = await this.demoManager.quickSaveCurrentLayout("导出测试布局");
        if (!saveResult.success || !saveResult.layoutId) {
            log("创建测试布局失败");
            return;
        }

        // 测试导出
        log("测试导出布局...");
        const exportData = await this.layoutManager.exportLayout(saveResult.layoutId);
        if (exportData) {
            log("导出成功，数据长度:", exportData.length);

            // 测试导入
            log("测试导入布局...");
            const importResult = await this.layoutManager.importLayout(exportData, "导入测试布局");
            log("导入结果:", importResult.success ? "成功" : importResult.message);

            // 清理导入的布局
            if (importResult.success && importResult.layoutId) {
                await this.layoutManager.deleteLayout(importResult.layoutId);
            }
        } else {
            log("导出失败");
        }

        // 清理原始测试布局
        await this.layoutManager.deleteLayout(saveResult.layoutId);
    }

    /**
     * 测试自动保存功能
     */
    private async testAutoSave() {
        log("--- 测试自动保存功能 ---");
        
        if (!this.layoutManager) return;

        // 获取自动保存前的布局数量
        const beforeLayouts = await this.layoutManager.searchLayouts(
            { category: LayoutCategory.AutoSave }
        );
        log(`自动保存前的布局数量: ${beforeLayouts.length}`);

        // 模拟一些操作来触发自动保存
        if (this.demoManager) {
            this.demoManager.goToStep(1);
            await this.wait(500);
            this.demoManager.goToStep(2);
            await this.wait(500);
            this.demoManager.goToStep(3);
            await this.wait(500);
        }

        // 手动触发自动保存（如果有相关方法）
        // 这里可能需要根据实际实现调整

        // 检查自动保存后的布局数量
        const afterLayouts = await this.layoutManager.searchLayouts(
            { category: LayoutCategory.AutoSave }
        );
        log(`自动保存后的布局数量: ${afterLayouts.length}`);
    }

    /**
     * 测试存储统计和清理
     */
    private async testStorageManagement() {
        log("--- 测试存储统计和清理 ---");
        
        if (!this.layoutManager) return;

        // 获取存储统计信息
        log("获取存储统计信息...");
        const stats = await this.layoutManager.getStorageStats();
        log("存储统计:", {
            总数量: stats.total,
            用户保存: stats.byCategory.user_saved || 0,
            自动保存: stats.byCategory.auto_save || 0,
            平均评分: stats.averageScore.toFixed(1),
            存储大小: `${(stats.totalSize / 1024).toFixed(1)}KB`
        });

        // 测试清理功能
        log("测试存储清理...");
        const removedCount = await this.layoutManager.cleanupStorage({
            removeAutoSaves: false, // 不删除自动保存，避免影响其他测试
            removeOldLayouts: 1, // 删除1天前的布局（测试用）
            maxLayouts: 100 // 最多保留100个布局
        });
        log(`清理完成，删除了 ${removedCount} 个布局`);

        // 再次获取统计信息
        const statsAfter = await this.layoutManager.getStorageStats();
        log("清理后存储统计:", {
            总数量: statsAfter.total,
            存储大小: `${(statsAfter.totalSize / 1024).toFixed(1)}KB`
        });
    }

    /**
     * 测试复制功能
     */
    private async testDuplication() {
        log("--- 测试布局复制功能 ---");
        
        if (!this.demoManager || !this.layoutManager) return;

        // 创建原始布局
        this.demoManager.goToStep(3);
        await this.wait(300);

        const originalResult = await this.demoManager.quickSaveCurrentLayout("原始布局");
        if (!originalResult.success || !originalResult.layoutId) {
            log("创建原始布局失败");
            return;
        }

        // 测试复制
        log("测试复制布局...");
        const duplicateResult = await this.layoutManager.duplicateLayout(
            originalResult.layoutId,
            "复制的布局"
        );
        log("复制结果:", duplicateResult.success ? "成功" : duplicateResult.message);

        // 清理测试数据
        await this.layoutManager.deleteLayout(originalResult.layoutId);
        if (duplicateResult.success && duplicateResult.layoutId) {
            await this.layoutManager.deleteLayout(duplicateResult.layoutId);
        }
    }

    /**
     * 测试布局验证
     */
    private async testLayoutValidation() {
        log("--- 测试布局数据验证 ---");

        // 测试无效的JSON数据
        const invalidJson = '{"invalid": "data"}';
        const importResult1 = await this.layoutManager?.importLayout(invalidJson);
        log("导入无效数据结果:", importResult1?.success ? "成功" : importResult1?.message);

        // 测试格式错误的JSON
        const malformedJson = '{"name": "test", "furnitures": "not_array"}';
        const importResult2 = await this.layoutManager?.importLayout(malformedJson);
        log("导入格式错误数据结果:", importResult2?.success ? "成功" : importResult2?.message);
    }

    /**
     * 手动触发完整测试
     */
    public async runFullTest() {
        await this.runTests();
        await this.testDuplication();
        await this.testLayoutValidation();
    }

    /**
     * 清理所有测试数据
     */
    public async cleanupTestData() {
        if (!this.layoutManager) return;

        log("清理所有测试数据...");
        const allLayouts = await this.layoutManager.searchLayouts();
        let cleanedCount = 0;

        for (const layout of allLayouts) {
            if (layout.name.includes("测试") || layout.name.includes("导入") || layout.name.includes("复制")) {
                const success = await this.layoutManager.deleteLayout(layout.id);
                if (success) {
                    cleanedCount++;
                }
            }
        }

        log(`清理完成，删除了 ${cleanedCount} 个测试布局`);
    }

    /**
     * 等待指定时间
     */
    private wait(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
