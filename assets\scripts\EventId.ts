import { ccenum } from "cc";

export enum EventId {
}

export enum EventTag {
    None = "None", //无
    UpdateScore = "UpdateScore", //更新矿石得分
    SetAdAddScore = "SetAdAddScore", //设置广告矿石得分
    MineLayerSelect = "MineLayerSelect", //矿石层数选择
    PageChange = "PageChange", //页面切换
    OnMainPage = "OnMainPage", //主页面
    ArrangeBagItem = "ArrangeBagItem",//整理背包拖拽道具事件
    StartDig = "StartDig",//开始挖掘事件
    BagItemSelected = "BagItemSelected",//盘外道具选中事件
    MainUILayoutUpdate = "MainUILayoutUpdate", //主界面布局更新事件
    MainMineUIUpdate = "MainMineUIUpdate", //主界面矿洞页面更新事件
    AddWeaponCard = "AddWeaponCard",//添加武器卡片
    RoundChangeReady = "RoundChangeReady",//回合数变化准备事件
    RoundChange = "RoundChange",//回合数变化事件
    GameOver = "GameOver",//游戏结束事件
    StartGame = "StartGame",//开始游戏事件
    LevelUPInGame = "LevelUPInGame",//游戏内升级事件
    OnAddExp = "OnAddExp",//经验值变化事件
    RogueEntrySelectFinish = "RogueEntrySelectFinish",//肉鸽词条选择完成事件
    RefreshNewWeapon = "RefreshNewWeapon",//刷新新武器事件
    DragRoleItem = "DragRoleItem",//拖拽卡牌上阵事件
    SilverChanged = "SilverChanged",//银币变化事件
    MineBreak = "MineBreak",//矿石破碎事件
    DepthUpdate = "DepthUpdate",//深度更新事件
    DepthUpdateAnimationFinish = "DepthUpdateAnimationFinish",//深度更新动画完成事件
    WeaponHurt = "WeaponHurt",//武器伤害事件
    AddMinerCard = "AddMinerCard",//添加雪人卡片事件
    RemoveMinerCard = "RemoveMinerCard",//移除雪人卡片事件
    MinerCardHpChanged = "MinerCardHpChanged",//雪人卡片血量变化事件
    MinerCardAtkChanged = "MinerCardAtkChanged",//雪人卡片攻击力变化事件
    UpgradeUIUp = "UpgradeUIUp",//升级事件
    EffectChanged = "EffectChanged",//效果变化事件
    EquipmentHpChanged = "EquipmentHpChanged",//装备血量变化事件
    EquipmentAtkChanged = "EquipmentAtkChanged",//装备攻击力变化事件
    MoneyFly = "MoneyFly",//从钱包收银币
    UserDataUpdate = "UserDataUpdate",//用户数据更新事件
    CloseChargeRewardUI = "CloseChargeRewardUI",//关闭获得物品页面
    UnlockAllUI = "UnlockAllUI",//解锁所有UI事件
    CloseTreasureTip = "CloseTreasureTip",//关闭宝箱提示事件
    GamblingTotalCountChanged = "GamblingTotalCountChanged",//赌博总次数变化事件
    ReceiveCumulativeCheckInReward = "ReceiveCumulativeCheckInReward",//领取累计签到奖励事件
    RequestUpdateHeatMap = "RequestUpdateHeatMap",//请求更新热力图事件
    RequestFlowField = "RequestFlowField",//请求更新流场事件
    RequestShowExpUp = "RequestShowExpUp",//请求显示经验值提升事件
    RequestShowResource = "RequestShowResource",//请求显示资源事件
    RequestBuyEnergy = "RequestBuyEnergy",//请求购买能量事件
    SkillIconOfBagItemDT = "SkillIconOfBagItemDT",//点击道具详情页的技能icon
    JumpTOBuyMoney = "JumpTOBuyMoney",//跳转到购买金币页面
    JumpTOBuyDiamond = "JumpTOBuyDiamond",//跳转到购买钻石页面
    BoardMaskTouch = "BoardMaskTouch",//点击操作区遮罩事件
    BoardMaskLeave = "BoardMaskLeave",//离开操作区遮罩事件
    Respawn = "Respawn",//重生事件
    MineChestOpen = "MineChestOpen",//矿石宝箱打开事件
    HandUpOneItem = "HandUpOneItem",//手指抬起一个道具事件
    ItemSetOnBagGrid = "ItemSetOnBagGrid",//道具放入背包格子里事件
    TwoItemTurnOne = "TwoItemTurnOne",//两个道具转化为一个事件
    RequestGetGuideStep1ItemPosAndBagPos = "RequestGetGuideStep1ItemPosAndBagPos",//请求获取引导步骤1的道具位置和背包位置事件
    RequestGetGuideStep2ItemPosAndBagPos = "RequestGetGuideStep2ItemPosAndBagPos",//请求获取引导步骤2的道具位置和背包位置事件
    SelectItemEmpty = "SelectItemEmpty",//可选择道具为空事件
    CardActivated = "CardActivated",//卡牌激活事件

    // 装饰系统事件
    FurniturePlaced = "FurniturePlaced",//家具放置事件
    FurnitureRemoved = "FurnitureRemoved",//家具移除事件
    FurnitureMoved = "FurnitureMoved",//家具移动事件
    FurnitureRotated = "FurnitureRotated",//家具旋转事件
    ScoreChanged = "ScoreChanged",//评分变化事件
    RoomChanged = "RoomChanged",//房间切换事件
    MultiRoomModeChanged = "MultiRoomModeChanged",//多房间模式变化事件
}

ccenum(EventTag)