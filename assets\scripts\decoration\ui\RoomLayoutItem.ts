/**
 * 房间布局项组件
 * 用于显示单个布局项目
 */

import { Component, Label, Button, _decorator } from "cc";
import { SavedRoomLayout } from "./RoomLayoutStorage";
import { RoomLayoutUI } from "./RoomLayoutUI";

const { ccclass, property } = _decorator;

@ccclass('RoomLayoutItem')
export class RoomLayoutItem extends Component {
    @property({ type: Label })
    nameLabel: Label | null = null;

    @property({ type: Label })
    scoreLabel: Label | null = null;

    @property({ type: Label })
    timeLabel: Label | null = null;

    @property({ type: Button })
    loadButton: Button | null = null;

    @property({ type: Button })
    deleteButton: Button | null = null;

    @property({ type: Button })
    duplicateButton: Button | null = null;

    @property({ type: Button })
    exportButton: Button | null = null;

    private layout: SavedRoomLayout | null = null;
    private parentUI: RoomLayoutUI | null = null;

    setup(layout: SavedRoomLayout, parentUI: RoomLayoutUI): void {
        this.layout = layout;
        this.parentUI = parentUI;

        // 更新显示
        if (this.nameLabel) {
            this.nameLabel.string = layout.name;
        }
        if (this.scoreLabel) {
            this.scoreLabel.string = layout.score ? layout.score.toString() : "未评分";
        }
        if (this.timeLabel) {
            this.timeLabel.string = new Date(layout.modifiedTime).toLocaleDateString();
        }

        // 设置按钮事件
        this.loadButton?.node.on(Button.EventType.CLICK, this.onLoadLayout, this);
        this.deleteButton?.node.on(Button.EventType.CLICK, this.onDelete, this);
        this.duplicateButton?.node.on(Button.EventType.CLICK, this.onDuplicate, this);
        this.exportButton?.node.on(Button.EventType.CLICK, this.onExport, this);
    }

    private onLoadLayout(): void {
        if (this.layout && this.parentUI) {
            this.parentUI.loadLayout(this.layout.id);
        }
    }

    private onDelete(): void {
        if (this.layout && this.parentUI) {
            this.parentUI.deleteLayout(this.layout.id);
        }
    }

    private onDuplicate(): void {
        if (this.layout && this.parentUI) {
            this.parentUI.duplicateLayout(this.layout.id);
        }
    }

    private onExport(): void {
        if (this.layout && this.parentUI) {
            this.parentUI.exportLayout(this.layout.id);
        }
    }
}
