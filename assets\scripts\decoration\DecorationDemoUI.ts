/**
 * 装饰演示UI组件
 * 提供演示功能的用户界面
 */

import { Component, Label, Button, _decorator, Node, Vec2 } from "cc";
import { DecorationDemoManager, DemoStep, DemoState } from "./DecorationDemoManager";
import { RoomScoreDetails } from "./DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('DecorationDemoUI')
export class DecorationDemoUI extends Component {
    @property({ type: Label, tooltip: "步骤标题显示" })
    stepTitleLabel: Label | null = null;

    @property({ type: Label, tooltip: "步骤描述显示" })
    stepDescriptionLabel: Label | null = null;

    @property({ type: Label, tooltip: "当前步骤显示" })
    currentStepLabel: Label | null = null;

    @property({ type: Label, tooltip: "总评分显示" })
    totalScoreLabel: Label | null = null;

    @property({ type: Label, tooltip: "主题评分显示" })
    themeScoreLabel: Label | null = null;

    @property({ type: Label, tooltip: "数量评分显示" })
    quantityScoreLabel: Label | null = null;

    @property({ type: Label, tooltip: "价值评分显示" })
    valueScoreLabel: Label | null = null;

    @property({ type: Label, tooltip: "布局评分显示" })
    layoutScoreLabel: Label | null = null;

    @property({ type: Label, tooltip: "主导主题显示" })
    dominantThemeLabel: Label | null = null;

    @property({ type: Label, tooltip: "状态显示" })
    statusLabel: Label | null = null;

    @property({ type: Button, tooltip: "上一步按钮" })
    prevButton: Button | null = null;

    @property({ type: Button, tooltip: "下一步按钮" })
    nextButton: Button | null = null;

    @property({ type: Button, tooltip: "重置按钮" })
    resetButton: Button | null = null;

    @property({ type: Button, tooltip: "随机变化按钮" })
    randomizeButton: Button | null = null;

    @property({ type: Button, tooltip: "验证测试按钮" })
    validateButton: Button | null = null;

    @property({ type: Button, tooltip: "快速保存按钮" })
    quickSaveButton: Button | null = null;

    @property({ type: Button, tooltip: "导出布局按钮" })
    exportButton: Button | null = null;

    @property({ type: DecorationDemoManager, tooltip: "演示管理器" })
    demoManager: DecorationDemoManager | null = null;

    protected onLoad() {
        this.setupEventListeners();
        this.setupDemoManagerCallbacks();
    }

    protected start() {
        this.updateUI();
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 按钮事件
        this.prevButton?.node.on(Button.EventType.CLICK, this.onPrevStep, this);
        this.nextButton?.node.on(Button.EventType.CLICK, this.onNextStep, this);
        this.resetButton?.node.on(Button.EventType.CLICK, this.onReset, this);
        this.randomizeButton?.node.on(Button.EventType.CLICK, this.onRandomize, this);
        this.validateButton?.node.on(Button.EventType.CLICK, this.onValidateTest, this);
        this.quickSaveButton?.node.on(Button.EventType.CLICK, this.onQuickSave, this);
        this.exportButton?.node.on(Button.EventType.CLICK, this.onExport, this);
    }

    /**
     * 设置演示管理器回调
     */
    private setupDemoManagerCallbacks(): void {
        if (!this.demoManager) return;

        this.demoManager.setOnStepChanged((step: number, stepData: DemoStep) => {
            this.onStepChanged(step, stepData);
        });

        this.demoManager.setOnScoreUpdated((score: RoomScoreDetails) => {
            this.onScoreUpdated(score);
        });

        this.demoManager.setOnStateChanged((state: DemoState) => {
            this.onStateChanged(state);
        });

        this.demoManager.setOnLayoutSaved((result) => {
            this.showLayoutOperationResult("保存", result);
        });

        this.demoManager.setOnLayoutLoaded((result) => {
            this.showLayoutOperationResult("加载", result);
        });
    }

    /**
     * 步骤变化回调
     */
    private onStepChanged(step: number, stepData: DemoStep): void {
        if (this.stepTitleLabel) {
            this.stepTitleLabel.string = stepData.title;
        }

        if (this.stepDescriptionLabel) {
            this.stepDescriptionLabel.string = stepData.description;
        }

        if (this.currentStepLabel) {
            const totalSteps = this.demoManager?.getDemoSteps().length || 0;
            this.currentStepLabel.string = `${step + 1} / ${totalSteps}`;
        }

        this.updateButtonStates();
    }

    /**
     * 评分更新回调
     */
    private onScoreUpdated(score: RoomScoreDetails): void {
        if (this.totalScoreLabel) {
            this.totalScoreLabel.string = score.totalScore.toString();
        }

        if (this.themeScoreLabel) {
            this.themeScoreLabel.string = score.themeScore.toFixed(1);
        }

        if (this.quantityScoreLabel) {
            this.quantityScoreLabel.string = score.quantityScore.toFixed(1);
        }

        if (this.valueScoreLabel) {
            this.valueScoreLabel.string = score.valueScore.toFixed(1);
        }

        if (this.layoutScoreLabel) {
            this.layoutScoreLabel.string = score.layoutScore.toFixed(1);
        }

        if (this.dominantThemeLabel) {
            this.dominantThemeLabel.string = this.getThemeName(score.dominantTheme);
        }
    }

    /**
     * 状态变化回调
     */
    private onStateChanged(state: DemoState): void {
        if (this.statusLabel) {
            this.statusLabel.string = this.getStateDescription(state);
        }

        this.updateButtonStates();
    }

    /**
     * 上一步按钮点击
     */
    private onPrevStep(): void {
        this.demoManager?.prevStep();
    }

    /**
     * 下一步按钮点击
     */
    private onNextStep(): void {
        this.demoManager?.nextStep();
    }

    /**
     * 重置按钮点击
     */
    private onReset(): void {
        this.demoManager?.resetDemo();
    }

    /**
     * 随机变化按钮点击
     */
    private onRandomize(): void {
        if (!this.demoManager) return;

        const currentFurnitures = this.demoManager.getCurrentStepFurnitures();
        if (currentFurnitures.length === 0) {
            console.warn("当前步骤没有家具可以随机变化");
            return;
        }

        // 随机选择最后一个家具进行变化
        const lastFurniture = currentFurnitures[currentFurnitures.length - 1];
        const result = this.demoManager.randomizeFurniture(lastFurniture.id);

        if (result && result.success) {
            console.log("随机变化成功:", result);
            // 这里可以添加视觉反馈
        } else {
            console.warn("随机变化失败:", result?.reason || "未知原因");
        }
    }

    /**
     * 验证测试按钮点击
     */
    private onValidateTest(): void {
        if (!this.demoManager) return;

        // 测试一些位置的验证结果
        const testPositions = [
            { x: 0, y: 0, desc: "左上角" },
            { x: 5, y: 3, desc: "中央位置" },
            { x: 11, y: 7, desc: "右下角" }
        ];

        console.log("=== 位置验证测试 ===");
        for (const pos of testPositions) {
            const result = this.demoManager.validateFurniturePlacement(
                1, // 椅子模板ID
                new Vec2(pos.x, pos.y),
                0 // 无旋转
            );
            console.log(`${pos.desc} (${pos.x},${pos.y}):`, result.isValid ? "可放置" : result.reason);
        }
    }

    /**
     * 快速保存按钮点击
     */
    private async onQuickSave(): Promise<void> {
        if (!this.demoManager) return;

        const currentFurnitures = this.demoManager.getCurrentStepFurnitures();
        if (currentFurnitures.length === 0) {
            console.warn("当前步骤没有家具可以保存");
            return;
        }

        console.log("正在快速保存当前布局...");
        await this.demoManager.quickSaveCurrentLayout();
    }

    /**
     * 导出按钮点击
     */
    private async onExport(): Promise<void> {
        if (!this.demoManager) return;

        const currentFurnitures = this.demoManager.getCurrentStepFurnitures();
        if (currentFurnitures.length === 0) {
            console.warn("当前步骤没有家具可以导出");
            return;
        }

        console.log("正在导出当前布局...");
        const exportData = await this.demoManager.exportCurrentLayout();

        if (exportData) {
            console.log("布局导出成功:");
            console.log(exportData);
            // 这里可以添加复制到剪贴板或下载文件的功能
        } else {
            console.warn("布局导出失败");
        }
    }

    /**
     * 显示布局操作结果
     */
    private showLayoutOperationResult(operation: string, result: any): void {
        if (result.success) {
            console.log(`${operation}成功:`, result.message);
        } else {
            console.warn(`${operation}失败:`, result.message);
        }
    }

    /**
     * 更新UI显示
     */
    private updateUI(): void {
        if (!this.demoManager) return;

        const currentStep = this.demoManager.getCurrentStep();
        const demoSteps = this.demoManager.getDemoSteps();
        
        if (currentStep >= 0 && currentStep < demoSteps.length) {
            this.onStepChanged(currentStep, demoSteps[currentStep]);
        }

        const score = this.demoManager.calculateCurrentScore();
        this.onScoreUpdated(score);

        const state = this.demoManager.getCurrentState();
        this.onStateChanged(state);
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        if (!this.demoManager) return;

        const currentStep = this.demoManager.getCurrentStep();
        const totalSteps = this.demoManager.getDemoSteps().length;
        const state = this.demoManager.getCurrentState();
        const isIdle = state === DemoState.Idle;

        // 上一步按钮
        if (this.prevButton) {
            this.prevButton.interactable = isIdle && currentStep > 0;
        }

        // 下一步按钮
        if (this.nextButton) {
            this.nextButton.interactable = isIdle && currentStep < totalSteps - 1;
        }

        // 重置按钮
        if (this.resetButton) {
            this.resetButton.interactable = isIdle;
        }

        // 随机变化按钮
        if (this.randomizeButton) {
            const hasFurniture = this.demoManager.getCurrentStepFurnitures().length > 0;
            this.randomizeButton.interactable = isIdle && hasFurniture;
        }

        // 验证测试按钮
        if (this.validateButton) {
            this.validateButton.interactable = isIdle;
        }

        // 快速保存按钮
        if (this.quickSaveButton) {
            const hasFurniture = this.demoManager.getCurrentStepFurnitures().length > 0;
            this.quickSaveButton.interactable = isIdle && hasFurniture;
        }

        // 导出按钮
        if (this.exportButton) {
            const hasFurniture = this.demoManager.getCurrentStepFurnitures().length > 0;
            this.exportButton.interactable = isIdle && hasFurniture;
        }
    }

    /**
     * 获取主题名称
     */
    private getThemeName(theme: any): string {
        const themeNames: { [key: number]: string } = {
            1: "现代风格",
            2: "古典风格",
            3: "工业风格",
            4: "自然风格",
            5: "简约风格"
        };
        return themeNames[theme] || "无";
    }

    /**
     * 获取状态描述
     */
    private getStateDescription(state: DemoState): string {
        switch (state) {
            case DemoState.Idle:
                return "就绪";
            case DemoState.Playing:
                return "播放中";
            case DemoState.Randomizing:
                return "随机变化中";
            case DemoState.StepChanging:
                return "切换步骤中";
            default:
                return "未知状态";
        }
    }
}
