# 分阶段雪人激活功能测试文档

## 测试目标
验证分阶段雪人激活功能是否按照需求正确工作：
- Stage 1 Round 1: 只有第1个雪人生效
- Stage 1 Round 2: 激活第2个雪人
- Stage 2 Round 1: 激活第3个雪人

## 测试环境准备

### 1. 配置文件检查
确认 `assets/resources/json/cardActivationConfig.json` 文件存在且内容正确：
```json
{
  "rules": [
    {
      "stageId": 1,
      "round": 2,
      "cardId": 2,
      "description": "Stage 1 Round 2: 激活第2个雪人"
    },
    {
      "stageId": 2,
      "round": 1,
      "cardId": 3,
      "description": "Stage 2 Round 1: 激活第3个雪人"
    }
  ]
}
```

### 2. 代码检查点
- StageDataMgr.loadCardActivationConfig() 正确加载配置
- CardMgr.resetCardActivationState() 正确重置卡牌状态
- 事件 EventTag.CardActivated 正确触发

## 测试用例

### 测试用例1: Stage 1 Round 1
**操作步骤:**
1. 启动游戏
2. 开始 Stage 1
3. 检查当前回合为 Round 1

**预期结果:**
- 只有第1个雪人（cardId: 1）处于激活状态
- 第2、3个雪人处于不激活状态
- 控制台输出: "初始化卡牌激活状态: Stage 1 Round 1, 激活卡牌: [1]"

**验证方法:**
- 检查 CardMgr.getInstance().isCardActive(1) 返回 true
- 检查 CardMgr.getInstance().isCardActive(2) 返回 false
- 检查 CardMgr.getInstance().isCardActive(3) 返回 false
- 在挖矿界面只能看到1个雪人在工作

### 测试用例2: Stage 1 Round 1 -> Round 2
**操作步骤:**
1. 在 Stage 1 Round 1 的基础上
2. 完成当前回合，触发回合切换
3. 进入 Round 2

**预期结果:**
- 第1、2个雪人处于激活状态
- 第3个雪人仍处于不激活状态
- 控制台输出: "激活卡牌: Stage 1 Round 2 -> Card 2"

**验证方法:**
- 检查 CardMgr.getInstance().isCardActive(1) 返回 true
- 检查 CardMgr.getInstance().isCardActive(2) 返回 true
- 检查 CardMgr.getInstance().isCardActive(3) 返回 false
- 在挖矿界面能看到2个雪人在工作
- arrangeUI 中显示2个雪人头像

### 测试用例3: Stage 2 Round 1
**操作步骤:**
1. 开始 Stage 2
2. 检查当前回合为 Round 1

**预期结果:**
- 第1、3个雪人处于激活状态
- 第2个雪人处于不激活状态（因为Stage 2没有激活第2个雪人的规则）
- 控制台输出: "激活卡牌: Stage 2 Round 1 -> Card 3"

**验证方法:**
- 检查 CardMgr.getInstance().isCardActive(1) 返回 true
- 检查 CardMgr.getInstance().isCardActive(2) 返回 false
- 检查 CardMgr.getInstance().isCardActive(3) 返回 true

### 测试用例4: 游戏继续功能
**操作步骤:**
1. 在 Stage 1 Round 2 保存游戏
2. 重启游戏
3. 选择继续游戏

**预期结果:**
- 正确恢复到 Stage 1 Round 2 的卡牌激活状态
- 第1、2个雪人处于激活状态
- 第3个雪人处于不激活状态

## 调试方法

### 1. 控制台日志
关注以下日志输出：
- "卡牌激活配置加载成功:" - 配置加载成功
- "卡牌激活配置加载失败，使用默认行为:" - 配置加载失败
- "激活卡牌: Stage X Round Y -> Card Z" - 卡牌激活
- "初始化卡牌激活状态: Stage X Round Y, 激活卡牌: [...]" - 状态初始化

### 2. 断点调试
在以下方法设置断点：
- StageDataMgr.loadCardActivationConfig()
- StageDataMgr.checkAndActivateCards()
- StageDataMgr.initCardActivationState()
- CardMgr.resetCardActivationState()

### 3. 运行时检查
在浏览器控制台执行：
```javascript
// 检查卡牌激活状态
const cardMgr = window.CardMgr?.getInstance();
if (cardMgr) {
  console.log('Card 1 active:', cardMgr.isCardActive(1));
  console.log('Card 2 active:', cardMgr.isCardActive(2));
  console.log('Card 3 active:', cardMgr.isCardActive(3));
}

// 检查当前关卡回合
const stageMgr = window.StageDataMgr?.getInstance();
if (stageMgr) {
  console.log('Current stage:', stageMgr.curStageId);
  console.log('Current round:', stageMgr.curStageProgress?.round);
}
```

## 已知问题和解决方案

### 问题1: 配置文件加载失败
**症状:** 控制台显示 "卡牌激活配置加载失败"
**解决方案:** 检查配置文件路径和JSON格式是否正确

### 问题2: 卡牌状态不正确
**症状:** 卡牌激活状态与预期不符
**解决方案:** 检查 CardMgr.resetCardActivationState() 方法是否正确执行

### 问题3: 事件未触发
**症状:** 其他系统没有响应卡牌激活
**解决方案:** 检查 EventTag.CardActivated 事件是否正确发送

## 测试完成标准
- 所有测试用例通过
- 控制台无错误日志
- UI显示正确的雪人数量
- 游戏逻辑符合需求规范
