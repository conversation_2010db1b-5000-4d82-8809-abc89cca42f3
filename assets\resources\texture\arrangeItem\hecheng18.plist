<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-帽子.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{295,140}</string>
                <key>spriteSourceSize</key>
                <string>{295,140}</string>
                <key>textureRect</key>
                <string>{{1199,1},{295,140}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-帽子2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{295,151}</string>
                <key>spriteSourceSize</key>
                <string>{295,151}</string>
                <key>textureRect</key>
                <string>{{607,1},{295,151}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-帽子3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{293,151}</string>
                <key>spriteSourceSize</key>
                <string>{293,151}</string>
                <key>textureRect</key>
                <string>{{904,1},{293,151}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-帽子4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,151}</string>
                <key>spriteSourceSize</key>
                <string>{302,151}</string>
                <key>textureRect</key>
                <string>{{1,1},{302,151}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-帽子5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,151}</string>
                <key>spriteSourceSize</key>
                <string>{300,151}</string>
                <key>textureRect</key>
                <string>{{305,1},{300,151}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng18.png</string>
            <key>size</key>
            <string>{1495,153}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:73c350a424b9368b8d93a6f4b3b361e5:9e967733bea914e7a5a06b08c6b905d5:7900a3d42d7a5d775c6ce30f89c15fc0$</string>
            <key>textureFileName</key>
            <string>hecheng18.png</string>
        </dict>
    </dict>
</plist>
