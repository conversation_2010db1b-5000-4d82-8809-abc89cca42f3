
关底宝箱.png
size: 445,1528
format: RGBA8888
filter: Linear,Linear
repeat: none
L-0
  rotate: false
  xy: 283, 164
  size: 158, 157
  orig: 215, 213
  offset: 28, 27
  index: -1
L-1
  rotate: true
  xy: 179, 0
  size: 48, 178
  orig: 59, 218
  offset: 2, 1
  index: -1
L-2
  rotate: true
  xy: 336, 1034
  size: 167, 108
  orig: 184, 117
  offset: 1, 1
  index: -1
L-3
  rotate: true
  xy: 283, 50
  size: 112, 152
  orig: 134, 184
  offset: 2, 1
  index: -1
L-4
  rotate: false
  xy: 268, 323
  size: 177, 172
  orig: 201, 205
  offset: 22, 1
  index: -1
L-5
  rotate: true
  xy: 0, 76
  size: 81, 161
  orig: 97, 187
  offset: 15, 1
  index: -1
L-back
  rotate: false
  xy: 0, 1261
  size: 400, 267
  orig: 479, 387
  offset: 28, 84
  index: -1
cb
  rotate: false
  xy: 0, 636
  size: 344, 159
  orig: 346, 161
  offset: 1, 1
  index: -1
cc
  rotate: false
  xy: 0, 159
  size: 281, 162
  orig: 283, 164
  offset: 1, 1
  index: -1
cu
  rotate: false
  xy: 0, 323
  size: 266, 173
  orig: 268, 175
  offset: 1, 1
  index: -1
cw
  rotate: false
  xy: 0, 797
  size: 346, 246
  orig: 346, 246
  offset: 0, 0
  index: -1
spark-1
  rotate: false
  xy: 379, 26
  size: 15, 22
  orig: 17, 24
  offset: 1, 1
  index: -1
spark-2
  rotate: false
  xy: 359, 22
  size: 18, 26
  orig: 20, 28
  offset: 1, 1
  index: -1
spark-3
  rotate: false
  xy: 0, 498
  size: 386, 136
  orig: 393, 142
  offset: 2, 4
  index: -1
spark-4
  rotate: false
  xy: 0, 1045
  size: 327, 214
  orig: 331, 220
  offset: 2, 4
  index: -1
spark-5
  rotate: true
  xy: 346, 636
  size: 321, 59
  orig: 327, 64
  offset: 4, 3
  index: -1
zhu
  rotate: false
  xy: 0, 7
  size: 177, 67
  orig: 179, 69
  offset: 1, 1
  index: -1
zhu1
  rotate: false
  xy: 339, 959
  size: 107, 36
  orig: 107, 36
  offset: 0, 0
  index: -1
