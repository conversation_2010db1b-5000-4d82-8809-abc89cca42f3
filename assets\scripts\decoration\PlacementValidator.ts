/**
 * 家具摆放验证器
 * 负责验证家具摆放的合法性
 */

import { Vec2 } from "cc";
import { 
    PlacementResult, 
    PlacedFurniture, 
    FurnitureTemplate, 
    Rotation, 
    getRotatedSize,
    isPositionInRoom,
    getFurnitureOccupiedPositions
} from "./DecorationDefine";
import { RoomGrid } from "./RoomGrid";

export class PlacementValidator {
    private roomGrid: RoomGrid;

    constructor(roomGrid: RoomGrid) {
        this.roomGrid = roomGrid;
    }

    /**
     * 验证家具是否可以放置在指定位置
     */
    validatePlacement(
        template: FurnitureTemplate, 
        position: Vec2, 
        rotation: Rotation = Rotation.Deg0
    ): PlacementResult {
        const rotatedSize = getRotatedSize(template.baseSize, rotation);
        
        // 检查边界
        const boundaryCheck = this.checkBoundary(position, rotatedSize);
        if (!boundaryCheck.success) {
            return boundaryCheck;
        }

        // 检查格子占用
        const occupancyCheck = this.checkOccupancy(position, rotatedSize);
        if (!occupancyCheck.success) {
            return occupancyCheck;
        }

        return { success: true };
    }

    /**
     * 验证家具移动是否合法
     */
    validateMove(
        furniture: PlacedFurniture,
        newPosition: Vec2,
        newRotation?: Rotation
    ): PlacementResult {
        const rotation = newRotation !== undefined ? newRotation : furniture.rotation;
        const template = this.getFurnitureTemplate(furniture.templateId);
        
        if (!template) {
            return {
                success: false,
                errorMessage: "找不到家具模板"
            };
        }

        const rotatedSize = getRotatedSize(template.baseSize, rotation);
        
        // 检查边界
        const boundaryCheck = this.checkBoundary(newPosition, rotatedSize);
        if (!boundaryCheck.success) {
            return boundaryCheck;
        }

        // 检查格子占用（排除当前家具）
        const occupancyCheck = this.checkOccupancy(newPosition, rotatedSize, furniture.id);
        if (!occupancyCheck.success) {
            return occupancyCheck;
        }

        return { success: true };
    }

    /**
     * 验证家具旋转是否合法
     */
    validateRotation(
        furniture: PlacedFurniture,
        newRotation: Rotation
    ): PlacementResult {
        return this.validateMove(furniture, furniture.position, newRotation);
    }

    /**
     * 检查边界约束
     */
    private checkBoundary(position: Vec2, size: Vec2): PlacementResult {
        const roomSize = this.roomGrid.getRoomSize();
        
        // 检查是否超出房间边界
        if (position.x < 0 || position.y < 0) {
            return {
                success: false,
                errorMessage: "家具位置不能为负数"
            };
        }

        if (position.x + size.x > roomSize.x || position.y + size.y > roomSize.y) {
            return {
                success: false,
                errorMessage: "家具超出房间边界",
                conflictPositions: this.getOutOfBoundsPositions(position, size, roomSize)
            };
        }

        return { success: true };
    }

    /**
     * 检查格子占用情况
     */
    private checkOccupancy(
        position: Vec2, 
        size: Vec2, 
        excludeFurnitureId?: string
    ): PlacementResult {
        const conflictPositions: Vec2[] = [];

        for (let x = 0; x < size.x; x++) {
            for (let y = 0; y < size.y; y++) {
                const checkPos = new Vec2(position.x + x, position.y + y);
                
                if (!this.roomGrid.canPlaceFurniture(checkPos, new Vec2(1, 1), excludeFurnitureId)) {
                    conflictPositions.push(checkPos);
                }
            }
        }

        if (conflictPositions.length > 0) {
            return {
                success: false,
                errorMessage: "位置被障碍物或其他家具占用",
                conflictPositions
            };
        }

        return { success: true };
    }

    /**
     * 获取超出边界的位置
     */
    private getOutOfBoundsPositions(position: Vec2, size: Vec2, roomSize: Vec2): Vec2[] {
        const outOfBounds: Vec2[] = [];
        
        for (let x = 0; x < size.x; x++) {
            for (let y = 0; y < size.y; y++) {
                const checkPos = new Vec2(position.x + x, position.y + y);
                if (!isPositionInRoom(checkPos, roomSize)) {
                    outOfBounds.push(checkPos);
                }
            }
        }
        
        return outOfBounds;
    }

    /**
     * 获取家具模板（这里应该从家具管理器获取）
     */
    private getFurnitureTemplate(templateId: number): FurnitureTemplate | null {
        // TODO: 从FurnitureManager获取模板
        // 这里先返回null，实际实现时需要注入FurnitureManager
        console.warn("getFurnitureTemplate not implemented, templateId:", templateId);
        return null;
    }

    /**
     * 获取推荐的摆放位置
     * 在指定位置附近寻找可用的摆放位置
     */
    findNearestValidPosition(
        template: FurnitureTemplate,
        preferredPosition: Vec2,
        rotation: Rotation = Rotation.Deg0,
        maxSearchRadius: number = 3
    ): Vec2 | null {
        const rotatedSize = getRotatedSize(template.baseSize, rotation);
        
        // 首先检查首选位置
        if (this.validatePlacement(template, preferredPosition, rotation).success) {
            return preferredPosition;
        }

        // 在首选位置周围搜索
        for (let radius = 1; radius <= maxSearchRadius; radius++) {
            for (let dx = -radius; dx <= radius; dx++) {
                for (let dy = -radius; dy <= radius; dy++) {
                    // 只检查当前半径圈上的位置
                    if (Math.abs(dx) !== radius && Math.abs(dy) !== radius) {
                        continue;
                    }

                    const testPosition = new Vec2(
                        preferredPosition.x + dx,
                        preferredPosition.y + dy
                    );

                    if (this.validatePlacement(template, testPosition, rotation).success) {
                        return testPosition;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取所有可用的摆放位置
     */
    getAllValidPositions(
        template: FurnitureTemplate,
        rotation: Rotation = Rotation.Deg0
    ): Vec2[] {
        const validPositions: Vec2[] = [];
        const roomSize = this.roomGrid.getRoomSize();
        const rotatedSize = getRotatedSize(template.baseSize, rotation);

        for (let x = 0; x <= roomSize.x - rotatedSize.x; x++) {
            for (let y = 0; y <= roomSize.y - rotatedSize.y; y++) {
                const position = new Vec2(x, y);
                if (this.validatePlacement(template, position, rotation).success) {
                    validPositions.push(position);
                }
            }
        }

        return validPositions;
    }
}
