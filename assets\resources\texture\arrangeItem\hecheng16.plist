<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-镐.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{275,300}</string>
                <key>spriteSourceSize</key>
                <string>{275,300}</string>
                <key>textureRect</key>
                <string>{{1,305},{275,300}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-镐2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{275,300}</string>
                <key>spriteSourceSize</key>
                <string>{275,300}</string>
                <key>textureRect</key>
                <string>{{1,607},{275,300}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-镐3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{275,302}</string>
                <key>spriteSourceSize</key>
                <string>{275,302}</string>
                <key>textureRect</key>
                <string>{{1,1},{275,302}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-镐4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{275,300}</string>
                <key>spriteSourceSize</key>
                <string>{275,300}</string>
                <key>textureRect</key>
                <string>{{1,909},{275,300}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-镐5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{275,300}</string>
                <key>spriteSourceSize</key>
                <string>{275,300}</string>
                <key>textureRect</key>
                <string>{{1,1211},{275,300}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng16.png</string>
            <key>size</key>
            <string>{277,1512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9576511461e32bf037c7ea313fc8f8a4:231fce7cb7af198c09e4b4147b7439eb:38e4674c188a30c050477a19cf8e5be4$</string>
            <key>textureFileName</key>
            <string>hecheng16.png</string>
        </dict>
    </dict>
</plist>
