/**
 * 默认房间模板配置
 * 提供预设的房间布局模板
 */

import { Vec2 } from "cc";
import { RoomTemplate } from "./DecorationDefine";

/**
 * 默认房间模板数据
 */
export class DefaultRoomTemplates {
    /**
     * 获取所有默认房间模板
     */
    static getAllTemplates(): RoomTemplate[] {
        return [
            DefaultRoomTemplates.fixRoomTemplates(DefaultRoomTemplates.getRoomATemplate()),
            DefaultRoomTemplates.fixRoomTemplates(DefaultRoomTemplates.getRoomBTemplate()),
            DefaultRoomTemplates.fixRoomTemplates(DefaultRoomTemplates.getSmallRoomTemplate()),
            DefaultRoomTemplates.fixRoomTemplates(DefaultRoomTemplates.getLargeRoomTemplate())
        ];
    }

    /**
     * 修正空的房间模板数据
     * @param rt 原始房间模板数据
     * @returns 
     */
    static fixRoomTemplates(rt: RoomTemplate): RoomTemplate {
        const layout = rt.layout;
        for (let y = 0; y < layout.length; y++) {
            for (let x = 0; x < layout[y].length; x++) {
                if (layout[y][x] === 2) {// 被定义为障碍的格子不处理
                    continue;
                }

                if (y === 0 || y === layout.length - 1) {// 上下边界不是2就是1
                    layout[y][x] = 1;
                }
                else if (x === 0 || x === layout[y].length - 1) {// 左右边界不是2就是1
                    layout[y][x] = 1;
                }
                else if (layout[y][x] === 0) {// 上下左右任何一侧贴着障碍的格子都是1
                    if (layout[y][x + 1] == 2 || layout[y][x - 1] == 2 || layout[y + 1]?.[x] == 2 || layout[y - 1]?.[x] == 2) {
                        layout[y][x] = 1;
                    }
                }

            }
        }
        return rt;
    }

    /**
     * 房间A模板 (10×10)
     * 较大的房间空间，适合放置更多家具
     */
    static getRoomATemplate(): RoomTemplate {
        return {
            id: "room_a",
            name: "房间A",
            size: new Vec2(10, 10),
            description: "大型房间，适合放置多种家具",
            layout: [
                [2, 2, 2, 1, 1, 1, 1, 2, 2, 2],
                [2, 2, 1, 0, 0, 0, 0, 1, 2, 2],
                [2, 1, 0, 0, 0, 0, 0, 0, 1, 2],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [2, 1, 0, 0, 0, 0, 0, 0, 1, 2],
                [2, 2, 1, 0, 0, 0, 0, 1, 2, 2],
                [2, 2, 2, 1, 1, 1, 1, 2, 2, 2]
            ]
        };
    }

    /**
     * 房间B模板 (6×6)
     * 较小的房间空间，更紧凑的布局
     */
    static getRoomBTemplate(): RoomTemplate {
        return {
            id: "room_b",
            name: "房间B",
            size: new Vec2(6, 6),
            description: "小型房间，适合精致布局",
            layout: [
                [2, 2, 1, 1, 2, 2],
                [2, 1, 0, 0, 1, 2],
                [1, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 1],
                [2, 1, 0, 0, 1, 2],
                [2, 2, 1, 1, 2, 2]
            ]
        };
    }

    /**
     * 小房间模板 (4×4)
     * 迷你房间，适合单一功能区域
     */
    static getSmallRoomTemplate(): RoomTemplate {
        return {
            id: "small_room",
            name: "小房间",
            size: new Vec2(4, 4),
            description: "迷你房间，适合单一功能",
            layout: [
                [1, 1, 1, 1],
                [1, 0, 0, 1],
                [1, 0, 0, 1],
                [1, 1, 1, 1]
            ]
        };
    }

    /**
     * 大房间模板 (12×8)
     * 超大房间，适合复杂布局
     */
    static getLargeRoomTemplate(): RoomTemplate {
        return {
            id: "large_room",
            name: "大房间",
            size: new Vec2(12, 8),
            description: "超大房间，适合复杂布局",
            layout: [
                [2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2],
                [2, 2, 1, 1, 0, 0, 0, 0, 1, 1, 2, 2],
                [2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
                [2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2],
                [2, 2, 1, 1, 0, 0, 0, 0, 1, 1, 2, 2],
                [2, 2, 2, 2, 1, 1, 1, 1, 2, 2, 2, 2]
            ]
        };
    }

    /**
     * 根据ID获取房间模板
     */
    static getTemplateById(id: string): RoomTemplate | null {
        const templates = DefaultRoomTemplates.getAllTemplates();
        return templates.find(template => template.id === id) || null;
    }

    /**
     * 获取推荐的双房间组合
     */
    static getRecommendedDualRoomSetup(): { roomA: RoomTemplate, roomB: RoomTemplate } {
        return {
            roomA: DefaultRoomTemplates.getRoomATemplate(),
            roomB: DefaultRoomTemplates.getRoomBTemplate()
        };
    }

    /**
     * 验证房间布局数据
     */
    static validateRoomLayout(layout: number[][], size: Vec2): boolean {
        if (layout.length !== size.y) {
            return false;
        }

        for (let row of layout) {
            if (row.length !== size.x) {
                return false;
            }

            // 检查值是否在有效范围内 (0, 1, 2)
            for (let cell of row) {
                if (cell < 0 || cell > 2) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 计算房间可用空间
     */
    static calculateAvailableSpace(layout: number[][]): number {
        let availableCount = 0;
        for (let row of layout) {
            for (let cell of row) {
                if (cell === 0 || cell === 1) { // 空地或邻墙都可以摆放
                    availableCount++;
                }
            }
        }
        return availableCount;
    }

    /**
     * 计算邻墙格子数量（挂饰专用）
     */
    static calculateWallAdjacentSpace(layout: number[][]): number {
        let wallAdjacentCount = 0;
        for (let row of layout) {
            for (let cell of row) {
                if (cell === 1) { // 邻墙格子
                    wallAdjacentCount++;
                }
            }
        }
        return wallAdjacentCount;
    }
}
