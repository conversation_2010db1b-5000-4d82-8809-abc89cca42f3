/**
 * 房间布局管理器
 * 整合保存/加载功能与装饰系统
 */

import { Component, Vec2, _decorator } from "cc";
import { PlacedFurniture, FurnitureTemplate, FurnitureTheme, Rotation } from "../logic/DecorationDefine";
import { RoomLayoutStorage, SavedRoomLayout, LayoutCategory, LayoutFilter, SortOption } from "./RoomLayoutStorage";
import { DecorationMgr } from "../logic/DecorationMgr";
import { EventTag } from "../../EventId";
import { FurnitureManager } from "../logic/FurnitureManager";
import { EventTargetMgr } from "../../EventTargetMgr";

const { ccclass, property } = _decorator;

/**
 * 布局操作结果
 */
export interface LayoutOperationResult {
    success: boolean;
    message: string;
    layoutId?: string;
    data?: SavedRoomLayout;
}

/**
 * 快速保存选项
 */
export interface QuickSaveOptions {
    autoGenerateName?: boolean;
    includeScore?: boolean;
    includeThumbnail?: boolean;
    tags?: string[];
}

@ccclass('RoomLayoutManager')
export class RoomLayoutManager extends Component {
    @property({ tooltip: "是否启用自动保存" })
    enableAutoSave: boolean = true;

    @property({ tooltip: "自动保存延迟(秒)" })
    autoSaveDelay: number = 2; // 操作完成后2秒保存

    @property({ tooltip: "最大用户保存数量" })
    maxUserSaves: number = 50;

    private storage!: RoomLayoutStorage;
    private decorationMgr!: DecorationMgr;
    private furnitureManager!: FurnitureManager;
    private lastAutoSaveHash: string = "";
    private autoSaveTimer: any = null;
    private eventListeners: Map<string, any> = new Map();

    // 事件回调
    private onLayoutSaved?: (result: LayoutOperationResult) => void;
    private onLayoutLoaded?: (result: LayoutOperationResult) => void;
    private onLayoutDeleted?: (layoutId: string, success: boolean) => void;

    protected onLoad() {
        this.storage = RoomLayoutStorage.getInstance();
        this.decorationMgr = DecorationMgr.getInstance();
        this.furnitureManager = FurnitureManager.getInstance();
    }

    protected start() {
        if (this.enableAutoSave) {
            this.registerAutoSaveEvents();
        }
    }

    protected onDisable() {
        this.unregisterAutoSaveEvents();
        this.clearAutoSaveTimer();
    }

    protected onDestroy() {
        this.unregisterAutoSaveEvents();
        this.clearAutoSaveTimer();
    }

    /**
     * 快速保存当前布局
     */
    async quickSave(name?: string, options?: QuickSaveOptions): Promise<LayoutOperationResult> {
        try {
            const furnitures = this.decorationMgr.getPlacedFurnitures();
            const roomGrid = this.decorationMgr.getRoomGrid();
            
            if (!roomGrid) {
                return {
                    success: false,
                    message: "房间未初始化"
                };
            }

            const roomSize = roomGrid.getRoomSize();
            const layoutName = name || (options?.autoGenerateName ? 
                this.generateAutoName() : 
                `布局_${new Date().toLocaleString()}`);

            // 获取评分信息
            let score: number | undefined;
            let dominantTheme: FurnitureTheme | undefined;
            
            if (options?.includeScore) {
                const scoreDetails = this.decorationMgr.getRoomScoreDetails();
                if (scoreDetails) {
                    score = scoreDetails.totalScore;
                    dominantTheme = scoreDetails.dominantTheme || undefined;
                }
            }

            // 生成缩略图
            let thumbnail: string | undefined;
            if (options?.includeThumbnail) {
                thumbnail = await this.generateThumbnail();
            }

            const layoutId = await this.storage.saveLayout(
                furnitures,
                roomSize,
                layoutName,
                LayoutCategory.UserSaved,
                {
                    score,
                    dominantTheme,
                    thumbnail,
                    tags: options?.tags,
                    description: `保存于 ${new Date().toLocaleString()}`
                }
            );

            const result: LayoutOperationResult = {
                success: true,
                message: `布局 "${layoutName}" 保存成功`,
                layoutId
            };

            this.onLayoutSaved?.(result);
            return result;

        } catch (error) {
            const result: LayoutOperationResult = {
                success: false,
                message: `保存失败: ${error instanceof Error ? error.message : String(error)}`
            };
            
            this.onLayoutSaved?.(result);
            return result;
        }
    }

    /**
     * 加载指定布局
     */
    async loadLayout(layoutId: string): Promise<LayoutOperationResult> {
        try {
            const savedLayout = await this.storage.loadLayout(layoutId);
            
            if (!savedLayout) {
                return {
                    success: false,
                    message: "找不到指定的布局"
                };
            }

            // 清空当前布局
            this.decorationMgr.clearAllFurniture();

            // 恢复房间尺寸（如果需要）
            // 这里可能需要根据实际情况调整房间大小

            // 逐个放置家具
            let successCount = 0;
            const totalCount = savedLayout.furnitures.length;

            for (const savedFurniture of savedLayout.furnitures) {
                const result = this.decorationMgr.placeFurniture(
                    savedFurniture.templateId,
                    new Vec2(savedFurniture.position.x, savedFurniture.position.y),
                    savedFurniture.rotation
                );

                if (result.success) {
                    successCount++;
                } else {
                    console.warn(`放置家具失败: ${result.errorMessage}`, savedFurniture);
                }
            }

            const result: LayoutOperationResult = {
                success: successCount > 0,
                message: successCount === totalCount ? 
                    `布局 "${savedLayout.name}" 加载成功` :
                    `布局 "${savedLayout.name}" 部分加载成功 (${successCount}/${totalCount})`,
                layoutId,
                data: savedLayout
            };

            this.onLayoutLoaded?.(result);
            return result;

        } catch (error) {
            const result: LayoutOperationResult = {
                success: false,
                message: `加载失败: ${error instanceof Error ? error.message : String(error)}`
            };
            
            this.onLayoutLoaded?.(result);
            return result;
        }
    }

    /**
     * 删除布局
     */
    async deleteLayout(layoutId: string): Promise<boolean> {
        try {
            const success = await this.storage.deleteLayout(layoutId);
            this.onLayoutDeleted?.(layoutId, success);
            return success;
        } catch (error) {
            console.error("删除布局失败:", error);
            this.onLayoutDeleted?.(layoutId, false);
            return false;
        }
    }

    /**
     * 搜索布局
     */
    async searchLayouts(
        filter?: LayoutFilter,
        sort: SortOption = SortOption.ModifiedTimeDesc,
        limit?: number
    ): Promise<SavedRoomLayout[]> {
        return await this.storage.searchLayouts(filter, sort, limit);
    }

    /**
     * 获取最近的布局
     */
    async getRecentLayouts(limit: number = 10): Promise<SavedRoomLayout[]> {
        return await this.searchLayouts(
            undefined,
            SortOption.ModifiedTimeDesc,
            limit
        );
    }

    /**
     * 获取高分布局
     */
    async getTopScoredLayouts(limit: number = 10): Promise<SavedRoomLayout[]> {
        return await this.searchLayouts(
            { minScore: 70 },
            SortOption.ScoreDesc,
            limit
        );
    }

    /**
     * 按主题获取布局
     */
    async getLayoutsByTheme(theme: FurnitureTheme, limit?: number): Promise<SavedRoomLayout[]> {
        return await this.searchLayouts(
            { theme },
            SortOption.ScoreDesc,
            limit
        );
    }

    /**
     * 导出布局
     */
    async exportLayout(layoutId: string): Promise<string | null> {
        return await this.storage.exportLayout(layoutId);
    }

    /**
     * 导入布局
     */
    async importLayout(jsonData: string, newName?: string): Promise<LayoutOperationResult> {
        try {
            const layoutId = await this.storage.importLayout(jsonData, newName);
            
            if (!layoutId) {
                return {
                    success: false,
                    message: "导入失败，数据格式无效"
                };
            }

            return {
                success: true,
                message: "布局导入成功",
                layoutId
            };
        } catch (error) {
            return {
                success: false,
                message: `导入失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    /**
     * 复制布局
     */
    async duplicateLayout(layoutId: string, newName?: string): Promise<LayoutOperationResult> {
        try {
            const originalLayout = await this.storage.loadLayout(layoutId);
            if (!originalLayout) {
                return {
                    success: false,
                    message: "找不到要复制的布局"
                };
            }

            const duplicateName = newName || `${originalLayout.name} (副本)`;
            const furnitures = originalLayout.furnitures.map(f => ({
                id: `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                templateId: f.templateId,
                position: new Vec2(f.position.x, f.position.y),
                rotation: f.rotation,
                currentSize: new Vec2(1, 1), // 这里需要根据模板计算实际尺寸
                placedTime: Date.now(),
                properties: {} // 这里需要从模板获取属性
            })) as PlacedFurniture[];

            const newLayoutId = await this.storage.saveLayout(
                furnitures,
                new Vec2(originalLayout.roomSize.x, originalLayout.roomSize.y),
                duplicateName,
                LayoutCategory.UserSaved,
                {
                    description: `复制自: ${originalLayout.name}`,
                    tags: originalLayout.tags
                }
            );

            return {
                success: true,
                message: `布局 "${duplicateName}" 复制成功`,
                layoutId: newLayoutId
            };
        } catch (error) {
            return {
                success: false,
                message: `复制失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    /**
     * 获取存储统计信息
     */
    async getStorageStats() {
        return await this.storage.getLayoutStats();
    }

    /**
     * 清理存储空间
     */
    async cleanupStorage(options?: {
        removeAutoSaves?: boolean;
        removeOldLayouts?: number;
        maxLayouts?: number;
    }): Promise<number> {
        return await this.storage.cleanup(options);
    }

    /**
     * 注册自动保存事件监听器
     */
    private registerAutoSaveEvents(): void {
        const eventId = 1; // 使用固定的事件ID

        // 监听家具放置事件
        const placedListener = EventTargetMgr.instance.addListener(EventTag.FurniturePlaced, eventId,
            this.onFurnitureChanged, this);
        this.eventListeners.set(EventTag.FurniturePlaced, placedListener);

        // 监听家具移除事件
        const removedListener = EventTargetMgr.instance.addListener(EventTag.FurnitureRemoved, eventId,
            this.onFurnitureChanged, this);
        this.eventListeners.set(EventTag.FurnitureRemoved, removedListener);

        // 监听家具移动事件
        const movedListener = EventTargetMgr.instance.addListener(EventTag.FurnitureMoved, eventId,
            this.onFurnitureChanged, this);
        this.eventListeners.set(EventTag.FurnitureMoved, movedListener);

        // 监听家具旋转事件
        const rotatedListener = EventTargetMgr.instance.addListener(EventTag.FurnitureRotated, eventId,
            this.onFurnitureChanged, this);
        this.eventListeners.set(EventTag.FurnitureRotated, rotatedListener);
    }

    /**
     * 取消注册自动保存事件监听器
     */
    private unregisterAutoSaveEvents(): void {
        const eventId = 1; // 使用相同的事件ID

        // 移除所有监听器
        this.eventListeners.forEach((listener, tag) => {
            EventTargetMgr.instance.removeListener(tag, eventId, listener);
        });

        this.eventListeners.clear();
    }

    /**
     * 家具变化事件处理
     */
    private onFurnitureChanged(): void {
        console.log("检测到家具变化，准备自动保存...");
        this.scheduleAutoSave();
    }

    /**
     * 调度自动保存
     */
    private scheduleAutoSave(): void {
        // 清除之前的定时器
        this.clearAutoSaveTimer();

        console.log(`将在 ${this.autoSaveDelay} 秒后执行自动保存`);

        // 设置新的延迟保存定时器
        this.autoSaveTimer = this.scheduleOnce(() => {
            this.performAutoSave().catch(error => {
                console.warn("自动保存失败:", error);
            });
        }, this.autoSaveDelay);
    }

    /**
     * 清除自动保存定时器
     */
    private clearAutoSaveTimer(): void {
        if (this.autoSaveTimer) {
            this.unschedule(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    /**
     * 执行自动保存
     */
    private async performAutoSave(): Promise<void> {
        try {
            const furnitures = this.decorationMgr.getPlacedFurnitures();
            const roomGrid = this.decorationMgr.getRoomGrid();
            
            if (!roomGrid || furnitures.length === 0) {
                return; // 没有内容需要保存
            }

            // 检查是否有变化
            const currentHash = this.calculateLayoutHash(furnitures);
            if (currentHash === this.lastAutoSaveHash) {
                return; // 没有变化，跳过保存
            }

            const roomSize = roomGrid.getRoomSize();
            const scoreDetails = this.decorationMgr.getRoomScoreDetails();

            await this.storage.autoSave(
                furnitures,
                roomSize,
                scoreDetails?.totalScore,
                scoreDetails?.dominantTheme || undefined
            );

            this.lastAutoSaveHash = currentHash;
            console.log("自动保存完成");

        } catch (error) {
            console.warn("自动保存失败:", error);
        }
    }

    /**
     * 计算布局哈希值
     */
    private calculateLayoutHash(furnitures: PlacedFurniture[]): string {
        const data = furnitures.map(f => ({
            templateId: f.templateId,
            x: f.position.x,
            y: f.position.y,
            rotation: f.rotation
        }));
        return JSON.stringify(data);
    }

    /**
     * 生成自动名称
     */
    private generateAutoName(): string {
        const now = new Date();
        const themes = ["现代", "古典", "工业", "自然", "简约"];
        const randomTheme = themes[Math.floor(Math.random() * themes.length)];
        return `${randomTheme}风格_${now.getMonth() + 1}${now.getDate()}_${now.getHours()}${now.getMinutes()}`;
    }

    /**
     * 生成缩略图
     */
    private async generateThumbnail(): Promise<string> {
        // 这里可以实现生成房间布局缩略图的逻辑
        // 返回base64编码的图片数据
        return ""; // 暂时返回空字符串
    }

    // 事件回调设置方法
    setOnLayoutSaved(callback: (result: LayoutOperationResult) => void): void {
        this.onLayoutSaved = callback;
    }

    setOnLayoutLoaded(callback: (result: LayoutOperationResult) => void): void {
        this.onLayoutLoaded = callback;
    }

    setOnLayoutDeleted(callback: (layoutId: string, success: boolean) => void): void {
        this.onLayoutDeleted = callback;
    }
}
