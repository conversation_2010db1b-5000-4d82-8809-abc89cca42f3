[{"__type__": "cc.Prefab", "_name": "完成第3关", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "完成第3关", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 16}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "01d4aUjQpZMip0tR3tmI9iX", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "guides": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}, {"__id__": 13}, {"__id__": 14}], "waitPre": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43PAxXqfhL54dXlfThQV+l"}, {"__type__": "SceneGuide", "name": "等待页面", "switchType": 2, "type": 9, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 1, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "道具可以提高雪人的基础能力，前往道具页面升级道具吧。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 7}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击道具", "waitPageId": 1, "targetNodePath": "mainUI/frame_maininterface_1/道具", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "点击道具卡的信息选项打开道具升级页面。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 500}, "roleName": "胡萝卜卜", "guides": [{"__id__": 9}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "", "waitPageId": 0, "targetNodePath": "", "force": false, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 11}, {"__id__": 12}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击镐", "waitPageId": 1, "targetNodePath": "mainUI/pages/道具/top/使用装备底/镐/Node/bg", "force": true, "nodeGuide": false}, {"__type__": "GuideConfig", "name": "点击信息", "waitPageId": 1, "targetNodePath": "mainUI/pages/道具/top/使用装备底/镐/Node/longBg/Button", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤3对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "点击升级道具", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1000}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤3引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 15}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击升级", "waitPageId": 18, "targetNodePath": "upgradeUI/upgradeBtn", "force": true, "nodeGuide": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]