/**
 * 相邻加成功能测试脚本
 * 验证相邻加成机制的正确性
 */

import { Component, Vec2, _decorator } from "cc";
import { RoomScoreCalculator } from "../RoomScoreCalculator";
import { PlacedFurniture, FurnitureTemplate, FurnitureTheme, FurnitureType, Rotation } from "../DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('AdjacentBonusTest')
export class AdjacentBonusTest extends Component {
    
    start() {
        console.log("=== 相邻加成功能测试开始 ===");
        
        try {
            this.testBasicAdjacency();
            this.testThemeMatching();
            this.testSpecialCombinations();
            this.testComplexLayout();
            this.testScoreComparison();
            
            console.log("=== 相邻加成功能测试完成 ===");
        } catch (error) {
            console.error("相邻加成功能测试失败:", error);
        }
    }

    /**
     * 测试基础相邻检测
     */
    private testBasicAdjacency() {
        console.log("--- 测试基础相邻检测 ---");
        
        const calculator = new RoomScoreCalculator();
        const templates = this.createTestTemplates();
        
        // 创建两个相邻的椅子
        const furnitures: PlacedFurniture[] = [
            {
                id: "chair1",
                templateId: 1,
                position: new Vec2(2, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "chair2", 
                templateId: 1,
                position: new Vec2(3, 2), // 右相邻
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            }
        ];
        
        const roomSize = new Vec2(8, 8);
        const score = calculator.calculateRoomScore(furnitures, templates, roomSize);
        
        console.log(`两个相邻椅子的评分:`);
        console.log(`- 主题得分: ${score.themeScore}`);
        console.log(`- 相邻得分: ${score.adjacentScore || 0}`);
        console.log(`- 总分: ${score.totalScore}`);
        
        // 测试不相邻的情况
        furnitures[1].position = new Vec2(5, 5); // 移到远处
        const scoreNonAdjacent = calculator.calculateRoomScore(furnitures, templates, roomSize);
        
        console.log(`两个不相邻椅子的评分:`);
        console.log(`- 主题得分: ${scoreNonAdjacent.themeScore}`);
        console.log(`- 相邻得分: ${scoreNonAdjacent.adjacentScore || 0}`);
        console.log(`- 总分: ${scoreNonAdjacent.totalScore}`);
        
        const adjacentBonus = (score.adjacentScore || 0) - (scoreNonAdjacent.adjacentScore || 0);
        console.log(`相邻加成差异: +${adjacentBonus}分`);
    }

    /**
     * 测试主题匹配相邻加成
     */
    private testThemeMatching() {
        console.log("--- 测试主题匹配相邻加成 ---");
        
        const calculator = new RoomScoreCalculator();
        const templates = this.createTestTemplates();
        
        // 同主题家具相邻
        const sameThemeFurnitures: PlacedFurniture[] = [
            {
                id: "modern1",
                templateId: 1, // 现代椅子
                position: new Vec2(2, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "modern2",
                templateId: 4, // 现代沙发
                position: new Vec2(3, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(2, 1),
                placedTime: Date.now()
            }
        ];
        
        // 不同主题家具相邻
        const differentThemeFurnitures: PlacedFurniture[] = [
            {
                id: "modern1",
                templateId: 1, // 现代椅子
                position: new Vec2(2, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "classic1",
                templateId: 2, // 古典床
                position: new Vec2(3, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(2, 1),
                placedTime: Date.now()
            }
        ];
        
        const roomSize = new Vec2(8, 8);
        const sameThemeScore = calculator.calculateRoomScore(sameThemeFurnitures, templates, roomSize);
        const differentThemeScore = calculator.calculateRoomScore(differentThemeFurnitures, templates, roomSize);
        
        console.log(`同主题相邻 - 相邻得分: ${sameThemeScore.adjacentScore || 0}`);
        console.log(`不同主题相邻 - 相邻得分: ${differentThemeScore.adjacentScore || 0}`);
    }

    /**
     * 测试特殊组合奖励
     */
    private testSpecialCombinations() {
        console.log("--- 测试特殊组合奖励 ---");
        
        const calculator = new RoomScoreCalculator();
        const templates = this.createTestTemplates();
        
        // 桌椅组合
        const tableChairCombo: PlacedFurniture[] = [
            {
                id: "table1",
                templateId: 3, // 工业桌子 (Medium)
                position: new Vec2(2, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(2, 2),
                placedTime: Date.now()
            },
            {
                id: "chair1",
                templateId: 1, // 现代椅子 (Small)
                position: new Vec2(4, 2), // 紧邻桌子
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            }
        ];
        
        // 挂饰与家具组合
        const wallDecoCombo: PlacedFurniture[] = [
            {
                id: "painting1",
                templateId: 6, // 古典壁画 (WallDecoration)
                position: new Vec2(1, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "chair1",
                templateId: 1, // 现代椅子 (Small)
                position: new Vec2(2, 2), // 紧邻壁画
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            }
        ];
        
        const roomSize = new Vec2(8, 8);
        const tableChairScore = calculator.calculateRoomScore(tableChairCombo, templates, roomSize);
        const wallDecoScore = calculator.calculateRoomScore(wallDecoCombo, templates, roomSize);
        
        console.log(`桌椅组合 - 相邻得分: ${tableChairScore.adjacentScore || 0}`);
        console.log(`挂饰装点 - 相邻得分: ${wallDecoScore.adjacentScore || 0}`);
    }

    /**
     * 测试复杂布局
     */
    private testComplexLayout() {
        console.log("--- 测试复杂布局 ---");
        
        const calculator = new RoomScoreCalculator();
        const templates = this.createTestTemplates();
        
        // 创建一个复杂的家具布局
        const complexLayout: PlacedFurniture[] = [
            // 中央桌子
            {
                id: "table1",
                templateId: 3,
                position: new Vec2(3, 3),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(2, 2),
                placedTime: Date.now()
            },
            // 四周椅子
            {
                id: "chair1",
                templateId: 1,
                position: new Vec2(2, 3),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "chair2",
                templateId: 1,
                position: new Vec2(5, 3),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "chair3",
                templateId: 1,
                position: new Vec2(3, 2),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            },
            {
                id: "chair4",
                templateId: 1,
                position: new Vec2(4, 5),
                rotation: Rotation.Deg0,
                currentSize: new Vec2(1, 1),
                placedTime: Date.now()
            }
        ];
        
        const roomSize = new Vec2(8, 8);
        const complexScore = calculator.calculateRoomScore(complexLayout, templates, roomSize);
        
        console.log(`复杂布局评分:`);
        console.log(`- 主题得分: ${complexScore.themeScore}`);
        console.log(`- 数量得分: ${complexScore.quantityScore}`);
        console.log(`- 价值得分: ${complexScore.valueScore}`);
        console.log(`- 布局得分: ${complexScore.layoutScore}`);
        console.log(`- 相邻得分: ${complexScore.adjacentScore || 0}`);
        console.log(`- 总分: ${complexScore.totalScore}`);
    }

    /**
     * 测试评分对比
     */
    private testScoreComparison() {
        console.log("--- 测试评分对比 ---");
        
        const calculator = new RoomScoreCalculator();
        const templates = this.createTestTemplates();
        const roomSize = new Vec2(8, 8);
        
        // 场景1：分散摆放
        const scatteredLayout: PlacedFurniture[] = [
            { id: "f1", templateId: 1, position: new Vec2(1, 1), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f2", templateId: 1, position: new Vec2(6, 1), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f3", templateId: 1, position: new Vec2(1, 6), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f4", templateId: 1, position: new Vec2(6, 6), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() }
        ];
        
        // 场景2：集中摆放
        const clusteredLayout: PlacedFurniture[] = [
            { id: "f1", templateId: 1, position: new Vec2(3, 3), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f2", templateId: 1, position: new Vec2(4, 3), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f3", templateId: 1, position: new Vec2(3, 4), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() },
            { id: "f4", templateId: 1, position: new Vec2(4, 4), rotation: Rotation.Deg0, currentSize: new Vec2(1, 1), placedTime: Date.now() }
        ];
        
        const scatteredScore = calculator.calculateRoomScore(scatteredLayout, templates, roomSize);
        const clusteredScore = calculator.calculateRoomScore(clusteredLayout, templates, roomSize);
        
        console.log(`分散摆放 - 相邻得分: ${scatteredScore.adjacentScore || 0}, 总分: ${scatteredScore.totalScore}`);
        console.log(`集中摆放 - 相邻得分: ${clusteredScore.adjacentScore || 0}, 总分: ${clusteredScore.totalScore}`);
        
        const bonusDifference = (clusteredScore.adjacentScore || 0) - (scatteredScore.adjacentScore || 0);
        console.log(`相邻加成优势: +${bonusDifference}分`);
    }

    /**
     * 创建测试用家具模板
     */
    private createTestTemplates(): Map<number, FurnitureTemplate> {
        const templates = new Map<number, FurnitureTemplate>();
        
        // 现代椅子
        templates.set(1, {
            id: 1,
            name: "现代椅子",
            type: FurnitureType.Small,
            baseSize: new Vec2(1, 1),
            spriteFrame: "chair_modern",
            description: "简洁现代的椅子",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 1,
                value: 8,
                beauty: 6
            }
        });
        
        // 古典床
        templates.set(2, {
            id: 2,
            name: "古典床",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "bed_classic",
            description: "优雅的古典风格床铺",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 2,
                value: 15,
                beauty: 8
            }
        });
        
        // 工业桌子
        templates.set(3, {
            id: 3,
            name: "工业桌子",
            type: FurnitureType.Large,
            baseSize: new Vec2(2, 2),
            spriteFrame: "table_industrial",
            description: "粗犷的工业风格桌子",
            properties: {
                theme: FurnitureTheme.Industrial,
                level: 2,
                value: 12,
                beauty: 5
            }
        });
        
        // 现代沙发
        templates.set(4, {
            id: 4,
            name: "现代沙发",
            type: FurnitureType.Medium,
            baseSize: new Vec2(2, 1),
            spriteFrame: "sofa_modern",
            description: "舒适的现代风格沙发",
            properties: {
                theme: FurnitureTheme.Modern,
                level: 3,
                value: 20,
                beauty: 10
            }
        });
        
        // 古典壁画
        templates.set(6, {
            id: 6,
            name: "古典壁画",
            type: FurnitureType.WallDecoration,
            baseSize: new Vec2(1, 1),
            spriteFrame: "painting_classic",
            description: "典雅的古典壁画",
            properties: {
                theme: FurnitureTheme.Classic,
                level: 3,
                value: 25,
                beauty: 15,
                isWallDecoration: true
            }
        });
        
        return templates;
    }
}
