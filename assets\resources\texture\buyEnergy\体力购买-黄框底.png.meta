{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "11e56db0-1198-469f-b647-25121f011fba", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "11e56db0-1198-469f-b647-25121f011fba@6c48a", "displayName": "体力购买-黄框底", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "11e56db0-1198-469f-b647-25121f011fba", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "11e56db0-1198-469f-b647-25121f011fba@f9941", "displayName": "体力购买-黄框底", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 279, "height": 327, "rawWidth": 279, "rawHeight": 327, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-139.5, -163.5, 0, 139.5, -163.5, 0, -139.5, 163.5, 0, 139.5, 163.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 327, 279, 327, 0, 0, 279, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-139.5, -163.5, 0], "maxPos": [139.5, 163.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "11e56db0-1198-469f-b647-25121f011fba@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "11e56db0-1198-469f-b647-25121f011fba@6c48a"}}