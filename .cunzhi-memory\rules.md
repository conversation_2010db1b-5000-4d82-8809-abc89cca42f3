# 开发规范和规则

- 卡牌激活系统分析：1.CardMgr管理inactiveCards集合控制卡牌生效状态；2.activateCard方法从inactiveCards中移除卡牌ID实现激活；3.EventTag.CardActivated事件通知各系统卡牌激活；4.MinerManager、EntryMgr等系统监听此事件并更新相应逻辑；5.StageDataMgr.allowEquipmentTypes根据生效卡牌计算可用装备类型；6.默认卡牌2、3初始不生效
- FurnitureManager.ts修复：移除了不存在的EffectType导入，将默认家具模板从effects属性改为properties属性，使用FurnitureTheme、level、value、beauty等新的评分系统属性结构
- RoomScoreCalculator.ts类型修复：使用类型守卫(info): info is { furniture: PlacedFurniture; template: FurnitureTemplate }解决filter后TypeScript无法推断template非undefined的类型问题
- HTML演示调试优化：添加完整的try-catch错误处理、详细的控制台日志输出、评分系统测试功能，将所有const/let改为var提升兼容性，确保评分更新的稳定性和可调试性
- 演示步骤验证系统：重新生成无重叠的演示数据，包含validateStepFurniture重叠检测函数、validateAllSteps全步骤验证，确保每个演示步骤中的家具都不会发生位置冲突，提供完整的调试验证功能
- DecorationDemoManager.ts修复：1.RoomGrid构造函数需要initialLayout参数，添加createDefaultLayout方法创建全空布局；2.FurnitureTemplate接口使用baseSize而非size，需要type、spriteFrame、description字段；3.PlacedFurniture接口需要placedTime字段，不包含properties字段；4.导入FurnitureType枚举用于家具类型定义
- 房间布局格式重定义：1.GridState枚举更新为Empty=0(空地可摆放)、WallAdjacent=1(邻墙格子可摆放且挂饰类家具只能摆放在此)、Obstacle=2(障碍不可摆放)、Occupied=3(已占用)；2.RoomGrid增加originalLayout保存原始状态、canPlaceWallDecoration验证挂饰类家具、placeFurniture支持isWallDecoration参数；3.所有调用placeFurniture的地方都传递正确的isWallDecoration参数；4.FurniturePlacementValidator.isAdjacentToWall使用新格子状态逻辑
- TypeScript属性初始化修复：在Cocos Creator组件中，对于在onLoad()方法中初始化的属性，使用明确赋值断言(!)告诉编译器属性会在使用前被初始化，这是处理组件生命周期属性的最佳实践
- FurnitureRandomizer.ts类型修复：将template.size改为template.baseSize以匹配FurnitureTemplate接口定义，解决TypeScript类型检查错误
- FurnitureManager类方法修复：1.添加autoCorrectFurniturePosition包装方法调用advancedValidator.autoCorrectPosition实现自动修正功能；2.添加getWallDecorations方法过滤返回所有挂饰类家具；3.导入AutoCorrectionResult类型确保类型安全；4.保持API一致性和封装性的最佳实践
- TypeScript错误处理类型修复：在catch块中，error参数默认为unknown类型，需要使用类型守卫`error instanceof Error ? error.message : String(error)`来安全访问message属性，避免"error的类型为未知"编译错误
- Cocos Creator定时器最佳实践：使用组件的schedule/unschedule方法替代setInterval/clearInterval，避免跨平台类型兼容问题，自动与组件生命周期绑定，组件销毁时自动清理资源
- RoomLayoutManager自动保存机制重构：从定时器触发改为事件驱动，监听DecorationEventTag的FurniturePlaced、FurnitureRemoved、FurnitureMoved、FurnitureRotated事件，在家具操作成功后延迟2秒自动保存，避免频繁保存操作
- DecorationMgr事件完整性优化：1.moveFurniture和rotateFurniture方法添加评分重新计算和ScoreChanged事件；2.新增randomizeFurniture方法处理随机变化并触发相应事件；3.优化clearAllFurniture方法避免重复触发自动保存；4.确保所有家具操作都能正确触发自动保存机制
- TypeScript数字枚举类型修复：Object.values()对数字枚举返回(string|EnumType)[]，需要filter(value => typeof value === 'number')过滤字符串键名，并使用类型断言确保类型安全
- MultiRoomManager.ts修复：将loadConfiguration方法中的DataManager.instance.getData改为DataManager.instance.loadLocalData，将saveConfiguration方法中的DataManager.instance.saveData改为DataManager.instance.saveLocalData，确保数据加载和保存方法的一致性
- MultiRoomManager.ts类型错误修复：1.移除全局placementValidator属性，改为在placeFurniture方法中创建局部FurniturePlacementValidator实例；2.修正validatePlacement方法调用参数，移除错误的roomGrid参数；3.修复ValidationResult属性访问，使用reason而非errorMessage；4.修复substr()弃用警告改用substring()；5.确保每个房间使用独立的验证器实例以避免类型冲突
- Cocos Creator组件分离规则：每个TypeScript文件只能包含一个@ccclass装饰器的组件类定义。RoomLayoutUI.ts修复：将RoomLayoutItem类移动到独立的RoomLayoutItem.ts文件中，解决"Each script can have at most one Component"编译错误，确保组件类的正确分离和导入关系
- 移除演示代码：1.删除DecorationDemoManager.ts和DecorationDemoUI.ts文件；2.删除demo目录下的ASCII演示和HTML演示文件；3.删除根目录下的room_decoration_test.html；4.修改LayoutStorageTest.ts移除对DecorationDemoManager的依赖，改为使用独立的测试数据；5.更新README文件移除演示相关内容；6.确保项目可以正常编译运行
- 项目代码结构重组：1.将UI相关代码移至decoration/ui目录，包括RoomLayoutUI、RoomLayoutItem、RoomLayoutManager和RoomLayoutStorage；2.将核心业务逻辑和数据模型移至decoration/logic目录，包括DecorationDefine、RoomGrid、FurnitureManager等；3.删除DecorationTestScene.ts测试场景文件；4.更新所有导入路径以反映新的目录结构
- 房间摆放系统文档重构：1.重新整理README文档，详细列出核心API；2.按照组件职责分类整理，包括DecorationMgr、FurnitureManager、RoomGrid等；3.添加数据结构、枚举定义、使用示例和最佳实践；4.移除旧的演示相关内容；5.提供完整的配置选项和注意事项
- 创建了房间摆放系统测试HTML页面：1.包含双房间(10x10和6x6)可视化展示；2.鼠标悬停显示格子信息(位置、状态、描述)和家具信息(名称、尺寸、主题等详细属性)；3.支持添加随机家具、清空家具、重置房间功能；4.完整模拟了房间摆放系统的核心数据结构和验证逻辑
- 更新房间摆放系统测试页面：1.使用用户提供的具体房间布局数据(房间A 10x10，房间B 6x6)；2.格子属性：0=空地可摆放，1=邻墙可摆放且挂饰专用，2=障碍不可摆放；3.信息面板移至页面底部并使用固定高度120px；4.移除动态生成布局，使用预定义的二维数组布局
- 为房间摆放系统测试页面添加旋转和评分功能：1.右键点击家具可旋转90度；2.添加评分面板显示总评分、主题评分、数量评分、价值评分、布局评分和主导主题；3.评分系统包含主题一致性、数量优化、价值计算、房间利用率和分布平衡；4.所有家具操作后自动重新计算评分
- 为房间摆放系统测试页面添加完整用户交互功能：1.三种操作模式(摆放/移除/旋转)切换；2.家具类型选择器，可选择4种不同家具；3.点击格子进行摆放、移除或旋转操作；4.格子悬停高亮提示；5.家具选中状态显示；6.所有操作后自动更新评分系统
- 删除房间摆放系统中所有随机功能：1.删除FurnitureRandomizer.ts文件和meta文件；2.从FurnitureManager.ts中移除randomizeFurniture和applyRandomizeResult方法；3.从DecorationMgr.ts中移除randomizeFurniture方法；4.从测试页面删除addRandomFurniture函数和按钮；5.更新README.md文档移除FurnitureRandomizer相关内容和随机变化配置
- 修改家具旋转角度规则限制为0度和90度：1.更新DecorationDefine.ts中Rotation枚举只保留Deg0和Deg90；2.修改getRotatedSize函数只处理0度和90度情况；3.更新FurniturePlacementValidator、MultiRoomManager中的getRotatedSize方法；4.修改测试页面旋转逻辑为0度和90度之间切换；5.更新README.md文档中的旋转角度说明
- 修复测试页面中旋转和移除功能点击家具不生效的问题：1.添加handleFurnitureClick函数根据当前模式执行不同操作；2.修改家具点击事件从selectFurniture改为handleFurnitureClick；3.添加removeFurnitureById函数支持直接通过家具ID移除；4.更新页面说明文本指导用户正确操作方式
- 将furnitureTemplates.json转换为TypeScript文件：1.创建FurnitureTemplates.ts类提供静态方法访问家具模板数据；2.修改FurnitureManager.ts使用FurnitureTemplates.getAllTemplates()直接加载；3.删除JSON导入和异步加载逻辑；4.更新测试页面家具模板数据与TS文件保持同步；5.删除原JSON文件，数据现在可直接访问
- 更新房间布局为新的二维数组：1.房间A(10x10)使用新的不规则布局；2.房间B改为5x4尺寸的简单布局；3.增大房间间距到60px强调不相邻；4.添加房间边框和阴影突出独立性；5.更新所有相关函数中的布局数据；6.确保共用评分系统正确计算总面积
