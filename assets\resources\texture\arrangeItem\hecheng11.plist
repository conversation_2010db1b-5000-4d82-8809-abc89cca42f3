<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-手榴弹.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,221}</string>
                <key>spriteSourceSize</key>
                <string>{124,221}</string>
                <key>textureRect</key>
                <string>{{1,1},{124,221}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-手榴弹2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,221}</string>
                <key>spriteSourceSize</key>
                <string>{124,221}</string>
                <key>textureRect</key>
                <string>{{1,224},{124,221}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-手榴弹3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,221}</string>
                <key>spriteSourceSize</key>
                <string>{124,221}</string>
                <key>textureRect</key>
                <string>{{1,447},{124,221}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-手榴弹4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,221}</string>
                <key>spriteSourceSize</key>
                <string>{124,221}</string>
                <key>textureRect</key>
                <string>{{1,670},{124,221}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-手榴弹5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{124,221}</string>
                <key>spriteSourceSize</key>
                <string>{124,221}</string>
                <key>textureRect</key>
                <string>{{1,893},{124,221}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng11.png</string>
            <key>size</key>
            <string>{126,1115}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:98258c8bee2173df1dd255dd4bc034f1:e982d276818bef7b4ea529b200e9e231:8c79e6fa063eca8160fe3c58cb28a55d$</string>
            <key>textureFileName</key>
            <string>hecheng11.png</string>
        </dict>
    </dict>
</plist>
