/**
 * 家具摆放验证器
 * 实现位置验证、自动修正和贴墙检测功能
 */

import { Vec2 } from "cc";
import { FurnitureTemplate, PlacedFurniture, Rotation, FurnitureType } from "./DecorationDefine";
import { RoomGrid } from "./RoomGrid";

/**
 * 验证结果
 */
export interface ValidationResult {
    isValid: boolean;
    reason?: string;
    suggestedPosition?: Vec2;
}

/**
 * 自动修正结果
 */
export interface AutoCorrectionResult {
    success: boolean;
    correctedPosition?: Vec2;
    correctedRotation?: Rotation;
    attempts: number;
}

export class FurniturePlacementValidator {
    private roomGrid: RoomGrid;
    private placedFurnitures: PlacedFurniture[];

    constructor(roomGrid: RoomGrid, placedFurnitures: PlacedFurniture[]) {
        this.roomGrid = roomGrid;
        this.placedFurnitures = placedFurnitures;
    }

    /**
     * 验证家具摆放位置
     */
    validatePlacement(
        template: FurnitureTemplate,
        position: Vec2,
        rotation: Rotation,
        excludeFurnitureId?: string
    ): ValidationResult {
        // 获取旋转后的尺寸
        const size = this.getRotatedSize(template.baseSize, rotation);
        
        // 检查边界
        if (!this.isWithinBounds(position, size)) {
            return {
                isValid: false,
                reason: "超出房间边界"
            };
        }

        // 检查障碍物
        if (!this.isPositionClear(position, size)) {
            return {
                isValid: false,
                reason: "位置被障碍物占用"
            };
        }

        // 检查家具冲突
        const conflictResult = this.checkFurnitureConflict(position, size, excludeFurnitureId);
        if (!conflictResult.isValid) {
            return conflictResult;
        }

        // 挂饰类家具需要贴墙检查
        if (template.properties.isWallDecoration && !this.isAdjacentToWall(position)) {
            return {
                isValid: false,
                reason: "挂饰类家具必须贴墙摆放"
            };
        }

        return { isValid: true };
    }

    /**
     * 自动修正家具位置
     */
    autoCorrectPosition(
        template: FurnitureTemplate,
        originalPosition: Vec2,
        rotation: Rotation,
        maxAttempts: number = 50
    ): AutoCorrectionResult {
        const size = this.getRotatedSize(template.baseSize, rotation);
        const roomSize = this.roomGrid.getRoomSize();
        
        // 生成搜索顺序：从原位置开始螺旋搜索
        const searchPositions = this.generateSpiralSearchPositions(
            originalPosition,
            roomSize,
            size,
            maxAttempts
        );

        for (let i = 0; i < searchPositions.length; i++) {
            const testPosition = searchPositions[i];
            const validation = this.validatePlacement(template, testPosition, rotation);
            
            if (validation.isValid) {
                return {
                    success: true,
                    correctedPosition: testPosition,
                    correctedRotation: rotation,
                    attempts: i + 1
                };
            }
        }

        // 如果原旋转角度失败，尝试其他旋转角度
        const rotations = [Rotation.Deg90, Rotation.Deg180, Rotation.Deg270];
        for (const testRotation of rotations) {
            if (testRotation === rotation) continue;
            
            const testSize = this.getRotatedSize(template.baseSize, testRotation);
            const rotationSearchPositions = this.generateSpiralSearchPositions(
                originalPosition,
                roomSize,
                testSize,
                maxAttempts / 2
            );

            for (const testPosition of rotationSearchPositions) {
                const validation = this.validatePlacement(template, testPosition, testRotation);
                
                if (validation.isValid) {
                    return {
                        success: true,
                        correctedPosition: testPosition,
                        correctedRotation: testRotation,
                        attempts: maxAttempts
                    };
                }
            }
        }

        return {
            success: false,
            attempts: maxAttempts
        };
    }

    /**
     * 检查位置是否贴墙（使用新的格子状态逻辑）
     */
    private isAdjacentToWall(position: Vec2): boolean {
        // 在新的格子状态系统中，挂饰类家具只能放在 WallAdjacent (1) 格子上
        const gridState = this.roomGrid.getGridState(position);
        return gridState === 1; // GridState.WallAdjacent
    }

    /**
     * 检查位置是否在边界内
     */
    private isWithinBounds(position: Vec2, size: Vec2): boolean {
        const roomSize = this.roomGrid.getRoomSize();
        return position.x >= 0 && 
               position.y >= 0 && 
               position.x + size.x <= roomSize.x && 
               position.y + size.y <= roomSize.y;
    }

    /**
     * 检查位置是否没有障碍物
     */
    private isPositionClear(position: Vec2, size: Vec2): boolean {
        for (let dx = 0; dx < size.x; dx++) {
            for (let dy = 0; dy < size.y; dy++) {
                const checkPos = new Vec2(position.x + dx, position.y + dy);
                const gridState = this.roomGrid.getGridState(checkPos);
                if (gridState !== 0) { // 0表示空地，其他值表示障碍物
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查家具冲突
     */
    private checkFurnitureConflict(
        position: Vec2,
        size: Vec2,
        excludeFurnitureId?: string
    ): ValidationResult {
        for (const furniture of this.placedFurnitures) {
            if (excludeFurnitureId && furniture.id === excludeFurnitureId) {
                continue;
            }

            const furnitureSize = furniture.currentSize;
            
            // 检查矩形重叠
            if (this.isRectangleOverlap(
                position, size,
                furniture.position, furnitureSize
            )) {
                return {
                    isValid: false,
                    reason: `与家具 ${furniture.id} 冲突`
                };
            }
        }

        return { isValid: true };
    }

    /**
     * 检查两个矩形是否重叠
     */
    private isRectangleOverlap(
        pos1: Vec2, size1: Vec2,
        pos2: Vec2, size2: Vec2
    ): boolean {
        return !(pos1.x + size1.x <= pos2.x || 
                 pos2.x + size2.x <= pos1.x ||
                 pos1.y + size1.y <= pos2.y ||
                 pos2.y + size2.y <= pos1.y);
    }

    /**
     * 获取旋转后的尺寸
     */
    private getRotatedSize(originalSize: Vec2, rotation: Rotation): Vec2 {
        if (rotation === Rotation.Deg90 || rotation === Rotation.Deg270) {
            return new Vec2(originalSize.y, originalSize.x);
        }
        return new Vec2(originalSize.x, originalSize.y);
    }

    /**
     * 生成螺旋搜索位置
     */
    private generateSpiralSearchPositions(
        center: Vec2,
        roomSize: Vec2,
        furnitureSize: Vec2,
        maxPositions: number
    ): Vec2[] {
        const positions: Vec2[] = [];
        const visited = new Set<string>();
        
        // 添加中心位置
        if (this.isWithinBounds(center, furnitureSize)) {
            positions.push(new Vec2(center.x, center.y));
            visited.add(`${center.x},${center.y}`);
        }

        // 螺旋搜索
        for (let radius = 1; radius <= Math.max(roomSize.x, roomSize.y) && positions.length < maxPositions; radius++) {
            for (let dx = -radius; dx <= radius && positions.length < maxPositions; dx++) {
                for (let dy = -radius; dy <= radius && positions.length < maxPositions; dy++) {
                    // 只处理边界上的点
                    if (Math.abs(dx) !== radius && Math.abs(dy) !== radius) {
                        continue;
                    }

                    const testPos = new Vec2(center.x + dx, center.y + dy);
                    const key = `${testPos.x},${testPos.y}`;
                    
                    if (!visited.has(key) && this.isWithinBounds(testPos, furnitureSize)) {
                        positions.push(testPos);
                        visited.add(key);
                    }
                }
            }
        }

        return positions;
    }
}
