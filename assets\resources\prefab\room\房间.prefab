[{"__type__": "cc.Prefab", "_name": "房间", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "房间", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}], "_active": true, "_components": [{"__id__": 40}, {"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": -540, "y": -960, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "scaleNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 512, "y": 240, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 0.72, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "一楼地板", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 41.82072815970446, "y": 41.8207281597044, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.3787318275466102, "w": 0.9213901273750129}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8713208364316786, "y": 0.8713208364316787, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 44.26032530160103}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 650}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57lBdfPphI8L8aOJ2x3mgk"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 325, "_verticalCenter": 325, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcsVH6U7hEI5/aW3T5lRIe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1w2a4CAFJspmL50lXgdmm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eh2VqCdNJjrBYsQvsbAhM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d4SOV/aiNChKx76ug5gQ80", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "家具层", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 21}, {"__id__": 29}], "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 39}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "416d89e0-7b88-453d-b725-a153450e381c", "__expectedType__": "cc.Prefab"}, "fileId": "1eVuH9GVZNK5MKPPr6r3o7", "instance": {"__id__": 15}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "96VOaqnJ1F8rMaudGEbQNc", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_name"], "value": "家具1x1"}, {"__type__": "cc.TargetInfo", "localID": ["1eVuH9GVZNK5MKPPr6r3o7"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 801.838, "y": 925.936, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 22}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 21}, "asset": {"__uuid__": "a64d7bdd-64ed-4d97-ace6-79468cc00719", "__expectedType__": "cc.Prefab"}, "fileId": "d9y20Pb0NG2YCUKrseORSg", "instance": {"__id__": 23}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "10TybdNclCVab8gtpigfGQ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_name"], "value": "家具1x2"}, {"__type__": "cc.TargetInfo", "localID": ["d9y20Pb0NG2YCUKrseORSg"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 574.831, "y": 988.747, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 30}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 29}, "asset": {"__uuid__": "7d49bb47-cf8a-4776-b24d-05660ba6fc75", "__expectedType__": "cc.Prefab"}, "fileId": "be/tLbCzJEtqKhypFRbTXV", "instance": {"__id__": 31}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5ccKqEegND6b+bV2nQ7mFq", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 32}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_name"], "value": "家具2x2"}, {"__type__": "cc.TargetInfo", "localID": ["be/tLbCzJEtqKhypFRbTXV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 252.238, "y": 919.643, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 38}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bIcy6r+JMIZBqydn12i9Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aVJ1uvs1HaaSryB9SRVKG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7TUoSYgdNVLkC7aVL/Gda"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 43}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75cKGjCENH0YNgXnhjyRP2"}, {"__type__": "9cd71L8WxhA0IXFEGzvFm1i", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 45}, "floorNode": {"__id__": 3}, "jiaju1x1": {"__id__": 13}, "jiaju1x2": {"__id__": 21}, "jiaju2x2": {"__id__": 29}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50MYekLodGrZQg4dF8iNvt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adT5J/mL1DAr1ly16PNfe4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 29}, {"__id__": 21}, {"__id__": 13}]}]