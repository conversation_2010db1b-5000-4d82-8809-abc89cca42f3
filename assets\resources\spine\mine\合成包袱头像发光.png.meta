{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "fb3cd133-1be6-427b-a217-b6ff8b55fd49", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "fb3cd133-1be6-427b-a217-b6ff8b55fd49@6c48a", "displayName": "合成包袱头像发光", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "fb3cd133-1be6-427b-a217-b6ff8b55fd49", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "fb3cd133-1be6-427b-a217-b6ff8b55fd49@f9941", "displayName": "合成包袱头像发光", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 453, "height": 474, "rawWidth": 453, "rawHeight": 474, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-226.5, -237, 0, 226.5, -237, 0, -226.5, 237, 0, 226.5, 237, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 474, 453, 474, 0, 0, 453, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-226.5, -237, 0], "maxPos": [226.5, 237, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "fb3cd133-1be6-427b-a217-b6ff8b55fd49@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "fb3cd133-1be6-427b-a217-b6ff8b55fd49@6c48a"}}