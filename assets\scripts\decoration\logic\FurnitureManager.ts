/**
 * 家具管理器
 * 负责管理家具模板、已放置的家具和相关操作
 */

import { Vec2 } from "cc";
import {
    FurnitureTemplate,
    PlacedFurniture,
    RoomData,
    Rotation,
    FurnitureType,
    FurnitureTheme,
    generateFurnitureId,
    getRotatedSize,
    PlacementResult,
    FurnitureOperation,
    FurnitureOperationRecord
} from "./DecorationDefine";
import { RoomGrid } from "./RoomGrid";
import { FurniturePlacementValidator, ValidationResult, AutoCorrectionResult } from "./FurniturePlacementValidator";
import { FurnitureRandomizer, RandomizeResult } from "./FurnitureRandomizer";
import { PlacementValidator } from "./PlacementValidator";
import { DataManager } from "../../DataManager";
import { IDataLoader } from "../../data_loader";
import { JSON } from "../../common";
import { MultiRoomManager } from "./MultiRoomManager";
import { DefaultRoomTemplates } from "./DefaultRoomTemplates";

/**
 * 房间创建选项接口
 */
interface RoomSetupOptions {
    mode: 'single' | 'dual' | 'multi';  // 房间模式：单房间、双房间、多房间
    roomTemplateIds?: string[];          // 指定房间模板ID列表（可选）
    useRecommended?: boolean;            // 是否使用推荐配置（默认true）
    customRoomConfigs?: Array<{          // 自定义房间配置（可选）
        templateId: string;
        position?: Vec2;                 // 房间位置（多房间布局用）
    }>;
}

/**
 * 房间创建结果接口
 */
interface RoomSetupResult {
    success: boolean;
    roomIds?: string[];
    roomConfigs?: Array<{id: string, name: string, size: Vec2}>;
    message?: string;
}

export class FurnitureManager {
    private static _instance: FurnitureManager;

    public static getInstance(): FurnitureManager {
        return this._instance || (this._instance = new FurnitureManager());
    }

    private furnitureTemplates: Map<number, FurnitureTemplate> = new Map();
    private currentRoom: RoomData | null = null;
    private roomGrid: RoomGrid | null = null;
    private placementValidator: PlacementValidator | null = null;
    private advancedValidator: FurniturePlacementValidator | null = null;
    private furnitureRandomizer: FurnitureRandomizer | null = null;
    private operationHistory: FurnitureOperationRecord[] = [];

    private constructor() {}

    /**
     * 初始化家具管理器
     */
    async init() {
        await this.loadFurnitureTemplates();
        await this.loadRoomData();
    }

    /**
     * 加载家具模板配置
     */
    private async loadFurnitureTemplates() {
        try {
            const configData = await JSON.loadJson('json/furnitureTemplates');
            const templates = configData.json?.templates || [];
            
            this.furnitureTemplates.clear();
            templates.forEach((template: FurnitureTemplate) => {
                this.furnitureTemplates.set(template.id, template);
            });
            
            console.log('家具模板加载成功，共', this.furnitureTemplates.size, '个模板');
        } catch (error) {
            console.warn('家具模板加载失败:', error);
            this.loadDefaultTemplates();
        }
    }

    /**
     * 加载默认家具模板
     */
    private loadDefaultTemplates() {
        const defaultTemplates: FurnitureTemplate[] = [
            {
                id: 1,
                name: "椅子",
                type: FurnitureType.Small,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/chair",
                description: "舒适的椅子",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 100,
                    beauty: 15
                }
            },
            {
                id: 2,
                name: "床",
                type: FurnitureType.Medium,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/bed",
                description: "温暖的床铺",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 250,
                    beauty: 25
                }
            },
            {
                id: 3,
                name: "桌子",
                type: FurnitureType.Large,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/table",
                description: "实用的桌子",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 2,
                    value: 300,
                    beauty: 30
                }
            }
        ];

        defaultTemplates.forEach(template => {
            this.furnitureTemplates.set(template.id, template);
        });
    }

    /**
     * 加载房间数据
     */
    private async loadRoomData() {
        try {
            const roomData = await DataManager.instance.loadData("roomData") as RoomData;
            if (roomData) {
                this.setCurrentRoom(roomData);
            } else {
                this.createDefaultRoom();
            }
        } catch (error) {
            console.warn('房间数据加载失败:', error);
            this.createDefaultRoom();
        }
    }

    /**
     * 创建默认房间
     */
    private createDefaultRoom() {
        const defaultLayout = [
            [1,1,1,0,0,0,0,1,1,1],
            [1,1,0,0,0,0,0,0,1,1],
            [1,0,0,0,0,0,0,0,0,1],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [0,0,0,0,0,0,0,0,0,0],
            [1,0,0,0,0,0,0,0,0,1],
            [1,1,0,0,0,0,0,0,1,1],
            [1,1,1,0,0,0,0,1,1,1]
        ];

        const roomData: RoomData = {
            id: 1,
            name: "默认房间",
            gridLayout: defaultLayout,
            placedFurnitures: [],
            lastModified: Date.now()
        };

        this.setCurrentRoom(roomData);
    }

    /**
     * 设置当前房间
     */
    setCurrentRoom(roomData: RoomData) {
        this.currentRoom = roomData;
        this.roomGrid = new RoomGrid(new Vec2(roomData.gridLayout[0].length, roomData.gridLayout.length), roomData.gridLayout);
        this.placementValidator = new PlacementValidator(this.roomGrid);

        // 初始化新的验证器和随机器
        this.advancedValidator = new FurniturePlacementValidator(this.roomGrid, roomData.placedFurnitures);
        this.furnitureRandomizer = new FurnitureRandomizer(this.roomGrid);

        // 恢复已放置的家具
        roomData.placedFurnitures.forEach(furniture => {
            const template = this.getFurnitureTemplate(furniture.templateId);
            const isWallDecoration = template?.properties.isWallDecoration || false;
            this.roomGrid!.placeFurniture(furniture, isWallDecoration);
        });
    }

    /**
     * 获取家具模板
     */
    getFurnitureTemplate(templateId: number): FurnitureTemplate | null {
        return this.furnitureTemplates.get(templateId) || null;
    }

    /**
     * 获取所有家具模板
     */
    getAllFurnitureTemplates(): FurnitureTemplate[] {
        return Array.from(this.furnitureTemplates.values());
    }

    /**
     * 获取家具模板Map
     */
    getFurnitureTemplatesMap(): Map<number, FurnitureTemplate> {
        return new Map(this.furnitureTemplates);
    }

    /**
     * 清空所有家具
     */
    clearAllFurnitures(): void {
        if (!this.currentRoom || !this.roomGrid) {
            return;
        }

        // 移除所有家具
        this.currentRoom.placedFurnitures.forEach(furniture => {
            this.roomGrid!.removeFurniture(furniture.id);
        });

        this.currentRoom.placedFurnitures = [];
        this.currentRoom.lastModified = Date.now();
        this.saveRoomData();
    }

    /**
     * 放置家具
     * @param templateId 家具模板ID
     * @param position 放置位置坐标
     * @param rotation 旋转角度，默认为0度
     * @returns 放置结果，包含成功状态和错误信息
     */
    placeFurniture(templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): PlacementResult {
        if (!this.currentRoom || !this.roomGrid || !this.placementValidator) {
            return { success: false, errorMessage: "房间未初始化" };
        }

        const template = this.getFurnitureTemplate(templateId);
        if (!template) {
            return { success: false, errorMessage: "家具模板不存在" };
        }

        // 验证摆放位置
        const validationResult = this.placementValidator.validatePlacement(template, position, rotation);
        if (!validationResult.success) {
            return validationResult;
        }

        // 创建家具实例
        const furniture: PlacedFurniture = {
            id: generateFurnitureId(),
            templateId,
            position: position.clone(),
            rotation,
            currentSize: getRotatedSize(template.baseSize, rotation),
            placedTime: Date.now()
        };

        // 放置家具
        const isWallDecoration = template.properties.isWallDecoration || false;
        if (this.roomGrid.placeFurniture(furniture, isWallDecoration)) {
            this.currentRoom.placedFurnitures.push(furniture);
            this.recordOperation({
                operation: FurnitureOperation.Place,
                furnitureId: furniture.id,
                newPosition: position,
                newRotation: rotation,
                timestamp: Date.now()
            });
            this.saveRoomData();
            return { success: true };
        }

        return { success: false, errorMessage: "放置失败" };
    }

    /**
     * 移除家具
     * @param furnitureId 家具实例ID
     * @returns 是否成功移除
     */
    removeFurniture(furnitureId: string): boolean {
        if (!this.currentRoom || !this.roomGrid) {
            return false;
        }

        const furnitureIndex = this.currentRoom.placedFurnitures.findIndex(f => f.id === furnitureId);
        if (furnitureIndex === -1) {
            return false;
        }

        const furniture = this.currentRoom.placedFurnitures[furnitureIndex];
        
        if (this.roomGrid.removeFurniture(furnitureId)) {
            this.currentRoom.placedFurnitures.splice(furnitureIndex, 1);
            this.recordOperation({
                operation: FurnitureOperation.Remove,
                furnitureId,
                oldPosition: furniture.position,
                oldRotation: furniture.rotation,
                timestamp: Date.now()
            });
            this.saveRoomData();
            return true;
        }

        return false;
    }

    /**
     * 移动家具
     * @param furnitureId 家具实例ID
     * @param newPosition 新的位置坐标
     * @returns 移动结果，包含成功状态和错误信息
     */
    moveFurniture(furnitureId: string, newPosition: Vec2): PlacementResult {
        if (!this.currentRoom || !this.roomGrid || !this.placementValidator) {
            return { success: false, errorMessage: "房间未初始化" };
        }

        const furniture = this.currentRoom.placedFurnitures.find(f => f.id === furnitureId);
        if (!furniture) {
            return { success: false, errorMessage: "家具不存在" };
        }

        // 验证新位置
        const validationResult = this.placementValidator.validateMove(furniture, newPosition);
        if (!validationResult.success) {
            return validationResult;
        }

        const oldPosition = furniture.position.clone();
        
        // 更新家具位置
        const newFurniture = { ...furniture, position: newPosition.clone() };
        const template = this.getFurnitureTemplate(furniture.templateId);
        const isWallDecoration = template?.properties.isWallDecoration || false;

        if (this.roomGrid.moveFurniture(furnitureId, newFurniture, isWallDecoration)) {
            furniture.position = newPosition.clone();
            this.recordOperation({
                operation: FurnitureOperation.Move,
                furnitureId,
                oldPosition,
                newPosition,
                timestamp: Date.now()
            });
            this.saveRoomData();
            return { success: true };
        }

        return { success: false, errorMessage: "移动失败" };
    }

    /**
     * 旋转家具
     * @param furnitureId 家具实例ID
     * @param newRotation 新的旋转角度
     * @returns 旋转结果，包含成功状态和错误信息
     */
    rotateFurniture(furnitureId: string, newRotation: Rotation): PlacementResult {
        if (!this.currentRoom || !this.roomGrid || !this.placementValidator) {
            return { success: false, errorMessage: "房间未初始化" };
        }

        const furniture = this.currentRoom.placedFurnitures.find(f => f.id === furnitureId);
        if (!furniture) {
            return { success: false, errorMessage: "家具不存在" };
        }

        const template = this.getFurnitureTemplate(furniture.templateId);
        if (!template) {
            return { success: false, errorMessage: "家具模板不存在" };
        }

        // 验证旋转
        const validationResult = this.placementValidator.validateRotation(furniture, newRotation);
        if (!validationResult.success) {
            return validationResult;
        }

        const oldRotation = furniture.rotation;
        
        // 先移除旧位置
        this.roomGrid.removeFurniture(furnitureId);
        
        // 更新旋转和尺寸
        furniture.rotation = newRotation;
        furniture.currentSize = getRotatedSize(template.baseSize, newRotation);
        
        // 重新放置
        const isWallDecoration = template.properties.isWallDecoration || false;
        if (this.roomGrid.placeFurniture(furniture, isWallDecoration)) {
            this.recordOperation({
                operation: FurnitureOperation.Rotate,
                furnitureId,
                oldRotation,
                newRotation,
                timestamp: Date.now()
            });
            this.saveRoomData();
            return { success: true };
        }

        return { success: false, errorMessage: "旋转失败" };
    }

    /**
     * 获取当前房间的所有家具
     */
    getPlacedFurnitures(): PlacedFurniture[] {
        return this.currentRoom?.placedFurnitures || [];
    }

    /**
     * 获取所有挂饰类家具
     */
    getWallDecorations(): PlacedFurniture[] {
        if (!this.currentRoom) {
            return [];
        }

        return this.currentRoom.placedFurnitures.filter(furniture => {
            const template = this.getFurnitureTemplate(furniture.templateId);
            return template?.properties.isWallDecoration || false;
        });
    }

    /**
     * 获取房间格子
     */
    getRoomGrid(): RoomGrid | null {
        return this.roomGrid;
    }

    /**
     * 记录操作历史
     */
    private recordOperation(operation: FurnitureOperationRecord) {
        this.operationHistory.push(operation);
        // 限制历史记录数量
        if (this.operationHistory.length > 100) {
            this.operationHistory.shift();
        }
    }

    /**
     * 保存房间数据
     */
    private saveRoomData() {
        if (this.currentRoom) {
            this.currentRoom.lastModified = Date.now();
            DataManager.instance.saveData("roomData", this.currentRoom);
        }
    }

    /**
     * 高级位置验证
     */
    validatePlacementAdvanced(
        templateId: number,
        position: Vec2,
        rotation: Rotation,
        excludeFurnitureId?: string
    ): ValidationResult {
        if (!this.advancedValidator) {
            return {
                isValid: false,
                reason: "验证器未初始化"
            };
        }

        const template = this.furnitureTemplates.get(templateId);
        if (!template) {
            return {
                isValid: false,
                reason: "找不到家具模板"
            };
        }

        return this.advancedValidator.validatePlacement(template, position, rotation, excludeFurnitureId);
    }

    /**
     * 随机变化家具位置和角度
     */
    randomizeFurniture(furnitureId: string): RandomizeResult | null {
        if (!this.furnitureRandomizer || !this.currentRoom) {
            return null;
        }

        const furniture = this.currentRoom.placedFurnitures.find(f => f.id === furnitureId);
        if (!furniture) {
            return null;
        }

        const template = this.furnitureTemplates.get(furniture.templateId);
        if (!template) {
            return null;
        }

        return this.furnitureRandomizer.randomizeFurniture(
            furniture,
            template,
            this.currentRoom.placedFurnitures
        );
    }

    /**
     * 自动修正家具位置
     */
    autoCorrectFurniturePosition(
        templateId: number,
        originalPosition: Vec2,
        rotation: Rotation,
        maxAttempts: number = 50
    ): AutoCorrectionResult {
        if (!this.advancedValidator) {
            return {
                success: false,
                attempts: 0
            };
        }

        const template = this.furnitureTemplates.get(templateId);
        if (!template) {
            return {
                success: false,
                attempts: 0
            };
        }

        return this.advancedValidator.autoCorrectPosition(template, originalPosition, rotation, maxAttempts);
    }

    /**
     * 应用随机变化结果
     */
    applyRandomizeResult(result: RandomizeResult): boolean {
        if (!result.success || !result.newFurniture || !this.currentRoom) {
            return false;
        }

        // 找到并更新家具
        const index = this.currentRoom.placedFurnitures.findIndex(
            f => f.id === result.originalFurniture.id
        );

        if (index === -1) {
            return false;
        }

        // 更新家具数据
        this.currentRoom.placedFurnitures[index] = result.newFurniture;

        // 更新房间网格
        if (this.roomGrid) {
            this.roomGrid.removeFurniture(result.originalFurniture.id);
            const template = this.getFurnitureTemplate(result.newFurniture.templateId);
            const isWallDecoration = template?.properties.isWallDecoration || false;
            this.roomGrid.placeFurniture(result.newFurniture, isWallDecoration);
        }

        // 更新验证器
        if (this.advancedValidator) {
            this.advancedValidator = new FurniturePlacementValidator(
                this.roomGrid!,
                this.currentRoom.placedFurnitures
            );
        }

        return true;
    }

    /**
     * 检查家具是否贴墙（用于挂饰类家具）
     */
    isAdjacentToWall(position: Vec2): boolean {
        if (!this.roomGrid) {
            return false;
        }

        const directions = [
            new Vec2(-1, 0), // 左
            new Vec2(1, 0),  // 右
            new Vec2(0, -1), // 上
            new Vec2(0, 1)   // 下
        ];

        for (const dir of directions) {
            const checkPos = new Vec2(position.x + dir.x, position.y + dir.y);

            // 检查是否是边界（视为墙壁）
            const roomSize = this.roomGrid.getRoomSize();
            if (checkPos.x < 0 || checkPos.x >= roomSize.x ||
                checkPos.y < 0 || checkPos.y >= roomSize.y) {
                return true;
            }

            // 检查是否是障碍物
            const gridState = this.roomGrid.getGridState(checkPos);
            if (gridState !== 0) { // 0表示空地，其他值表示障碍物
                return true;
            }
        }

        return false;
    }

    // ==================== 双房间系统入口函数 ====================

    /**
     * 启用双房间模式
     * 这是进入双房间系统的主要入口函数
     */
    async enableMultiRoomMode(): Promise<boolean> {
        try {
            const multiRoomMgr = MultiRoomManager.getInstance();

            // 使用当前的家具模板初始化多房间系统
            await multiRoomMgr.init(this.getFurnitureTemplatesMap());

            console.log("双房间模式已启用");
            return true;
        } catch (error) {
            console.error("启用双房间模式失败:", error);
            return false;
        }
    }

    /**
     * 获取多房间管理器实例
     * 用于访问双房间系统的功能
     */
    getMultiRoomManager() {
        try {
            return MultiRoomManager.getInstance();
        } catch (error) {
            console.error("获取多房间管理器失败:", error);
            return null;
        }
    }

    /**
     * 检查双房间系统是否可用
     */
    isMultiRoomSystemAvailable(): boolean {
        try {
            // 检查必要的类是否可用
            return !!(MultiRoomManager && DefaultRoomTemplates);
        } catch (error) {
            console.warn("双房间系统不可用:", error);
            return false;
        }
    }

    /**
     * 创建房间配置
     * 统一的房间创建入口函数，支持单房间和多房间模式
     * @param options 房间创建选项
     */
    async createRoomSetup(options: RoomSetupOptions = { mode: 'single', useRecommended: true }): Promise<RoomSetupResult> {
        try {
            const multiRoomMgr = MultiRoomManager.getInstance();

            let setupMessage = "";

            // 根据模式选择房间配置
            switch (options.mode) {
                case 'single':
                    await this.createSingleRoomConfig(options);
                    setupMessage = `成功创建单房间配置`;
                    break;

                case 'dual':
                    await this.createDualRoomConfig(options);
                    setupMessage = `成功创建双房间配置`;
                    break;

                case 'multi':
                    await this.createMultiRoomConfig(options);
                    setupMessage = `成功创建多房间配置`;
                    break;

                default:
                    throw new Error(`不支持的房间模式: ${options.mode}`);
            }

            // 初始化多房间管理器
            await multiRoomMgr.init(this.getFurnitureTemplatesMap());

            // 获取房间ID列表和配置信息
            const roomIds = multiRoomMgr.getAllRoomIds();
            const roomConfigs = roomIds.map(roomId => {
                const config = multiRoomMgr.getRoomConfig(roomId);
                return config ? {
                    id: config.id,
                    name: config.name,
                    size: config.size
                } : null;
            }).filter(config => config !== null);

            return {
                success: true,
                roomIds: roomIds,
                roomConfigs: roomConfigs,
                message: `${setupMessage}，包含 ${roomIds.length} 个房间`
            };
        } catch (error: any) {
            return {
                success: false,
                message: `创建房间配置失败: ${error?.message || error}`
            };
        }
    }

    /**
     * 创建单房间配置
     */
    private async createSingleRoomConfig(options: any): Promise<any[]> {
        if (options.customRoomConfigs && options.customRoomConfigs.length > 0) {
            // 使用自定义配置
            const templateId = options.customRoomConfigs[0].templateId;
            return [DefaultRoomTemplates.getTemplateById(templateId)];
        } else if (options.roomTemplateIds && options.roomTemplateIds.length > 0) {
            // 使用指定的模板ID
            return [DefaultRoomTemplates.getTemplateById(options.roomTemplateIds[0])];
        } else {
            // 使用默认推荐的单房间配置
            const allTemplates = DefaultRoomTemplates.getAllTemplates();
            return [allTemplates[0]]; // 选择第一个作为默认单房间
        }
    }

    /**
     * 创建双房间配置
     */
    private async createDualRoomConfig(options: any): Promise<any[]> {
        if (options.customRoomConfigs && options.customRoomConfigs.length >= 2) {
            // 使用自定义配置
            return options.customRoomConfigs.slice(0, 2).map((config: any) =>
                DefaultRoomTemplates.getTemplateById(config.templateId)
            );
        } else if (options.roomTemplateIds && options.roomTemplateIds.length >= 2) {
            // 使用指定的模板ID
            return options.roomTemplateIds.slice(0, 2).map((templateId: string) =>
                DefaultRoomTemplates.getTemplateById(templateId)
            );
        } else {
            // 使用推荐的双房间配置
            const dualSetup = DefaultRoomTemplates.getRecommendedDualRoomSetup();
            return [dualSetup.roomA, dualSetup.roomB];
        }
    }

    /**
     * 创建多房间配置
     */
    private async createMultiRoomConfig(options: any): Promise<any[]> {
        if (options.customRoomConfigs && options.customRoomConfigs.length > 0) {
            // 使用自定义配置
            return options.customRoomConfigs.map((config: any) =>
                DefaultRoomTemplates.getTemplateById(config.templateId)
            );
        } else if (options.roomTemplateIds && options.roomTemplateIds.length > 0) {
            // 使用指定的模板ID
            return options.roomTemplateIds.map((templateId: string) =>
                DefaultRoomTemplates.getTemplateById(templateId)
            );
        } else {
            // 使用所有可用的房间模板
            return DefaultRoomTemplates.getAllTemplates();
        }
    }

    /**
     * 创建双房间配置（向后兼容）
     * @deprecated 请使用 createRoomSetup({mode: 'dual'}) 替代
     */
    async createDualRoomSetup(): Promise<{success: boolean, roomIds?: string[], message?: string}> {
        const result = await this.createRoomSetup({ mode: 'dual' });
        return {
            success: result.success,
            roomIds: result.roomIds,
            message: result.message
        };
    }

    /**
     * 在指定房间放置家具（双房间模式）
     * @param roomId 房间ID
     * @param templateId 家具模板ID
     * @param position 放置位置坐标
     * @param rotation 旋转角度，默认为0度
     * @returns 放置结果的Promise，包含成功状态和错误信息
     */
    async placeFurnitureInRoom(
        roomId: string,
        templateId: number,
        position: Vec2,
        rotation: Rotation = Rotation.Deg0
    ): Promise<PlacementResult> {
        try {
            const multiRoomMgr = this.getMultiRoomManager();
            if (!multiRoomMgr) {
                return { success: false, errorMessage: "多房间管理器不可用" };
            }

            return multiRoomMgr.placeFurniture(roomId, templateId, position, rotation);
        } catch (error: any) {
            return {
                success: false,
                errorMessage: `在房间 ${roomId} 放置家具失败: ${error?.message || error}`
            };
        }
    }

    /**
     * 获取所有房间的家具列表（双房间模式）
     */
    getAllRoomsFurnitures(): {[roomId: string]: PlacedFurniture[]} {
        try {
            const multiRoomMgr = this.getMultiRoomManager();
            if (!multiRoomMgr) {
                return {};
            }

            const result: {[roomId: string]: PlacedFurniture[]} = {};
            const roomIds = multiRoomMgr.getAllRoomIds();

            for (const roomId of roomIds) {
                result[roomId] = multiRoomMgr.getRoomFurnitures(roomId);
            }

            return result;
        } catch (error) {
            console.error("获取所有房间家具失败:", error);
            return {};
        }
    }

    /**
     * 计算双房间系统的总评分
     */
    calculateMultiRoomScore() {
        try {
            const multiRoomMgr = this.getMultiRoomManager();
            if (!multiRoomMgr) {
                return null;
            }

            return multiRoomMgr.calculateScore();
        } catch (error) {
            console.error("计算双房间评分失败:", error);
            return null;
        }
    }

    /**
     * 清空指定房间（双房间模式）
     */
    clearRoom(roomId: string): boolean {
        try {
            const multiRoomMgr = this.getMultiRoomManager();
            if (!multiRoomMgr) {
                return false;
            }

            return multiRoomMgr.clearRoom(roomId);
        } catch (error) {
            console.error(`清空房间 ${roomId} 失败:`, error);
            return false;
        }
    }

    /**
     * 获取房间配置信息（双房间模式）
     */
    getRoomInfo(roomId: string) {
        try {
            const multiRoomMgr = this.getMultiRoomManager();
            if (!multiRoomMgr) {
                return null;
            }

            const roomConfig = multiRoomMgr.getRoomConfig(roomId);
            if (!roomConfig) {
                return null;
            }

            return {
                id: roomConfig.id,
                name: roomConfig.name,
                size: roomConfig.size,
                furnitureCount: roomConfig.placedFurnitures.length,
                lastModified: roomConfig.lastModified
            };
        } catch (error) {
            console.error(`获取房间 ${roomId} 信息失败:`, error);
            return null;
        }
    }

    /**
     * 获取所有可用的房间模板
     */
    getAvailableRoomTemplates() {
        try {
            return DefaultRoomTemplates.getAllTemplates();
        } catch (error) {
            console.error("获取房间模板失败:", error);
            return [];
        }
    }

    // ==================== 双房间系统入口函数结束 ====================

    /**
     * 数据加载器
     */
    loader: IDataLoader = {
        name: "FurnitureManager",
        total: async () => 1,
        load: async (update) => {
            await this.init();
            update(1);
        },
    };
}
