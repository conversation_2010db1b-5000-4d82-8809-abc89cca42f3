/**
 * 统一房间创建入口函数测试
 * 验证 createRoomSetup 函数的各种模式和参数组合
 */

import { Component, Vec2, _decorator } from "cc";
import { FurnitureManager } from "../logic/FurnitureManager";

const { ccclass, property } = _decorator;

@ccclass('UnifiedRoomSetupTest')
export class UnifiedRoomSetupTest extends Component {
    
    private furnitureManager: FurnitureManager | null = null;

    async start() {
        console.log("=== 统一房间创建入口函数测试开始 ===");
        
        try {
            this.furnitureManager = FurnitureManager.getInstance();
            await this.furnitureManager.init();
            
            await this.testSingleRoomMode();
            await this.testDualRoomMode();
            await this.testMultiRoomMode();
            await this.testCustomConfigurations();
            await this.testErrorHandling();
            await this.testBackwardCompatibility();
            
            console.log("=== 统一房间创建入口函数测试完成 ===");
        } catch (error) {
            console.error("测试过程中出现错误:", error);
        }
    }

    /**
     * 测试单房间模式
     */
    private async testSingleRoomMode() {
        console.log("--- 测试单房间模式 ---");
        
        if (!this.furnitureManager) return;

        // 测试默认单房间配置
        const result1 = await this.furnitureManager.createRoomSetup({
            mode: 'single'
        });
        
        console.log(`默认单房间创建: ${result1.success ? "成功" : "失败"}`);
        console.log(`消息: ${result1.message}`);
        if (result1.success && result1.roomIds) {
            console.log(`房间数量: ${result1.roomIds.length}`);
            console.log(`房间ID: [${result1.roomIds.join(', ')}]`);
        }

        // 测试指定模板ID的单房间
        const result2 = await this.furnitureManager.createRoomSetup({
            mode: 'single',
            roomTemplateIds: ['room_a']
        });
        
        console.log(`指定模板单房间创建: ${result2.success ? "成功" : "失败"}`);
        if (result2.success && result2.roomConfigs) {
            result2.roomConfigs.forEach(config => {
                console.log(`- 房间: ${config.name} (${config.size.x}×${config.size.y})`);
            });
        }
    }

    /**
     * 测试双房间模式
     */
    private async testDualRoomMode() {
        console.log("--- 测试双房间模式 ---");
        
        if (!this.furnitureManager) return;

        // 测试默认双房间配置
        const result1 = await this.furnitureManager.createRoomSetup({
            mode: 'dual'
        });
        
        console.log(`默认双房间创建: ${result1.success ? "成功" : "失败"}`);
        console.log(`消息: ${result1.message}`);
        if (result1.success && result1.roomConfigs) {
            result1.roomConfigs.forEach(config => {
                console.log(`- 房间: ${config.name} (${config.size.x}×${config.size.y})`);
            });
        }

        // 测试指定模板ID的双房间
        const result2 = await this.furnitureManager.createRoomSetup({
            mode: 'dual',
            roomTemplateIds: ['room_a', 'room_b']
        });
        
        console.log(`指定模板双房间创建: ${result2.success ? "成功" : "失败"}`);
        if (result2.success) {
            console.log(`房间数量: ${result2.roomIds?.length || 0}`);
        }

        // 测试推荐配置
        const result3 = await this.furnitureManager.createRoomSetup({
            mode: 'dual',
            useRecommended: true
        });
        
        console.log(`推荐配置双房间创建: ${result3.success ? "成功" : "失败"}`);
    }

    /**
     * 测试多房间模式
     */
    private async testMultiRoomMode() {
        console.log("--- 测试多房间模式 ---");
        
        if (!this.furnitureManager) return;

        // 测试默认多房间配置（所有可用模板）
        const result1 = await this.furnitureManager.createRoomSetup({
            mode: 'multi'
        });
        
        console.log(`默认多房间创建: ${result1.success ? "成功" : "失败"}`);
        console.log(`消息: ${result1.message}`);
        if (result1.success && result1.roomConfigs) {
            console.log(`房间总数: ${result1.roomConfigs.length}`);
            result1.roomConfigs.forEach(config => {
                console.log(`- 房间: ${config.name} (${config.size.x}×${config.size.y})`);
            });
        }

        // 测试指定多个模板ID
        const result2 = await this.furnitureManager.createRoomSetup({
            mode: 'multi',
            roomTemplateIds: ['room_a', 'room_b', 'large_room']
        });
        
        console.log(`指定模板多房间创建: ${result2.success ? "成功" : "失败"}`);
        if (result2.success) {
            console.log(`指定房间数量: ${result2.roomIds?.length || 0}`);
        }
    }

    /**
     * 测试自定义配置
     */
    private async testCustomConfigurations() {
        console.log("--- 测试自定义配置 ---");
        
        if (!this.furnitureManager) return;

        // 测试自定义双房间配置
        const result1 = await this.furnitureManager.createRoomSetup({
            mode: 'dual',
            customRoomConfigs: [
                { templateId: 'room_a', position: new Vec2(0, 0) },
                { templateId: 'small_room', position: new Vec2(10, 0) }
            ]
        });
        
        console.log(`自定义双房间配置: ${result1.success ? "成功" : "失败"}`);
        if (result1.success && result1.roomConfigs) {
            result1.roomConfigs.forEach(config => {
                console.log(`- 自定义房间: ${config.name} (${config.size.x}×${config.size.y})`);
            });
        }

        // 测试自定义多房间配置
        const result2 = await this.furnitureManager.createRoomSetup({
            mode: 'multi',
            customRoomConfigs: [
                { templateId: 'room_a' },
                { templateId: 'room_b' },
                { templateId: 'large_room' },
                { templateId: 'small_room' }
            ]
        });
        
        console.log(`自定义多房间配置: ${result2.success ? "成功" : "失败"}`);
        if (result2.success) {
            console.log(`自定义房间数量: ${result2.roomIds?.length || 0}`);
        }
    }

    /**
     * 测试错误处理
     */
    private async testErrorHandling() {
        console.log("--- 测试错误处理 ---");
        
        if (!this.furnitureManager) return;

        // 测试无效的房间模式
        try {
            const result1 = await this.furnitureManager.createRoomSetup({
                mode: 'invalid' as any
            });
            console.log(`无效模式处理: ${result1.success ? "意外成功" : "正确失败"}`);
            console.log(`错误信息: ${result1.message}`);
        } catch (error) {
            console.log(`无效模式异常捕获: 正确`);
        }

        // 测试无效的模板ID
        const result2 = await this.furnitureManager.createRoomSetup({
            mode: 'single',
            roomTemplateIds: ['invalid_template_id']
        });
        
        console.log(`无效模板ID处理: ${result2.success ? "成功" : "失败"}`);
        if (!result2.success) {
            console.log(`错误信息: ${result2.message}`);
        }

        // 测试空的自定义配置
        const result3 = await this.furnitureManager.createRoomSetup({
            mode: 'dual',
            customRoomConfigs: []
        });
        
        console.log(`空自定义配置处理: ${result3.success ? "成功" : "失败"}`);
    }

    /**
     * 测试向后兼容性
     */
    private async testBackwardCompatibility() {
        console.log("--- 测试向后兼容性 ---");
        
        if (!this.furnitureManager) return;

        // 测试旧的 createDualRoomSetup 方法
        const oldResult = await this.furnitureManager.createDualRoomSetup();
        console.log(`旧方法 createDualRoomSetup: ${oldResult.success ? "成功" : "失败"}`);
        console.log(`旧方法消息: ${oldResult.message}`);

        // 测试新方法的等效调用
        const newResult = await this.furnitureManager.createRoomSetup({ mode: 'dual' });
        console.log(`新方法等效调用: ${newResult.success ? "成功" : "失败"}`);
        console.log(`新方法消息: ${newResult.message}`);

        // 比较结果
        const oldRoomCount = oldResult.roomIds?.length || 0;
        const newRoomCount = newResult.roomIds?.length || 0;
        console.log(`房间数量对比: 旧方法=${oldRoomCount}, 新方法=${newRoomCount}`);
        console.log(`向后兼容性: ${oldRoomCount === newRoomCount ? "通过" : "失败"}`);
    }

    /**
     * 测试参数组合
     */
    private async testParameterCombinations() {
        console.log("--- 测试参数组合 ---");
        
        if (!this.furnitureManager) return;

        // 测试所有参数的组合
        const combinations = [
            { mode: 'single' as const, useRecommended: true },
            { mode: 'single' as const, useRecommended: false },
            { mode: 'dual' as const, useRecommended: true },
            { mode: 'dual' as const, useRecommended: false },
            { mode: 'multi' as const, useRecommended: true },
            { mode: 'multi' as const, useRecommended: false }
        ];

        for (const combo of combinations) {
            const result = await this.furnitureManager.createRoomSetup(combo);
            console.log(`${combo.mode}模式(推荐:${combo.useRecommended}): ${result.success ? "成功" : "失败"}`);
        }
    }

    /**
     * 手动触发完整测试
     */
    public async runFullTest() {
        await this.start();
        await this.testParameterCombinations();
    }

    /**
     * 清理测试数据
     */
    public cleanupTestData() {
        if (!this.furnitureManager) return;

        console.log("清理测试数据...");
        
        // 清空所有可能的房间
        const possibleRoomIds = ["room_a", "room_b", "large_room", "small_room"];
        for (const roomId of possibleRoomIds) {
            const cleared = this.furnitureManager.clearRoom(roomId);
            if (cleared) {
                console.log(`清空房间 ${roomId}: 成功`);
            }
        }
    }
}
