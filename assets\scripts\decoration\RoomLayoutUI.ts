/**
 * 房间布局管理UI
 * 提供保存/加载布局的用户界面
 */

import { Component, Label, Button, EditBox, ScrollView, Prefab, instantiate, Color, _decorator } from "cc";
import { RoomLayoutManager, LayoutOperationResult } from "./RoomLayoutManager";
import { SavedRoomLayout, SortOption } from "./RoomLayoutStorage";
import { FurnitureTheme } from "./DecorationDefine";
import { RoomLayoutItem } from "./RoomLayoutItem";

const { ccclass, property } = _decorator;

@ccclass('RoomLayoutUI')
export class RoomLayoutUI extends Component {
    @property({ type: EditBox, tooltip: "布局名称输入框" })
    layoutNameInput: EditBox | null = null;

    @property({ type: EditBox, tooltip: "布局描述输入框" })
    layoutDescriptionInput: EditBox | null = null;

    @property({ type: EditBox, tooltip: "标签输入框" })
    tagsInput: EditBox | null = null;

    @property({ type: Button, tooltip: "快速保存按钮" })
    quickSaveButton: Button | null = null;

    @property({ type: Button, tooltip: "保存按钮" })
    saveButton: Button | null = null;

    @property({ type: Button, tooltip: "刷新列表按钮" })
    refreshButton: Button | null = null;

    @property({ type: Button, tooltip: "清理存储按钮" })
    cleanupButton: Button | null = null;

    @property({ type: ScrollView, tooltip: "布局列表滚动视图" })
    layoutListScrollView: ScrollView | null = null;

    @property({ type: Prefab, tooltip: "布局项预制体" })
    layoutItemPrefab: Prefab | null = null;

    @property({ type: Label, tooltip: "状态显示标签" })
    statusLabel: Label | null = null;

    @property({ type: Label, tooltip: "统计信息标签" })
    statsLabel: Label | null = null;

    @property({ type: RoomLayoutManager, tooltip: "布局管理器" })
    layoutManager: RoomLayoutManager | null = null;

    private currentLayouts: SavedRoomLayout[] = [];

    protected onLoad() {
        this.setupEventListeners();
        this.setupLayoutManagerCallbacks();
    }

    protected start() {
        this.refreshLayoutList();
        this.updateStats();
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        this.quickSaveButton?.node.on(Button.EventType.CLICK, this.onQuickSave, this);
        this.saveButton?.node.on(Button.EventType.CLICK, this.onSave, this);
        this.refreshButton?.node.on(Button.EventType.CLICK, this.onRefresh, this);
        this.cleanupButton?.node.on(Button.EventType.CLICK, this.onCleanup, this);
    }

    /**
     * 设置布局管理器回调
     */
    private setupLayoutManagerCallbacks(): void {
        if (!this.layoutManager) return;

        this.layoutManager.setOnLayoutSaved((result: LayoutOperationResult) => {
            this.showStatus(result.message, result.success);
            if (result.success) {
                this.refreshLayoutList();
                this.clearInputs();
            }
        });

        this.layoutManager.setOnLayoutLoaded((result: LayoutOperationResult) => {
            this.showStatus(result.message, result.success);
        });

        this.layoutManager.setOnLayoutDeleted((_layoutId: string, success: boolean) => {
            this.showStatus(
                success ? "布局删除成功" : "布局删除失败",
                success
            );
            if (success) {
                this.refreshLayoutList();
            }
        });
    }

    /**
     * 快速保存按钮点击
     */
    private async onQuickSave(): Promise<void> {
        if (!this.layoutManager) return;

        this.showStatus("正在快速保存...", true);
        
        await this.layoutManager.quickSave(undefined, {
            autoGenerateName: true,
            includeScore: true,
            includeThumbnail: false
        });
    }

    /**
     * 保存按钮点击
     */
    private async onSave(): Promise<void> {
        if (!this.layoutManager) return;

        const name = this.layoutNameInput?.string.trim();
        if (!name) {
            this.showStatus("请输入布局名称", false);
            return;
        }

        const tagsString = this.tagsInput?.string.trim();
        const tags = tagsString ? tagsString.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        this.showStatus("正在保存布局...", true);

        await this.layoutManager.quickSave(name, {
            includeScore: true,
            includeThumbnail: false,
            tags
        });
    }

    /**
     * 刷新按钮点击
     */
    private onRefresh(): void {
        this.refreshLayoutList();
        this.updateStats();
    }

    /**
     * 清理按钮点击
     */
    private async onCleanup(): Promise<void> {
        if (!this.layoutManager) return;

        this.showStatus("正在清理存储空间...", true);

        const removedCount = await this.layoutManager.cleanupStorage({
            removeAutoSaves: true,
            removeOldLayouts: 30, // 删除30天前的布局
            maxLayouts: 50 // 最多保留50个布局
        });

        this.showStatus(`清理完成，删除了 ${removedCount} 个布局`, true);
        this.refreshLayoutList();
        this.updateStats();
    }

    /**
     * 刷新布局列表
     */
    private async refreshLayoutList(): Promise<void> {
        if (!this.layoutManager || !this.layoutListScrollView) return;

        try {
            // 获取所有布局，按修改时间排序
            this.currentLayouts = await this.layoutManager.searchLayouts(
                undefined,
                SortOption.ModifiedTimeDesc
            );

            this.updateLayoutList();
        } catch (error) {
            console.error("刷新布局列表失败:", error);
            this.showStatus("刷新列表失败", false);
        }
    }

    /**
     * 更新布局列表显示
     */
    private updateLayoutList(): void {
        if (!this.layoutListScrollView || !this.layoutItemPrefab) return;

        const content = this.layoutListScrollView.content;
        if (!content) return;

        // 清空现有项目
        content.removeAllChildren();

        // 创建布局项目
        this.currentLayouts.forEach((layout) => {
            const itemNode = instantiate(this.layoutItemPrefab!);
            const itemComponent = itemNode.getComponent(RoomLayoutItem);
            
            if (itemComponent) {
                itemComponent.setup(layout, this);
            }

            content.addChild(itemNode);
        });
    }

    /**
     * 更新统计信息
     */
    private async updateStats(): Promise<void> {
        if (!this.layoutManager || !this.statsLabel) return;

        try {
            const stats = await this.layoutManager.getStorageStats();
            
            const statsText = [
                `总布局: ${stats.total}`,
                `用户保存: ${stats.byCategory.user_saved || 0}`,
                `自动保存: ${stats.byCategory.auto_save || 0}`,
                `平均评分: ${stats.averageScore.toFixed(1)}`,
                `存储大小: ${(stats.totalSize / 1024).toFixed(1)}KB`
            ].join(' | ');

            this.statsLabel.string = statsText;
        } catch (error) {
            console.error("更新统计信息失败:", error);
        }
    }

    /**
     * 显示状态信息
     */
    private showStatus(message: string, isSuccess: boolean): void {
        if (!this.statusLabel) return;

        this.statusLabel.string = message;
        this.statusLabel.color = isSuccess ?
            new Color(76, 175, 80, 255) :  // 绿色
            new Color(244, 67, 54, 255);   // 红色

        // 3秒后清空状态
        setTimeout(() => {
            if (this.statusLabel) {
                this.statusLabel.string = "";
            }
        }, 3000);
    }

    /**
     * 清空输入框
     */
    private clearInputs(): void {
        if (this.layoutNameInput) {
            this.layoutNameInput.string = "";
        }
        if (this.layoutDescriptionInput) {
            this.layoutDescriptionInput.string = "";
        }
        if (this.tagsInput) {
            this.tagsInput.string = "";
        }
    }

    /**
     * 加载指定布局
     */
    async loadLayout(layoutId: string): Promise<void> {
        if (!this.layoutManager) return;

        this.showStatus("正在加载布局...", true);

        await this.layoutManager.loadLayout(layoutId);
    }

    /**
     * 删除指定布局
     */
    async deleteLayout(layoutId: string): Promise<void> {
        if (!this.layoutManager) return;

        // 这里可以添加确认对话框
        await this.layoutManager.deleteLayout(layoutId);
    }

    /**
     * 复制指定布局
     */
    async duplicateLayout(layoutId: string): Promise<void> {
        if (!this.layoutManager) return;

        this.showStatus("正在复制布局...", true);
        await this.layoutManager.duplicateLayout(layoutId);
    }

    /**
     * 导出指定布局
     */
    async exportLayout(layoutId: string): Promise<void> {
        if (!this.layoutManager) return;

        try {
            const jsonData = await this.layoutManager.exportLayout(layoutId);
            if (jsonData) {
                // 这里可以实现文件下载或复制到剪贴板
                console.log("导出的布局数据:", jsonData);
                this.showStatus("布局已导出到控制台", true);
            } else {
                this.showStatus("导出失败", false);
            }
        } catch (error) {
            console.error("导出布局失败:", error);
            this.showStatus("导出失败", false);
        }
    }

    /**
     * 导入布局
     */
    async importLayout(jsonData: string): Promise<void> {
        if (!this.layoutManager) return;

        this.showStatus("正在导入布局...", true);
        const result = await this.layoutManager.importLayout(jsonData);
        
        if (result.success) {
            this.refreshLayoutList();
        }
    }

    /**
     * 按主题筛选布局
     */
    async filterByTheme(theme: FurnitureTheme): Promise<void> {
        if (!this.layoutManager) return;

        try {
            this.currentLayouts = await this.layoutManager.getLayoutsByTheme(theme);
            this.updateLayoutList();
            this.showStatus(`已筛选 ${this.getThemeName(theme)} 主题布局`, true);
        } catch (error) {
            console.error("筛选布局失败:", error);
            this.showStatus("筛选失败", false);
        }
    }

    /**
     * 显示高分布局
     */
    async showTopScoredLayouts(): Promise<void> {
        if (!this.layoutManager) return;

        try {
            this.currentLayouts = await this.layoutManager.getTopScoredLayouts(20);
            this.updateLayoutList();
            this.showStatus("已显示高分布局", true);
        } catch (error) {
            console.error("获取高分布局失败:", error);
            this.showStatus("获取失败", false);
        }
    }

    /**
     * 获取主题名称
     */
    private getThemeName(theme: FurnitureTheme): string {
        const themeNames: { [key: number]: string } = {
            1: "现代风格",
            2: "古典风格",
            3: "自然风格",
            4: "工业风格",
            5: "简约风格"
        };
        return themeNames[theme] || "未知风格";
    }
}


