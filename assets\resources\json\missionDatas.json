[{"id": 1001, "type": 1, "title": "通关3次", "desc": "通关3次", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "completeStage", "args": []}, "target": 3, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 500, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 1002, "type": 1, "title": "扫荡5次", "desc": "扫荡5次", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "sweep", "args": []}, "target": 5, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 500, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 1003, "type": 1, "title": "合成5级装备4次", "desc": "合成5级装备4次", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "composeEquipment", "args": ["", 5]}, "target": 4, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 500, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 1004, "type": 1, "title": "开高级宝箱2次", "desc": "开高级宝箱2次", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "openAdvancedChest", "args": []}, "target": 2, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 500, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 1005, "type": 1, "title": "观看广告10次", "desc": "观看广告10次", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 10, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 500, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 1, "type": 2, "title": "通过第1关", "desc": "通过第1关", "order": 0, "preIds": [], "nextIds": [2, 3002, 3006], "targets": [{"tag": {"key": "completeStage", "args": [1]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 1000, "diamond": 0, "exp": 0, "resources": [], "items": [{"itemId": 1001, "amount": 5}], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 19, "args": ["CardActivated", 3]}]}, {"id": 2, "type": 2, "title": "通过第2关", "desc": "通过第2关", "order": 0, "preIds": [1], "nextIds": [3, 3003], "targets": [{"tag": {"key": "completeStage", "args": [2]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [4], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 3, "type": 2, "title": "通过第3关", "desc": "通过第3关", "order": 0, "preIds": [2], "nextIds": [4, 3004], "targets": [{"tag": {"key": "completeStage", "args": [3]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [{"itemId": 1016, "amount": 5}], "card": [], "equipment": [19], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 4, "type": 2, "title": "通过第4关", "desc": "通过第4关", "order": 0, "preIds": [3], "nextIds": [5], "targets": [{"tag": {"key": "completeStage", "args": [4]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 5, "type": 2, "title": "通过第5关", "desc": "通过第5关", "order": 0, "preIds": [4], "nextIds": [6], "targets": [{"tag": {"key": "completeStage", "args": [5]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [7], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 6, "type": 2, "title": "通过第6关", "desc": "通过第6关", "order": 0, "preIds": [5], "nextIds": [7], "targets": [{"tag": {"key": "completeStage", "args": [6]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [20], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 7, "type": 2, "title": "通过第7关", "desc": "通过第7关", "order": 0, "preIds": [6], "nextIds": [8], "targets": [{"tag": {"key": "completeStage", "args": [7]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 8, "type": 2, "title": "通过第8关", "desc": "通过第8关", "order": 0, "preIds": [7], "nextIds": [9], "targets": [{"tag": {"key": "completeStage", "args": [8]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [18], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 9, "type": 2, "title": "通过第9关", "desc": "通过第9关", "order": 0, "preIds": [8], "nextIds": [10], "targets": [{"tag": {"key": "completeStage", "args": [9]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 10, "type": 2, "title": "通过第10关", "desc": "通过第10关", "order": 0, "preIds": [9], "nextIds": [11], "targets": [{"tag": {"key": "completeStage", "args": [10]}, "target": 1, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 0, "exp": 0, "resources": [], "items": [], "card": [6], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 11, "type": 2, "title": "通过第11关", "desc": "通过第11关", "order": 0, "preIds": [10], "nextIds": [12], "targets": [{"tag": {"key": "completeStage", "args": [11]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 12, "type": 2, "title": "通过第12关", "desc": "通过第12关", "order": 0, "preIds": [11], "nextIds": [13], "targets": [{"tag": {"key": "completeStage", "args": [12]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 13, "type": 2, "title": "通过第13关", "desc": "通过第13关", "order": 0, "preIds": [12], "nextIds": [14], "targets": [{"tag": {"key": "completeStage", "args": [13]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 14, "type": 2, "title": "通过第14关", "desc": "通过第14关", "order": 0, "preIds": [13], "nextIds": [15], "targets": [{"tag": {"key": "completeStage", "args": [14]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 15, "type": 2, "title": "通过第15关", "desc": "通过第15关", "order": 0, "preIds": [14], "nextIds": [16], "targets": [{"tag": {"key": "completeStage", "args": [15]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 16, "type": 2, "title": "通过第16关", "desc": "通过第16关", "order": 0, "preIds": [15], "nextIds": [17], "targets": [{"tag": {"key": "completeStage", "args": [16]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 17, "type": 2, "title": "通过第17关", "desc": "通过第17关", "order": 0, "preIds": [16], "nextIds": [18], "targets": [{"tag": {"key": "completeStage", "args": [17]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 18, "type": 2, "title": "通过第18关", "desc": "通过第18关", "order": 0, "preIds": [17], "nextIds": [19], "targets": [{"tag": {"key": "completeStage", "args": [18]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 19, "type": 2, "title": "通过第19关", "desc": "通过第19关", "order": 0, "preIds": [18], "nextIds": [20], "targets": [{"tag": {"key": "completeStage", "args": [19]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 20, "type": 2, "title": "通过第20关", "desc": "通过第20关", "order": 0, "preIds": [19], "nextIds": [21], "targets": [{"tag": {"key": "completeStage", "args": [20]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 21, "type": 2, "title": "通过第21关", "desc": "通过第21关", "order": 0, "preIds": [20], "nextIds": [22], "targets": [{"tag": {"key": "completeStage", "args": [21]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 22, "type": 2, "title": "通过第22关", "desc": "通过第22关", "order": 0, "preIds": [21], "nextIds": [23], "targets": [{"tag": {"key": "completeStage", "args": [22]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 23, "type": 2, "title": "通过第23关", "desc": "通过第23关", "order": 0, "preIds": [22], "nextIds": [24], "targets": [{"tag": {"key": "completeStage", "args": [23]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 24, "type": 2, "title": "通过第24关", "desc": "通过第24关", "order": 0, "preIds": [23], "nextIds": [25], "targets": [{"tag": {"key": "completeStage", "args": [24]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 25, "type": 2, "title": "通过第25关", "desc": "通过第25关", "order": 0, "preIds": [24], "nextIds": [], "targets": [{"tag": {"key": "completeStage", "args": [25]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2001, "type": 7, "title": "累计看5个广告", "desc": "累计看5个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 5, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 80, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2002, "type": 7, "title": "累计看10个广告", "desc": "累计看10个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 10, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 120, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2003, "type": 7, "title": "累计看15个广告", "desc": "累计看15个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 15, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2004, "type": 7, "title": "累计看20个广告", "desc": "累计看20个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 20, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2005, "type": 7, "title": "累计看25个广告", "desc": "累计看25个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 25, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 80, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2006, "type": 7, "title": "累计看30个广告", "desc": "累计看30个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 30, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 120, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2007, "type": 7, "title": "累计看35个广告", "desc": "累计看35个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 35, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2008, "type": 7, "title": "累计看40个广告", "desc": "累计看40个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 40, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2009, "type": 7, "title": "累计看45个广告", "desc": "累计看45个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 45, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 80, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2010, "type": 7, "title": "累计看50个广告", "desc": "累计看50个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 50, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 120, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2011, "type": 7, "title": "累计看55个广告", "desc": "累计看55个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 55, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2012, "type": 7, "title": "累计看60个广告", "desc": "累计看60个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 60, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2013, "type": 7, "title": "累计看65个广告", "desc": "累计看65个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 65, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 80, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2014, "type": 7, "title": "累计看70个广告", "desc": "累计看70个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 70, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 120, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2015, "type": 7, "title": "累计看75个广告", "desc": "累计看75个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 75, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2016, "type": 7, "title": "累计看80个广告", "desc": "累计看80个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 80, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2017, "type": 7, "title": "累计看85个广告", "desc": "累计看85个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 85, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 80, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2018, "type": 7, "title": "累计看90个广告", "desc": "累计看90个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 90, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 120, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2019, "type": 7, "title": "累计看95个广告", "desc": "累计看95个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 95, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2020, "type": 7, "title": "累计看100个广告", "desc": "累计看100个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 100, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2021, "type": 7, "title": "累计看110个广告", "desc": "累计看110个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 110, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2022, "type": 7, "title": "累计看120个广告", "desc": "累计看120个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 120, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2023, "type": 7, "title": "累计看130个广告", "desc": "累计看130个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 130, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2024, "type": 7, "title": "累计看140个广告", "desc": "累计看140个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 140, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2025, "type": 7, "title": "累计看150个广告", "desc": "累计看150个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 150, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2026, "type": 7, "title": "累计看160个广告", "desc": "累计看160个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 160, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2027, "type": 7, "title": "累计看170个广告", "desc": "累计看170个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 170, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2028, "type": 7, "title": "累计看180个广告", "desc": "累计看180个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 180, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2029, "type": 7, "title": "累计看190个广告", "desc": "累计看190个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 190, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2030, "type": 7, "title": "累计看200个广告", "desc": "累计看200个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 200, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2031, "type": 7, "title": "累计看210个广告", "desc": "累计看210个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 210, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2032, "type": 7, "title": "累计看220个广告", "desc": "累计看220个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 220, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2033, "type": 7, "title": "累计看230个广告", "desc": "累计看230个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 230, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2034, "type": 7, "title": "累计看240个广告", "desc": "累计看240个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 240, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2035, "type": 7, "title": "累计看250个广告", "desc": "累计看250个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 250, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2036, "type": 7, "title": "累计看260个广告", "desc": "累计看260个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 260, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2037, "type": 7, "title": "累计看270个广告", "desc": "累计看270个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 270, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2038, "type": 7, "title": "累计看280个广告", "desc": "累计看280个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 280, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2039, "type": 7, "title": "累计看290个广告", "desc": "累计看290个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 290, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 100, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2040, "type": 7, "title": "累计看300个广告", "desc": "累计看300个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 300, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2041, "type": 7, "title": "累计看320个广告", "desc": "累计看320个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 320, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2042, "type": 7, "title": "累计看340个广告", "desc": "累计看340个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 340, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2043, "type": 7, "title": "累计看360个广告", "desc": "累计看360个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 360, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2044, "type": 7, "title": "累计看380个广告", "desc": "累计看380个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 380, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2045, "type": 7, "title": "累计看400个广告", "desc": "累计看400个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 400, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2046, "type": 7, "title": "累计看420个广告", "desc": "累计看420个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 420, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2047, "type": 7, "title": "累计看440个广告", "desc": "累计看440个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 440, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2048, "type": 7, "title": "累计看460个广告", "desc": "累计看460个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 460, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2049, "type": 7, "title": "累计看480个广告", "desc": "累计看480个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 480, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2050, "type": 7, "title": "累计看500个广告", "desc": "累计看500个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 500, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2051, "type": 7, "title": "累计看540个广告", "desc": "累计看540个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 540, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2052, "type": 7, "title": "累计看580个广告", "desc": "累计看580个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 580, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2053, "type": 7, "title": "累计看620个广告", "desc": "累计看620个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 620, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2054, "type": 7, "title": "累计看660个广告", "desc": "累计看660个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 660, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2055, "type": 7, "title": "累计看700个广告", "desc": "累计看700个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 700, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2056, "type": 7, "title": "累计看740个广告", "desc": "累计看740个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 740, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2057, "type": 7, "title": "累计看780个广告", "desc": "累计看780个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 780, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2058, "type": 7, "title": "累计看820个广告", "desc": "累计看820个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 820, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2059, "type": 7, "title": "累计看860个广告", "desc": "累计看860个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 860, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 2060, "type": 7, "title": "累计看900个广告", "desc": "累计看900个广告", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "watchAD", "args": []}, "target": 900, "persistent": false, "condition": false}], "rewards": [{"name": "<PERSON><PERSON>", "type": 0, "money": 0, "diamond": 150, "exp": 0, "resources": [], "items": [], "card": [], "equipment": [], "equipBox": 0, "shopItem": [], "energy": 0, "rEquipPieces": 0, "srEquipPieces": 0, "ssrEquipPieces": 0}], "autoFinish": false, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": []}, {"id": 3001, "type": 2, "title": "开始第1关", "desc": "开始第1关", "order": 0, "preIds": [], "nextIds": [3005], "targets": [{"tag": {"key": "startStage", "args": [1]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 3, "args": ["第1关第1波开始前"]}]}, {"id": 3002, "type": 2, "title": "通过第1关", "desc": "通过第1关", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "completeStage", "args": [1]}, "target": 1, "persistent": true, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 3, "args": ["完成第1关"]}]}, {"id": 3003, "type": 2, "title": "通过第2关", "desc": "通过第2关", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "completeStage", "args": [2]}, "target": 1, "persistent": true, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 3, "args": ["完成第2关"]}]}, {"id": 3004, "type": 2, "title": "通过第3关", "desc": "通过第3关", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "completeStage", "args": [3]}, "target": 1, "persistent": true, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 3, "args": ["完成第3关"]}]}, {"id": 3005, "type": 2, "title": "通过第1关第5波", "desc": "通过第1关第5波", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "finishRound", "args": [1, 5]}, "target": 1, "persistent": true, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 19, "args": ["CardActivated", 2]}, {"type": 3, "args": ["第1关第6波开始前"]}]}, {"id": 3006, "type": 2, "title": "开始第2关", "desc": "开始第2关", "order": 0, "preIds": [], "nextIds": [], "targets": [{"tag": {"key": "startStage", "args": [2]}, "target": 1, "persistent": false, "condition": false}], "rewards": [], "autoFinish": true, "playGuideActionOnTrack": false, "goAction": [], "guideAction": [], "endAction": [{"type": 3, "args": ["第2关第1波开始前"]}]}]