import { _decorator, Component, EventTouch, instantiate, Node, Vec3 } from 'cc';
import { UI } from '../../common';
const { ccclass, property } = _decorator;

@ccclass('RoomComp')
export class RoomComp extends Component {
    @property({ type: Node, displayName: "", tooltip: "" })
    public floorNode!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public jiaju1x1!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public jiaju1x2!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public jiaju2x2!: Node;

    start() {
        this.creatreFloor(10);

        this.addEvents();
    }

    addEvents() {
        this.jiaju1x1.on(Node.EventType.TOUCH_START, this.moveStart, this);
    }

    private moveTarget!: Node;
    private initPos: Vec3 = new Vec3(0, 0, 0);
    private moveStart(evt: EventTouch) {
        this.moveTarget = evt.currentTarget;
        this.initPos.set(this.moveTarget.worldPosition);
        this.node.on(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.on(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

    private move(evt: EventTouch) {
    
    }

    private moveEnd() {
        this.node.off(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.off(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

    //创建地板(正方形)
    /** 
     * @param grid 边缘格子数量 总共将创建出 grid * grid 个格子
     */
    creatreFloor(grid: number) {
        UI.loadPrefab("prefab/room/房间地板砖块").then((prefab) => {
            for (let i = 0; i < grid; i++) {
                for (let j = 0; j < grid; j++) {
                    let node = instantiate(prefab);
                    node.parent = this.floorNode;
                    node.setPosition(i * 65, j * 65);
                }
            }
        })
    }
}


