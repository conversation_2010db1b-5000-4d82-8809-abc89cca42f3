import { _decorator, Component, EventTouch, instantiate, Node, Vec3 } from 'cc';
import { UI } from '../../common';
import { DecorationMgr } from '../logic/DecorationMgr';
import { DefaultRoomTemplates } from '../logic/DefaultRoomTemplates';
import { RoomTemplate } from '../logic/DecorationDefine';
const { ccclass, property } = _decorator;

@ccclass('RoomComp')
export class RoomComp extends Component {
    @property({ type: Node, displayName: "", tooltip: "" })
    public floorNode!: Node;


    private roomA!: RoomTemplate;
    private roomB!: RoomTemplate;
    start() {
        this.addEvents();
        DecorationMgr.instance.init();
        //创建房间  
        this.roomA = DefaultRoomTemplates.getAllTemplates()[0];
        this.roomB = DefaultRoomTemplates.getAllTemplates()[1];
        //放置家具
    }

    addEvents() {

    }

    private moveTarget!: Node;
    private initPos: Vec3 = new Vec3(0, 0, 0);
    private moveStart(evt: EventTouch) {
        this.moveTarget = evt.currentTarget;
        this.initPos.set(this.moveTarget.worldPosition);
        this.node.on(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.on(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

    private move(evt: EventTouch) {

    }

    private moveEnd() {
        this.node.off(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.off(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

}


