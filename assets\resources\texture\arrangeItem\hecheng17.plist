<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>合成用-护目镜.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,80}</string>
                <key>spriteSourceSize</key>
                <string>{302,80}</string>
                <key>textureRect</key>
                <string>{{1,1},{302,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-护目镜2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,80}</string>
                <key>spriteSourceSize</key>
                <string>{302,80}</string>
                <key>textureRect</key>
                <string>{{305,1},{302,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-护目镜3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,80}</string>
                <key>spriteSourceSize</key>
                <string>{302,80}</string>
                <key>textureRect</key>
                <string>{{609,1},{302,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-护目镜4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,80}</string>
                <key>spriteSourceSize</key>
                <string>{302,80}</string>
                <key>textureRect</key>
                <string>{{913,1},{302,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>合成用-护目镜5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{302,80}</string>
                <key>spriteSourceSize</key>
                <string>{302,80}</string>
                <key>textureRect</key>
                <string>{{1217,1},{302,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>hecheng17.png</string>
            <key>size</key>
            <string>{1520,82}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:e826b07aa2d0f4ced1d5d693122bf5b7:a7ce2d93b3e74e27e69724d6d7d65125:25cbc4372aaedaa11540159bc1fcea70$</string>
            <key>textureFileName</key>
            <string>hecheng17.png</string>
        </dict>
    </dict>
</plist>
