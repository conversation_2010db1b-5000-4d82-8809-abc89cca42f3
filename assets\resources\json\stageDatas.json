[{"id": 1, "name": "浅雪土层", "type": "主线", "caveType": 1, "order": 1, "round": [{"id": 4, "depth": 20, "difficulty": 0.6}, {"id": 4, "depth": 40, "difficulty": 0.97}, {"id": 4, "depth": 62, "difficulty": 1.36}, {"id": 4, "depth": 85, "difficulty": 1.76}, {"id": 4, "depth": 110, "difficulty": 2.17}, {"id": 1, "depth": 135, "difficulty": 2.6}, {"id": 1, "depth": 160, "difficulty": 3.04}, {"id": 1, "depth": 185, "difficulty": 3.5}, {"id": 1, "depth": 210, "difficulty": 3.97}, {"id": 1001, "depth": 235, "difficulty": 4.46}], "finishReward": {"money": 220, "rPieces": 6, "srPieces": 2, "ssrPieces": 0}, "roundReward": {"4": {"money": 220, "rPieces": 6, "srPieces": 2, "ssrPieces": 0}, "7": {"money": 220, "rPieces": 6, "srPieces": 2, "ssrPieces": 0}, "10": {"money": 220, "rPieces": 6, "srPieces": 2, "ssrPieces": 0}}, "targetScore": 5000}, {"id": 2, "name": "灰雪土层", "type": "主线", "caveType": 1, "order": 2, "round": [{"id": 1, "depth": 20, "difficulty": 0.78}, {"id": 1, "depth": 40, "difficulty": 1.26}, {"id": 1, "depth": 62, "difficulty": 1.77}, {"id": 1, "depth": 85, "difficulty": 2.29}, {"id": 1, "depth": 110, "difficulty": 2.82}, {"id": 1, "depth": 135, "difficulty": 3.38}, {"id": 1, "depth": 160, "difficulty": 3.95}, {"id": 1, "depth": 185, "difficulty": 4.55}, {"id": 1, "depth": 210, "difficulty": 5.16}, {"id": 1001, "depth": 235, "difficulty": 5.8}, {"id": 1, "depth": 260, "difficulty": 6.45}, {"id": 1, "depth": 285, "difficulty": 7.13}, {"id": 1, "depth": 310, "difficulty": 8.03}, {"id": 1, "depth": 335, "difficulty": 8.95}, {"id": 1001, "depth": 360, "difficulty": 9.92}], "finishReward": {"money": 240, "rPieces": 7, "srPieces": 2, "ssrPieces": 0}, "roundReward": {"5": {"money": 240, "rPieces": 7, "srPieces": 2, "ssrPieces": 0}, "10": {"money": 240, "rPieces": 7, "srPieces": 2, "ssrPieces": 0}, "15": {"money": 240, "rPieces": 7, "srPieces": 2, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 3, "name": "深雪土层", "type": "主线", "caveType": 1, "order": 3, "round": [{"id": 1, "depth": 20, "difficulty": 1.02}, {"id": 1, "depth": 40, "difficulty": 1.65}, {"id": 1, "depth": 62, "difficulty": 2.32}, {"id": 1, "depth": 85, "difficulty": 2.99}, {"id": 1, "depth": 110, "difficulty": 3.69}, {"id": 1, "depth": 135, "difficulty": 4.42}, {"id": 1, "depth": 160, "difficulty": 5.17}, {"id": 1, "depth": 185, "difficulty": 5.95}, {"id": 1, "depth": 210, "difficulty": 6.75}, {"id": 1001, "depth": 235, "difficulty": 7.58}, {"id": 1, "depth": 260, "difficulty": 8.44}, {"id": 1, "depth": 285, "difficulty": 9.32}, {"id": 1, "depth": 310, "difficulty": 10.5}, {"id": 1, "depth": 335, "difficulty": 11.71}, {"id": 1001, "depth": 360, "difficulty": 12.97}], "finishReward": {"money": 260, "rPieces": 8, "srPieces": 2, "ssrPieces": 0}, "roundReward": {"5": {"money": 260, "rPieces": 8, "srPieces": 2, "ssrPieces": 0}, "10": {"money": 260, "rPieces": 8, "srPieces": 2, "ssrPieces": 0}, "15": {"money": 260, "rPieces": 8, "srPieces": 2, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 4, "name": "地下河层", "type": "主线", "caveType": 3, "order": 4, "round": [{"id": 2, "depth": 20, "difficulty": 1.3}, {"id": 2, "depth": 40, "difficulty": 2.11}, {"id": 2, "depth": 62, "difficulty": 2.95}, {"id": 2, "depth": 85, "difficulty": 3.81}, {"id": 2, "depth": 110, "difficulty": 4.71}, {"id": 2, "depth": 135, "difficulty": 5.63}, {"id": 2, "depth": 160, "difficulty": 6.59}, {"id": 2, "depth": 185, "difficulty": 7.58}, {"id": 2, "depth": 210, "difficulty": 8.61}, {"id": 1002, "depth": 235, "difficulty": 9.66}, {"id": 2, "depth": 260, "difficulty": 10.75}, {"id": 2, "depth": 285, "difficulty": 11.88}, {"id": 2, "depth": 310, "difficulty": 13.38}, {"id": 2, "depth": 335, "difficulty": 14.92}, {"id": 1002, "depth": 360, "difficulty": 16.54}], "finishReward": {"money": 280, "rPieces": 9, "srPieces": 3, "ssrPieces": 0}, "roundReward": {"5": {"money": 280, "rPieces": 9, "srPieces": 3, "ssrPieces": 0}, "10": {"money": 280, "rPieces": 9, "srPieces": 3, "ssrPieces": 0}, "15": {"money": 280, "rPieces": 9, "srPieces": 3, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 5, "name": "地下廊道", "type": "主线", "caveType": 3, "order": 5, "round": [{"id": 2, "depth": 20, "difficulty": 1.5}, {"id": 2, "depth": 40, "difficulty": 2.43}, {"id": 2, "depth": 62, "difficulty": 3.41}, {"id": 2, "depth": 85, "difficulty": 4.4}, {"id": 2, "depth": 110, "difficulty": 5.43}, {"id": 2, "depth": 135, "difficulty": 6.5}, {"id": 2, "depth": 160, "difficulty": 7.61}, {"id": 2, "depth": 185, "difficulty": 8.75}, {"id": 2, "depth": 210, "difficulty": 9.93}, {"id": 1002, "depth": 235, "difficulty": 11.14}, {"id": 2, "depth": 260, "difficulty": 12.4}, {"id": 2, "depth": 285, "difficulty": 13.71}, {"id": 2, "depth": 310, "difficulty": 15.43}, {"id": 2, "depth": 335, "difficulty": 17.22}, {"id": 1002, "depth": 360, "difficulty": 19.08}], "finishReward": {"money": 300, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "roundReward": {"5": {"money": 300, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "10": {"money": 300, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "15": {"money": 300, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 6, "name": "深水矿区", "type": "主线", "caveType": 3, "order": 6, "round": [{"id": 2, "depth": 20, "difficulty": 1.73}, {"id": 2, "depth": 40, "difficulty": 2.8}, {"id": 2, "depth": 62, "difficulty": 3.93}, {"id": 2, "depth": 85, "difficulty": 5.07}, {"id": 2, "depth": 110, "difficulty": 6.26}, {"id": 2, "depth": 135, "difficulty": 7.49}, {"id": 2, "depth": 160, "difficulty": 8.77}, {"id": 2, "depth": 185, "difficulty": 10.09}, {"id": 2, "depth": 210, "difficulty": 11.45}, {"id": 1002, "depth": 235, "difficulty": 12.85}, {"id": 2, "depth": 260, "difficulty": 14.31}, {"id": 2, "depth": 285, "difficulty": 15.81}, {"id": 2, "depth": 310, "difficulty": 17.8}, {"id": 2, "depth": 335, "difficulty": 19.86}, {"id": 1002, "depth": 360, "difficulty": 22.01}], "finishReward": {"money": 310, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "roundReward": {"5": {"money": 310, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "10": {"money": 310, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}, "15": {"money": 310, "rPieces": 10, "srPieces": 3, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 7, "name": "深水溶洞", "type": "主线", "caveType": 3, "order": 7, "round": [{"id": 2, "depth": 20, "difficulty": 1.99}, {"id": 2, "depth": 40, "difficulty": 3.22}, {"id": 2, "depth": 62, "difficulty": 4.52}, {"id": 2, "depth": 85, "difficulty": 5.83}, {"id": 2, "depth": 110, "difficulty": 7.2}, {"id": 2, "depth": 135, "difficulty": 8.62}, {"id": 2, "depth": 160, "difficulty": 10.09}, {"id": 2, "depth": 185, "difficulty": 11.6}, {"id": 2, "depth": 210, "difficulty": 13.17}, {"id": 1002, "depth": 235, "difficulty": 14.79}, {"id": 2, "depth": 260, "difficulty": 16.46}, {"id": 2, "depth": 285, "difficulty": 18.19}, {"id": 2, "depth": 310, "difficulty": 20.48}, {"id": 2, "depth": 335, "difficulty": 22.85}, {"id": 1002, "depth": 360, "difficulty": 25.31}], "finishReward": {"money": 320, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "roundReward": {"5": {"money": 320, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "10": {"money": 320, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "15": {"money": 320, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 8, "name": "冻土层", "type": "主线", "caveType": 2, "order": 8, "round": [{"id": 3, "depth": 20, "difficulty": 2.29}, {"id": 3, "depth": 40, "difficulty": 3.71}, {"id": 3, "depth": 62, "difficulty": 5.2}, {"id": 3, "depth": 85, "difficulty": 6.71}, {"id": 3, "depth": 110, "difficulty": 8.29}, {"id": 3, "depth": 135, "difficulty": 9.92}, {"id": 3, "depth": 160, "difficulty": 11.61}, {"id": 3, "depth": 185, "difficulty": 13.35}, {"id": 3, "depth": 210, "difficulty": 15.16}, {"id": 1003, "depth": 235, "difficulty": 17.01}, {"id": 3, "depth": 260, "difficulty": 18.94}, {"id": 3, "depth": 285, "difficulty": 20.93}, {"id": 3, "depth": 310, "difficulty": 23.56}, {"id": 3, "depth": 335, "difficulty": 26.29}, {"id": 1003, "depth": 360, "difficulty": 29.13}], "finishReward": {"money": 330, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "roundReward": {"5": {"money": 330, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "10": {"money": 330, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}, "15": {"money": 330, "rPieces": 11, "srPieces": 3, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 9, "name": "冰天雪地", "type": "主线", "caveType": 2, "order": 9, "round": [{"id": 3, "depth": 20, "difficulty": 2.63}, {"id": 3, "depth": 40, "difficulty": 4.26}, {"id": 3, "depth": 62, "difficulty": 5.97}, {"id": 3, "depth": 85, "difficulty": 7.71}, {"id": 3, "depth": 110, "difficulty": 9.52}, {"id": 3, "depth": 135, "difficulty": 11.39}, {"id": 3, "depth": 160, "difficulty": 13.33}, {"id": 3, "depth": 185, "difficulty": 15.33}, {"id": 3, "depth": 210, "difficulty": 17.41}, {"id": 1003, "depth": 235, "difficulty": 19.54}, {"id": 3, "depth": 260, "difficulty": 21.75}, {"id": 3, "depth": 285, "difficulty": 24.04}, {"id": 3, "depth": 310, "difficulty": 27.06}, {"id": 3, "depth": 335, "difficulty": 30.19}, {"id": 1003, "depth": 360, "difficulty": 33.45}], "finishReward": {"money": 340, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 340, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 340, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 340, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 10, "name": "石冰矿区", "type": "主线", "caveType": 2, "order": 10, "round": [{"id": 3, "depth": 20, "difficulty": 3.02}, {"id": 3, "depth": 40, "difficulty": 4.89}, {"id": 3, "depth": 62, "difficulty": 6.86}, {"id": 3, "depth": 85, "difficulty": 8.85}, {"id": 3, "depth": 110, "difficulty": 10.93}, {"id": 3, "depth": 135, "difficulty": 13.08}, {"id": 3, "depth": 160, "difficulty": 15.31}, {"id": 3, "depth": 185, "difficulty": 17.61}, {"id": 3, "depth": 210, "difficulty": 19.99}, {"id": 1003, "depth": 235, "difficulty": 22.44}, {"id": 3, "depth": 260, "difficulty": 24.98}, {"id": 3, "depth": 285, "difficulty": 27.6}, {"id": 3, "depth": 310, "difficulty": 31.08}, {"id": 3, "depth": 335, "difficulty": 34.67}, {"id": 1003, "depth": 360, "difficulty": 38.41}], "finishReward": {"money": 350, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 350, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 350, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 350, "rPieces": 12, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 11, "name": "冰晶石区", "type": "主线", "caveType": 2, "order": 11, "round": [{"id": 3, "depth": 20, "difficulty": 3.47}, {"id": 3, "depth": 40, "difficulty": 5.62}, {"id": 3, "depth": 62, "difficulty": 7.88}, {"id": 3, "depth": 85, "difficulty": 10.17}, {"id": 3, "depth": 110, "difficulty": 12.56}, {"id": 3, "depth": 135, "difficulty": 15.03}, {"id": 3, "depth": 160, "difficulty": 17.59}, {"id": 3, "depth": 185, "difficulty": 20.23}, {"id": 3, "depth": 210, "difficulty": 22.97}, {"id": 1003, "depth": 235, "difficulty": 25.78}, {"id": 3, "depth": 260, "difficulty": 28.7}, {"id": 3, "depth": 285, "difficulty": 31.72}, {"id": 3, "depth": 310, "difficulty": 35.71}, {"id": 3, "depth": 335, "difficulty": 39.84}, {"id": 1003, "depth": 360, "difficulty": 44.14}], "finishReward": {"money": 360, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 360, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 360, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 360, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 12, "name": "冰之世界", "type": "主线", "caveType": 2, "order": 12, "round": [{"id": 3, "depth": 20, "difficulty": 3.99}, {"id": 3, "depth": 40, "difficulty": 6.46}, {"id": 3, "depth": 62, "difficulty": 9.06}, {"id": 3, "depth": 85, "difficulty": 11.69}, {"id": 3, "depth": 110, "difficulty": 14.44}, {"id": 3, "depth": 135, "difficulty": 17.28}, {"id": 3, "depth": 160, "difficulty": 20.23}, {"id": 3, "depth": 185, "difficulty": 23.26}, {"id": 3, "depth": 210, "difficulty": 26.41}, {"id": 1003, "depth": 235, "difficulty": 29.65}, {"id": 3, "depth": 260, "difficulty": 33}, {"id": 3, "depth": 285, "difficulty": 36.47}, {"id": 3, "depth": 310, "difficulty": 41.06}, {"id": 3, "depth": 335, "difficulty": 45.81}, {"id": 1003, "depth": 360, "difficulty": 50.75}], "finishReward": {"money": 370, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 370, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 370, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 370, "rPieces": 13, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 13, "name": "浅雪土层", "type": "主线", "caveType": 1, "order": 13, "round": [{"id": 1, "depth": 20, "difficulty": 4.59}, {"id": 1, "depth": 40, "difficulty": 7.44}, {"id": 1, "depth": 62, "difficulty": 10.42}, {"id": 1, "depth": 85, "difficulty": 13.45}, {"id": 1, "depth": 110, "difficulty": 16.62}, {"id": 1, "depth": 135, "difficulty": 19.87}, {"id": 1, "depth": 160, "difficulty": 23.27}, {"id": 1, "depth": 185, "difficulty": 26.76}, {"id": 1, "depth": 210, "difficulty": 30.39}, {"id": 1001, "depth": 235, "difficulty": 34.1}, {"id": 1, "depth": 260, "difficulty": 37.96}, {"id": 1, "depth": 285, "difficulty": 41.95}, {"id": 1, "depth": 310, "difficulty": 47.23}, {"id": 1, "depth": 335, "difficulty": 52.69}, {"id": 1001, "depth": 360, "difficulty": 58.38}], "finishReward": {"money": 380, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 380, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 380, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 380, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 14, "name": "灰雪土层", "type": "主线", "caveType": 1, "order": 14, "round": [{"id": 1, "depth": 20, "difficulty": 5.28}, {"id": 1, "depth": 40, "difficulty": 8.55}, {"id": 1, "depth": 62, "difficulty": 11.99}, {"id": 1, "depth": 85, "difficulty": 15.47}, {"id": 1, "depth": 110, "difficulty": 19.11}, {"id": 1, "depth": 135, "difficulty": 22.86}, {"id": 1, "depth": 160, "difficulty": 26.77}, {"id": 1, "depth": 185, "difficulty": 30.78}, {"id": 1, "depth": 210, "difficulty": 34.95}, {"id": 1001, "depth": 235, "difficulty": 39.23}, {"id": 1, "depth": 260, "difficulty": 43.67}, {"id": 1, "depth": 285, "difficulty": 48.26}, {"id": 1, "depth": 310, "difficulty": 54.33}, {"id": 1, "depth": 335, "difficulty": 60.61}, {"id": 1001, "depth": 360, "difficulty": 67.16}], "finishReward": {"money": 390, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "roundReward": {"5": {"money": 390, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "10": {"money": 390, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}, "15": {"money": 390, "rPieces": 14, "srPieces": 4, "ssrPieces": 0}}, "targetScore": 8000}, {"id": 15, "name": "深雪土层", "type": "主线", "caveType": 1, "order": 15, "round": [{"id": 1, "depth": 20, "difficulty": 6.07}, {"id": 1, "depth": 40, "difficulty": 9.83}, {"id": 1, "depth": 62, "difficulty": 13.78}, {"id": 1, "depth": 85, "difficulty": 17.79}, {"id": 1, "depth": 110, "difficulty": 21.97}, {"id": 1, "depth": 135, "difficulty": 26.28}, {"id": 1, "depth": 160, "difficulty": 30.77}, {"id": 1, "depth": 185, "difficulty": 35.39}, {"id": 1, "depth": 210, "difficulty": 40.18}, {"id": 1001, "depth": 235, "difficulty": 45.1}, {"id": 1, "depth": 260, "difficulty": 50.2}, {"id": 1, "depth": 285, "difficulty": 55.48}, {"id": 1, "depth": 310, "difficulty": 62.46}, {"id": 1, "depth": 335, "difficulty": 69.68}, {"id": 1001, "depth": 360, "difficulty": 77.21}], "finishReward": {"money": 400, "rPieces": 15, "srPieces": 5, "ssrPieces": 0}, "roundReward": {"5": {"money": 400, "rPieces": 15, "srPieces": 5, "ssrPieces": 0}, "10": {"money": 400, "rPieces": 15, "srPieces": 5, "ssrPieces": 0}, "15": {"money": 400, "rPieces": 15, "srPieces": 5, "ssrPieces": 0}}, "targetScore": 8000}]