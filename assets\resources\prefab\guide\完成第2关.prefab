[{"__type__": "cc.Prefab", "_name": "完成第2关", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "完成第2关", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "01d4aUjQpZMip0tR3tmI9iX", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "guides": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}, {"__id__": 12}], "waitPre": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43PAxXqfhL54dXlfThQV+l"}, {"__type__": "SceneGuide", "name": "等待页面", "switchType": 2, "type": 9, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 1, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "商店开启了！看看有什么好东西吧。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 800}, "roleName": "胡萝卜卜", "guides": [], "eventTag": "None"}, {"__type__": "SceneGuide", "name": "步骤1引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 7}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击商店", "waitPageId": 1, "targetNodePath": "mainUI/frame_maininterface_1/商店", "force": true, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2对话", "switchType": 2, "type": 12, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "这里有一个免费的宝箱！里面好像有不少装备碎片呢。", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1200}, "roleName": "胡萝卜卜", "guides": [{"__id__": 9}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "", "waitPageId": 0, "targetNodePath": "", "force": false, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2对话", "switchType": 2, "type": 16, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": false, "isSubNode": false, "clearCount": 1, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "点击领取免费宝箱", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 1000}, "roleName": "胡萝卜卜", "guides": [{"__id__": 11}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "", "waitPageId": 26, "targetNodePath": "", "force": false, "nodeGuide": false}, {"__type__": "SceneGuide", "name": "步骤2引导", "switchType": 2, "type": 14, "playSpinePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "args": "", "isWaitAnimation": true, "isSubNode": true, "clearCount": 0, "spineData": null, "animation": "animation", "image": null, "prefab": null, "waitAllSceneGuideContainerPrefab": null, "audioClip": null, "isBgm": false, "loop": false, "volume": 1, "delay": 0, "uiType": 1, "waitPageId": 0, "deletePageId": 0, "waitTime": 0, "dialogContent": "", "dialogPos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "roleName": "", "guides": [{"__id__": 13}], "eventTag": "None"}, {"__type__": "GuideConfig", "name": "点击箱子", "waitPageId": 1, "targetNodePath": "mainUI/pages/商店/ScrollView/view/content/layoutNode/道具升级页buff底1/商店-普通宝箱框/freeBtn", "force": true, "nodeGuide": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]