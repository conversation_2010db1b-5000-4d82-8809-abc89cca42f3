/**
 * 房间评分计算器
 * 根据主题匹配度、家具数量价值、布局美观度计算房间评分
 */

import { Vec2 } from "cc";
import {
    FurnitureTemplate,
    PlacedFurniture,
    FurnitureTheme,
    FurnitureType,
    RoomScoreDetails,
    ScoreWeights
} from "./DecorationDefine";

export class RoomScoreCalculator {
    private scoreWeights: ScoreWeights = {
        themeWeight: 0.4,    // 主题权重40%
        valueWeight: 0.25,   // 价值权重25%
        layoutWeight: 0.25,  // 布局权重25%
        adjacentWeight: 0.1  // 相邻加成权重10%
    };

    /**
     * 计算房间总评分
     */
    calculateRoomScore(
        placedFurnitures: PlacedFurniture[], 
        furnitureTemplates: Map<number, FurnitureTemplate>,
        roomSize: Vec2
    ): RoomScoreDetails {
        // 获取家具模板信息
        const furnitureInfos = placedFurnitures.map(furniture => {
            const template = furnitureTemplates.get(furniture.templateId);
            return { furniture, template };
        }).filter((info): info is { furniture: PlacedFurniture; template: FurnitureTemplate } =>
            info.template !== undefined
        );

        // 计算各项得分
        const themeScore = this.calculateThemeScore(furnitureInfos);
        const quantityScore = this.calculateQuantityScore(furnitureInfos.length);
        const valueScore = this.calculateValueScore(furnitureInfos);
        const layoutScore = this.calculateLayoutScore(furnitureInfos, roomSize);
        const adjacentScore = this.calculateAdjacentBonus(furnitureInfos);

        // 计算总分
        const adjacentWeight = this.scoreWeights.adjacentWeight || 0.1;
        const totalScore =
            themeScore * this.scoreWeights.themeWeight +
            (quantityScore + valueScore) * this.scoreWeights.valueWeight +
            layoutScore * this.scoreWeights.layoutWeight +
            adjacentScore * adjacentWeight;

        // 确定主导主题
        const dominantTheme = this.getDominantTheme(furnitureInfos);

        return {
            themeScore,
            quantityScore,
            valueScore,
            layoutScore,
            adjacentScore,
            totalScore: Math.round(totalScore),
            dominantTheme
        };
    }

    /**
     * 计算主题匹配度得分
     */
    private calculateThemeScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        // 统计各主题的家具数量
        const themeCount = new Map<FurnitureTheme, number>();
        furnitureInfos.forEach(info => {
            const theme = info.template.properties.theme;
            themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
        });

        // 找到最多的主题
        let maxCount = 0;
        let dominantTheme: FurnitureTheme | null = null;
        themeCount.forEach((count, theme) => {
            if (count > maxCount) {
                maxCount = count;
                dominantTheme = theme;
            }
        });

        if (!dominantTheme) return 0;

        // 计算主题一致性得分
        const totalFurniture = furnitureInfos.length;
        const themeConsistency = maxCount / totalFurniture;
        
        // 基础分数 + 一致性奖励
        let themeScore = 50 + (themeConsistency * 50);
        
        // 如果有多个主题但搭配合理，给予额外奖励
        if (themeCount.size > 1) {
            const harmoniousBonus = this.calculateHarmoniousBonus(themeCount, dominantTheme);
            themeScore += harmoniousBonus;
        }

        return Math.min(100, themeScore);
    }

    /**
     * 计算和谐搭配奖励
     */
    private calculateHarmoniousBonus(themeCount: Map<FurnitureTheme, number>, dominantTheme: FurnitureTheme): number {
        // 定义主题搭配关系
        const harmoniousThemes = new Map<FurnitureTheme, FurnitureTheme[]>([
            [FurnitureTheme.Modern, [FurnitureTheme.Minimalist, FurnitureTheme.Industrial]],
            [FurnitureTheme.Classic, [FurnitureTheme.Natural]],
            [FurnitureTheme.Natural, [FurnitureTheme.Classic, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Industrial, [FurnitureTheme.Modern, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Minimalist, [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]]
        ]);

        const compatibleThemes = harmoniousThemes.get(dominantTheme) || [];
        let bonus = 0;

        themeCount.forEach((count, theme) => {
            if (theme !== dominantTheme && compatibleThemes.includes(theme)) {
                bonus += count * 2; // 每个和谐搭配的家具+2分
            } else if (theme !== dominantTheme) {
                bonus -= count * 3; // 每个不和谐的家具-3分
            }
        });

        return Math.max(-20, Math.min(20, bonus));
    }

    /**
     * 计算家具数量得分
     */
    private calculateQuantityScore(furnitureCount: number): number {
        // 数量得分曲线：0-5个家具线性增长，5-10个缓慢增长，10个以上不再增长
        if (furnitureCount <= 5) {
            return furnitureCount * 10; // 每个家具10分，最高50分
        } else if (furnitureCount <= 10) {
            return 50 + (furnitureCount - 5) * 5; // 5个以上每个家具5分，最高75分
        } else {
            return 75; // 10个以上不再增长
        }
    }

    /**
     * 计算家具价值得分
     */
    private calculateValueScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        // 计算总价值和平均等级
        let totalValue = 0;
        let totalLevel = 0;

        furnitureInfos.forEach(info => {
            totalValue += info.template.properties.value;
            totalLevel += info.template.properties.level;
        });

        const averageValue = totalValue / furnitureInfos.length;
        const averageLevel = totalLevel / furnitureInfos.length;

        // 价值得分：基于平均价值和等级
        const valueScore = Math.min(50, averageValue * 2); // 价值得分最高50分
        const levelScore = Math.min(25, averageLevel * 5); // 等级得分最高25分

        return valueScore + levelScore;
    }

    /**
     * 计算布局美观度得分
     */
    private calculateLayoutScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        if (furnitureInfos.length === 0) return 0;

        let layoutScore = 50; // 基础分数

        // 1. 计算分布均匀度
        const distributionScore = this.calculateDistributionScore(furnitureInfos, roomSize);
        layoutScore += distributionScore;

        // 2. 计算对称性
        const symmetryScore = this.calculateSymmetryScore(furnitureInfos, roomSize);
        layoutScore += symmetryScore;

        // 3. 计算空间利用率
        const utilizationScore = this.calculateUtilizationScore(furnitureInfos, roomSize);
        layoutScore += utilizationScore;

        // 4. 计算美观度加成
        const beautyScore = this.calculateBeautyScore(furnitureInfos);
        layoutScore += beautyScore;

        // 5. 计算挂饰类家具奖励
        const wallDecorationScore = this.calculateWallDecorationScore(furnitureInfos);
        layoutScore += wallDecorationScore;

        // 6. 计算相邻加成奖励
        const adjacentScore = this.calculateAdjacentBonus(furnitureInfos);
        layoutScore += adjacentScore;

        return Math.max(0, Math.min(100, layoutScore));
    }

    /**
     * 计算分布均匀度得分
     */
    private calculateDistributionScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        if (furnitureInfos.length <= 1) return 0;

        // 计算家具重心
        const centerX = furnitureInfos.reduce((sum, info) => sum + info.furniture.position.x, 0) / furnitureInfos.length;
        const centerY = furnitureInfos.reduce((sum, info) => sum + info.furniture.position.y, 0) / furnitureInfos.length;

        // 计算与房间中心的偏差
        const roomCenterX = roomSize.x / 2;
        const roomCenterY = roomSize.y / 2;
        const deviation = Math.sqrt(Math.pow(centerX - roomCenterX, 2) + Math.pow(centerY - roomCenterY, 2));
        const maxDeviation = Math.sqrt(Math.pow(roomSize.x / 2, 2) + Math.pow(roomSize.y / 2, 2));

        // 分布得分：偏差越小得分越高
        return Math.max(-10, 10 - (deviation / maxDeviation) * 20);
    }

    /**
     * 计算对称性得分
     */
    private calculateSymmetryScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        // 简化的对称性计算：检查左右对称
        const centerX = roomSize.x / 2;
        let symmetryScore = 0;

        furnitureInfos.forEach(info => {
            const mirrorX = centerX * 2 - info.furniture.position.x;
            const hasMirror = furnitureInfos.some(other => 
                Math.abs(other.furniture.position.x - mirrorX) <= 1 &&
                Math.abs(other.furniture.position.y - info.furniture.position.y) <= 1
            );
            
            if (hasMirror) {
                symmetryScore += 2;
            }
        });

        return Math.min(10, symmetryScore);
    }

    /**
     * 计算空间利用率得分
     */
    private calculateUtilizationScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        const totalRoomArea = roomSize.x * roomSize.y;
        const occupiedArea = furnitureInfos.reduce((sum, info) => 
            sum + info.furniture.currentSize.x * info.furniture.currentSize.y, 0);
        
        const utilization = occupiedArea / totalRoomArea;
        
        // 最佳利用率在20%-60%之间
        if (utilization >= 0.2 && utilization <= 0.6) {
            return 10;
        } else if (utilization < 0.2) {
            return utilization * 50; // 利用率过低扣分
        } else {
            return Math.max(-10, 10 - (utilization - 0.6) * 25); // 利用率过高扣分
        }
    }

    /**
     * 计算美观度得分
     */
    private calculateBeautyScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        const totalBeauty = furnitureInfos.reduce((sum, info) => sum + info.template.properties.beauty, 0);
        const averageBeauty = totalBeauty / furnitureInfos.length;

        return Math.min(15, averageBeauty * 3);
    }

    /**
     * 计算挂饰类家具得分
     */
    private calculateWallDecorationScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        const wallDecorations = furnitureInfos.filter(info =>
            info.template.properties.isWallDecoration
        );

        if (wallDecorations.length === 0) return 0;

        // 每个挂饰类家具+8分
        return Math.min(20, wallDecorations.length * 8);
    }

    /**
     * 获取主导主题
     */
    private getDominantTheme(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): FurnitureTheme | null {
        if (furnitureInfos.length === 0) return null;

        const themeCount = new Map<FurnitureTheme, number>();
        furnitureInfos.forEach(info => {
            const theme = info.template.properties.theme;
            themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
        });

        let maxCount = 0;
        let dominantTheme: FurnitureTheme | null = null;
        themeCount.forEach((count, theme) => {
            if (count > maxCount) {
                maxCount = count;
                dominantTheme = theme;
            }
        });

        return dominantTheme;
    }

    /**
     * 获取主题名称
     */
    getThemeName(theme: FurnitureTheme): string {
        switch (theme) {
            case FurnitureTheme.Modern:
                return "现代风格";
            case FurnitureTheme.Classic:
                return "古典风格";
            case FurnitureTheme.Natural:
                return "自然风格";
            case FurnitureTheme.Industrial:
                return "工业风格";
            case FurnitureTheme.Minimalist:
                return "简约风格";
            default:
                return "未知风格";
        }
    }

    /**
     * 设置评分权重
     */
    setScoreWeights(weights: ScoreWeights) {
        this.scoreWeights = { ...weights };
    }

    /**
     * 获取当前评分权重
     */
    getScoreWeights(): ScoreWeights {
        return { ...this.scoreWeights };
    }

    /**
     * 计算相邻加成奖励
     */
    private calculateAdjacentBonus(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length <= 1) return 0;

        let totalBonus = 0;
        const processedPairs = new Set<string>(); // 避免重复计算同一对家具

        furnitureInfos.forEach((furnitureInfo) => {
            const adjacentFurnitures = this.getAdjacentFurnitures(furnitureInfo, furnitureInfos);

            adjacentFurnitures.forEach(adjacentInfo => {
                // 创建唯一的配对标识符，避免重复计算
                const pairId = this.createPairId(furnitureInfo.furniture.id, adjacentInfo.furniture.id);
                if (processedPairs.has(pairId)) return;
                processedPairs.add(pairId);

                // 计算相邻奖励
                const bonus = this.calculatePairBonus(furnitureInfo, adjacentInfo);
                totalBonus += bonus;
            });
        });

        return Math.min(100, totalBonus); // 相邻加成最高100分，与其他维度保持一致
    }

    /**
     * 获取与指定家具相邻的家具列表
     */
    private getAdjacentFurnitures(
        targetFurniture: {furniture: PlacedFurniture, template: FurnitureTemplate},
        allFurnitures: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>
    ): Array<{furniture: PlacedFurniture, template: FurnitureTemplate}> {
        const adjacent: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}> = [];
        const targetPositions = this.getFurnitureOccupiedPositions(targetFurniture.furniture);

        allFurnitures.forEach(otherFurniture => {
            if (otherFurniture.furniture.id === targetFurniture.furniture.id) return;

            const otherPositions = this.getFurnitureOccupiedPositions(otherFurniture.furniture);

            // 检查是否相邻（任意一个格子相邻即可）
            const isAdjacent = targetPositions.some(targetPos =>
                otherPositions.some(otherPos =>
                    this.arePositionsAdjacent(targetPos, otherPos)
                )
            );

            if (isAdjacent) {
                adjacent.push(otherFurniture);
            }
        });

        return adjacent;
    }

    /**
     * 获取家具占用的所有位置
     */
    private getFurnitureOccupiedPositions(furniture: PlacedFurniture): Vec2[] {
        const positions: Vec2[] = [];
        const { position, currentSize } = furniture;

        for (let x = 0; x < currentSize.x; x++) {
            for (let y = 0; y < currentSize.y; y++) {
                positions.push(new Vec2(position.x + x, position.y + y));
            }
        }

        return positions;
    }

    /**
     * 检查两个位置是否相邻（四方向）
     */
    private arePositionsAdjacent(pos1: Vec2, pos2: Vec2): boolean {
        const dx = Math.abs(pos1.x - pos2.x);
        const dy = Math.abs(pos1.y - pos2.y);

        // 相邻条件：曼哈顿距离为1
        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }

    /**
     * 计算两个相邻家具的奖励分数
     */
    private calculatePairBonus(
        furniture1: {furniture: PlacedFurniture, template: FurnitureTemplate},
        furniture2: {furniture: PlacedFurniture, template: FurnitureTemplate}
    ): number {
        let bonus = 0;

        // 1. 同类型家具相邻奖励
        if (furniture1.template.type === furniture2.template.type) {
            bonus += 4; // 同类型+4分
        }

        // 2. 同主题家具相邻奖励
        if (furniture1.template.properties.theme === furniture2.template.properties.theme) {
            bonus += 3; // 同主题+3分
        }

        // 3. 和谐主题搭配奖励
        if (this.areThemesHarmonious(furniture1.template.properties.theme, furniture2.template.properties.theme)) {
            bonus += 2; // 和谐搭配+2分
        }

        // 4. 特殊组合奖励
        const specialBonus = this.calculateSpecialCombinationBonus(furniture1, furniture2);
        bonus += specialBonus;

        // 5. 尺寸搭配奖励
        const sizeBonus = this.calculateSizeMatchBonus(furniture1, furniture2);
        bonus += sizeBonus;

        return bonus;
    }

    /**
     * 检查两个主题是否和谐
     */
    private areThemesHarmonious(theme1: FurnitureTheme, theme2: FurnitureTheme): boolean {
        if (theme1 === theme2) return false; // 相同主题不算和谐搭配

        const harmoniousThemes = new Map<FurnitureTheme, FurnitureTheme[]>([
            [FurnitureTheme.Modern, [FurnitureTheme.Minimalist, FurnitureTheme.Industrial]],
            [FurnitureTheme.Classic, [FurnitureTheme.Natural]],
            [FurnitureTheme.Natural, [FurnitureTheme.Classic, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Industrial, [FurnitureTheme.Modern, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Minimalist, [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]]
        ]);

        const compatibleThemes = harmoniousThemes.get(theme1) || [];
        return compatibleThemes.includes(theme2);
    }

    /**
     * 计算特殊组合奖励
     */
    private calculateSpecialCombinationBonus(
        furniture1: {furniture: PlacedFurniture, template: FurnitureTemplate},
        furniture2: {furniture: PlacedFurniture, template: FurnitureTemplate}
    ): number {
        // 定义特殊组合规则
        const specialCombinations = [
            // 桌椅组合
            { types: [FurnitureType.Medium, FurnitureType.Small], bonus: 3, description: "桌椅组合" },
            // 沙发茶几组合
            { types: [FurnitureType.Large, FurnitureType.Medium], bonus: 2, description: "沙发茶几组合" },
            // 挂饰与家具组合
            { types: [FurnitureType.WallDecoration, FurnitureType.Small], bonus: 2, description: "挂饰装点" },
            { types: [FurnitureType.WallDecoration, FurnitureType.Medium], bonus: 2, description: "挂饰装点" },
            { types: [FurnitureType.WallDecoration, FurnitureType.Large], bonus: 1, description: "挂饰装点" }
        ];

        for (const combination of specialCombinations) {
            if ((combination.types.includes(furniture1.template.type) &&
                 combination.types.includes(furniture2.template.type)) &&
                furniture1.template.type !== furniture2.template.type) {
                return combination.bonus;
            }
        }

        return 0;
    }

    /**
     * 计算尺寸搭配奖励
     */
    private calculateSizeMatchBonus(
        furniture1: {furniture: PlacedFurniture, template: FurnitureTemplate},
        furniture2: {furniture: PlacedFurniture, template: FurnitureTemplate}
    ): number {
        const size1 = furniture1.furniture.currentSize.x * furniture1.furniture.currentSize.y;
        const size2 = furniture2.furniture.currentSize.x * furniture2.furniture.currentSize.y;

        // 大小家具搭配奖励
        if (Math.abs(size1 - size2) >= 2) {
            return 1; // 大小搭配+1分
        }

        return 0;
    }

    /**
     * 创建家具配对的唯一标识符
     */
    private createPairId(id1: string, id2: string): string {
        // 确保ID顺序一致，避免重复
        return id1 < id2 ? `${id1}_${id2}` : `${id2}_${id1}`;
    }
}
