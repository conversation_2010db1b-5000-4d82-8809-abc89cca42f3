# 装饰系统集成文档

## 概述

本文档描述了从HTML演示集成到Cocos Creator项目中的装饰系统功能。集成后的系统包含了家具摆放验证、自动修正、随机变化、评分计算等完整功能。

## 新增组件

### 1. FurniturePlacementValidator (家具摆放验证器)

**功能：**
- 验证家具摆放位置的合法性
- 检查障碍物、边界、家具冲突
- 挂饰类家具的贴墙验证
- 智能自动修正功能

**主要方法：**
```typescript
// 验证摆放位置
validatePlacement(template: FurnitureTemplate, position: Vec2, rotation: Rotation): ValidationResult

// 自动修正位置
autoCorrectPosition(template: FurnitureTemplate, originalPosition: Vec2, rotation: Rotation): AutoCorrectionResult
```

### 2. FurnitureRandomizer (家具随机变化器)

**功能：**
- 随机变化家具位置和角度
- 智能搜索合适的摆放位置
- 支持批量随机变化
- 可配置的搜索参数

**主要方法：**
```typescript
// 随机变化单个家具
randomizeFurniture(targetFurniture: PlacedFurniture, template: FurnitureTemplate, allFurnitures: PlacedFurniture[]): RandomizeResult

// 批量随机变化
randomizeMultipleFurnitures(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>): RandomizeResult[]
```

### 3. DecorationDemoManager (装饰演示管理器)

**功能：**
- 整合所有装饰功能
- 管理演示步骤和状态
- 提供完整的API接口
- 事件回调机制

**主要方法：**
```typescript
// 步骤控制
nextStep(): void
prevStep(): void
resetDemo(): void

// 家具操作
validateFurniturePlacement(templateId: number, position: Vec2, rotation: Rotation): ValidationResult
randomizeFurniture(furnitureId: string): RandomizeResult

// 评分计算
calculateCurrentScore(): RoomScoreDetails
```

### 4. DecorationDemoUI (装饰演示UI)

**功能：**
- 提供用户界面
- 显示评分和状态信息
- 控制按钮和交互
- 实时反馈

## 扩展的现有组件

### FurnitureManager

**新增方法：**
```typescript
// 高级位置验证
validatePlacementAdvanced(templateId: number, position: Vec2, rotation: Rotation): ValidationResult

// 随机变化家具
randomizeFurniture(furnitureId: string): RandomizeResult

// 应用随机变化结果
applyRandomizeResult(result: RandomizeResult): boolean

// 检查贴墙
isAdjacentToWall(position: Vec2): boolean
```

### RoomScoreCalculator

**新增功能：**
- 挂饰类家具的特殊评分
- 贴墙摆放奖励机制

### DecorationDefine

**新增定义：**
```typescript
// 挂饰类家具类型
enum FurnitureType {
    WallDecoration = 4  // 挂饰类家具（必须贴墙）
}

// 家具属性扩展
interface FurnitureProperties {
    isWallDecoration?: boolean;  // 是否为挂饰类家具
}
```

## 使用方法

### 1. 基本设置

```typescript
// 在场景中添加DecorationDemoManager组件
const demoManager = this.node.addComponent(DecorationDemoManager);

// 设置房间尺寸
demoManager.roomSize = new Vec2(12, 8);

// 设置回调
demoManager.setOnStepChanged((step, stepData) => {
    console.log(`切换到步骤 ${step}: ${stepData.title}`);
});

demoManager.setOnScoreUpdated((score) => {
    console.log(`当前评分: ${score.totalScore}`);
});
```

### 2. 家具摆放验证

```typescript
// 验证家具摆放
const validation = demoManager.validateFurniturePlacement(
    1,                    // 家具模板ID
    new Vec2(5, 3),      // 位置
    Rotation.Deg0        // 旋转角度
);

if (validation.isValid) {
    console.log("可以摆放");
} else {
    console.log("无法摆放:", validation.reason);
}
```

### 3. 随机变化功能

```typescript
// 随机变化指定家具
const result = demoManager.randomizeFurniture("furniture_id");

if (result && result.success) {
    console.log("随机变化成功");
    console.log("变化类型:", result.changeType);
    console.log("尝试次数:", result.attempts);
} else {
    console.log("随机变化失败:", result?.reason);
}
```

### 4. 评分计算

```typescript
// 计算当前房间评分
const score = demoManager.calculateCurrentScore();

console.log("总评分:", score.totalScore);
console.log("主题评分:", score.themeScore);
console.log("数量评分:", score.quantityScore);
console.log("价值评分:", score.valueScore);
console.log("布局评分:", score.layoutScore);
console.log("主导主题:", score.dominantTheme);
```

### 5. 演示控制

```typescript
// 步骤控制
demoManager.nextStep();     // 下一步
demoManager.prevStep();     // 上一步
demoManager.resetDemo();    // 重置

// 获取当前状态
const currentStep = demoManager.getCurrentStep();
const currentState = demoManager.getCurrentState();
const furnitures = demoManager.getCurrentStepFurnitures();
```

## 配置选项

### 随机变化配置

```typescript
const randomizeConfig = {
    maxAttempts: 100,           // 最大尝试次数
    rotationProbability: 0.5,   // 旋转概率
    searchRadius: 5,            // 搜索半径
    allowRoomChange: false      // 是否允许跨房间
};

// 应用配置
furnitureRandomizer.setDefaultConfig(randomizeConfig);
```

### 评分权重配置

```typescript
const scoreWeights = {
    theme: 0.3,      // 主题权重
    quantity: 0.25,  // 数量权重
    value: 0.25,     // 价值权重
    layout: 0.2      // 布局权重
};

scoreCalculator.setWeights(scoreWeights);
```

## 事件系统

### 可用事件

```typescript
// 步骤变化事件
setOnStepChanged(callback: (step: number, stepData: DemoStep) => void)

// 评分更新事件
setOnScoreUpdated(callback: (score: RoomScoreDetails) => void)

// 状态变化事件
setOnStateChanged(callback: (state: DemoState) => void)

// 家具摆放事件
setOnFurniturePlaced(callback: (furniture: PlacedFurniture) => void)
```

## 注意事项

1. **挂饰类家具**：必须设置 `isWallDecoration: true` 并确保贴墙摆放
2. **性能考虑**：随机变化功能会进行多次验证，建议合理设置最大尝试次数
3. **状态管理**：在进行操作前检查当前状态，避免冲突
4. **错误处理**：所有方法都包含错误处理，建议检查返回结果

## 保存/加载功能

### 新增组件

#### 1. RoomLayoutStorage (房间布局存储管理器)

**功能：**
- 房间布局的持久化存储
- 支持多种布局分类（用户保存、模板、自动保存、分享）
- 强大的搜索和过滤功能
- 导入/导出功能
- 存储空间管理和清理

**主要方法：**
```typescript
// 保存布局
saveLayout(furnitures: PlacedFurniture[], roomSize: Vec2, layoutName: string): Promise<string>

// 加载布局
loadLayout(layoutId: string): Promise<SavedRoomLayout | null>

// 搜索布局
searchLayouts(filter?: LayoutFilter, sort?: SortOption): Promise<SavedRoomLayout[]>

// 导入/导出
exportLayout(layoutId: string): Promise<string | null>
importLayout(jsonData: string, newName?: string): Promise<string | null>
```

#### 2. RoomLayoutManager (房间布局管理器)

**功能：**
- 整合保存/加载功能与装饰系统
- 自动保存功能
- 布局操作的统一接口
- 事件回调机制

**主要方法：**
```typescript
// 快速保存
quickSave(name?: string, options?: QuickSaveOptions): Promise<LayoutOperationResult>

// 加载布局
loadLayout(layoutId: string): Promise<LayoutOperationResult>

// 获取布局列表
getRecentLayouts(limit?: number): Promise<SavedRoomLayout[]>
getTopScoredLayouts(limit?: number): Promise<SavedRoomLayout[]>
getLayoutsByTheme(theme: FurnitureTheme): Promise<SavedRoomLayout[]>
```

#### 3. RoomLayoutUI (布局管理UI)

**功能：**
- 保存/加载的用户界面
- 布局列表显示和管理
- 搜索和过滤界面
- 导入/导出操作

### 使用方法

#### 1. 基本保存/加载

```typescript
// 快速保存当前布局
const saveResult = await demoManager.quickSaveCurrentLayout("我的布局");
if (saveResult.success) {
    console.log("保存成功:", saveResult.layoutId);
}

// 加载指定布局
const loadResult = await demoManager.loadLayout(layoutId);
if (loadResult.success) {
    console.log("加载成功");
}
```

#### 2. 高级保存选项

```typescript
// 带选项的保存
const result = await layoutManager.quickSave("高级布局", {
    autoGenerateName: false,
    includeScore: true,
    includeThumbnail: true,
    tags: ["现代", "简约", "高分"]
});
```

#### 3. 搜索和过滤

```typescript
// 按主题搜索
const modernLayouts = await layoutManager.getLayoutsByTheme(FurnitureTheme.Modern);

// 获取高分布局
const topLayouts = await layoutManager.getTopScoredLayouts(10);

// 复杂搜索
const layouts = await layoutManager.searchLayouts({
    theme: FurnitureTheme.Modern,
    minScore: 80,
    tags: ["简约"],
    nameKeyword: "客厅"
}, SortOption.ScoreDesc, 20);
```

#### 4. 导入/导出

```typescript
// 导出布局
const jsonData = await layoutManager.exportLayout(layoutId);
if (jsonData) {
    // 保存到文件或复制到剪贴板
    console.log("导出数据:", jsonData);
}

// 导入布局
const importResult = await layoutManager.importLayout(jsonData, "导入的布局");
```

#### 5. 自动保存

```typescript
// 启用自动保存（在RoomLayoutManager组件中设置）
layoutManager.enableAutoSave = true;
layoutManager.autoSaveInterval = 300; // 5分钟间隔
```

### 数据结构

#### SavedRoomLayout
```typescript
interface SavedRoomLayout {
    id: string;                    // 布局唯一ID
    name: string;                  // 布局名称
    description?: string;          // 布局描述
    roomSize: { x: number; y: number }; // 房间尺寸
    furnitures: SavedFurniture[];  // 家具列表
    score?: number;                // 评分
    dominantTheme?: FurnitureTheme; // 主导主题
    createdTime: number;           // 创建时间
    modifiedTime: number;          // 修改时间
    tags?: string[];               // 标签
    thumbnail?: string;            // 缩略图
}
```

#### 布局分类
- **UserSaved**: 用户手动保存的布局
- **Template**: 系统模板布局
- **AutoSave**: 自动保存的布局
- **Shared**: 分享的布局

### 存储管理

#### 存储限制
- 用户保存布局：最多100个
- 自动保存布局：最多10个
- 总存储空间监控

#### 清理功能
```typescript
// 清理存储空间
const removedCount = await layoutManager.cleanupStorage({
    removeAutoSaves: true,        // 删除自动保存
    removeOldLayouts: 30,         // 删除30天前的布局
    maxLayouts: 50                // 最多保留50个布局
});
```

### 事件系统

```typescript
// 设置保存/加载事件回调
demoManager.setOnLayoutSaved((result) => {
    console.log("布局保存:", result.success ? "成功" : result.message);
});

demoManager.setOnLayoutLoaded((result) => {
    console.log("布局加载:", result.success ? "成功" : result.message);
});
```

### 测试功能

使用 `LayoutStorageTest` 组件进行完整的功能测试：

```typescript
// 运行完整测试
const testComponent = this.node.getComponent(LayoutStorageTest);
await testComponent.runFullTest();

// 清理测试数据
await testComponent.cleanupTestData();
```

## 扩展建议

1. **添加更多家具类型**：在 `createFurnitureTemplates()` 中添加新模板
2. **自定义评分规则**：修改 `RoomScoreCalculator` 中的评分算法
3. **增加动画效果**：在UI组件中添加家具摆放动画
4. **缩略图生成**：实现房间布局的可视化缩略图
5. **云端同步**：扩展为支持云端存储和多设备同步
6. **分享功能**：实现布局分享和社区功能
7. **多房间支持**：扩展为支持多个房间的复杂布局

## 调试工具

系统提供了丰富的调试信息：

```typescript
// 启用详细日志
console.log("验证结果:", validation);
console.log("随机变化结果:", randomizeResult);
console.log("评分详情:", scoreDetails);

// 验证测试
demoManager.validateFurniturePlacement(templateId, position, rotation);
```

通过这些工具可以快速定位问题和优化性能。
