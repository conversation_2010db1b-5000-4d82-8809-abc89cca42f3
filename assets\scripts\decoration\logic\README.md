# 房间摆放系统 - 核心逻辑 API 文档

## 概述

本文档详细描述了房间摆放系统的核心逻辑层 API。该系统提供了完整的家具摆放、验证、评分和管理功能，支持单房间和多房间模式。

## 核心组件架构

### 1. DecorationMgr (装饰管理器) - 主入口
**职责：** 系统主入口，协调各个子系统
```typescript
class DecorationMgr {
    // 系统初始化
    static getInstance(): DecorationMgr
    async init(): Promise<void>

    // 家具操作
    placeFurniture(templateId: number, position: Vec2, rotation: Rotation): PlacementResult
    removeFurniture(furnitureId: string): boolean
    moveFurniture(furnitureId: string, newPosition: Vec2): PlacementResult
    rotateFurniture(furnitureId: string, newRotation: Rotation): PlacementResult

    // 数据获取
    getAllFurnitureTemplates(): FurnitureTemplate[]
    getFurnitureTemplate(templateId: number): FurnitureTemplate | null
    getPlacedFurnitures(): PlacedFurniture[]
    getRoomSize(): Vec2

    // 验证功能
    validateFurniturePlacement(templateId: number, position: Vec2, rotation: Rotation): ValidationResult

    // 多房间支持
    enableMultiRoomMode(roomConfigs: RoomTemplate[]): boolean
    switchToRoom(roomId: string): boolean
    getCurrentRoomId(): string
}
```

### 2. FurnitureManager (家具管理器)
**职责：** 管理家具模板和已放置的家具实例
```typescript
class FurnitureManager {
    static getInstance(): FurnitureManager

    // 模板管理
    loadTemplatesFromJson(jsonPath: string): Promise<void>
    addTemplate(template: FurnitureTemplate): void
    getTemplate(templateId: number): FurnitureTemplate | null
    getAllTemplates(): FurnitureTemplate[]

    // 家具实例管理
    createFurniture(templateId: number, position: Vec2, rotation: Rotation): PlacedFurniture | null
    addFurniture(furniture: PlacedFurniture): boolean
    removeFurniture(furnitureId: string): boolean
    getFurniture(furnitureId: string): PlacedFurniture | null
    getAllFurnitures(): PlacedFurniture[]
    clearAllFurnitures(): void

    // 多房间支持
    setupRooms(options: RoomSetupOptions): RoomSetupResult
    isMultiRoomAvailable(): boolean
    enableMultiRoomMode(roomTemplateIds?: string[]): boolean
    getMultiRoomManager(): MultiRoomManager | null
}
```

### 3. RoomGrid (房间格子管理器)
**职责：** 管理房间格子状态和占用情况
```typescript
class RoomGrid {
    constructor(size: Vec2, initialLayout?: number[][])

    // 格子状态管理
    getGridState(position: Vec2): GridState
    setGridState(position: Vec2, state: GridState): void
    isPositionValid(position: Vec2): boolean
    isPositionEmpty(position: Vec2): boolean
    isPositionOccupied(position: Vec2): boolean

    // 家具占用管理
    placeFurniture(furniture: PlacedFurniture, isWallDecoration?: boolean): boolean
    removeFurniture(furnitureId: string): boolean
    getFurnitureAt(position: Vec2): PlacedFurniture | null

    // 区域检查
    isAreaEmpty(position: Vec2, size: Vec2): boolean
    getOccupiedPositions(position: Vec2, size: Vec2): Vec2[]
    canPlaceWallDecoration(position: Vec2): boolean

    // 工具方法
    getSize(): Vec2
    getEmptyPositions(): Vec2[]
    getOccupiedPositions(): Vec2[]
    clone(): RoomGrid
}
```

### 4. FurniturePlacementValidator (摆放验证器)
**职责：** 验证家具摆放的合法性
```typescript
class FurniturePlacementValidator {
    constructor(roomGrid: RoomGrid, placedFurnitures: PlacedFurniture[])

    // 核心验证方法
    validatePlacement(template: FurnitureTemplate, position: Vec2, rotation: Rotation, excludeFurnitureId?: string): ValidationResult

    // 自动修正功能
    autoCorrectPosition(template: FurnitureTemplate, originalPosition: Vec2, rotation: Rotation): AutoCorrectionResult

    // 辅助验证方法
    checkBoundary(position: Vec2, size: Vec2): boolean
    checkObstacles(position: Vec2, size: Vec2): boolean
    checkFurnitureConflict(position: Vec2, size: Vec2, excludeId?: string): boolean
    checkWallAdjacency(position: Vec2): boolean

    // 搜索功能
    findNearestValidPosition(template: FurnitureTemplate, preferredPosition: Vec2, rotation: Rotation, maxRadius?: number): Vec2 | null
}
```

### 5. RoomScoreCalculator (评分计算器)
**职责：** 计算房间装饰评分
```typescript
class RoomScoreCalculator {
    // 评分计算
    calculateScore(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>, roomSize: Vec2): RoomScoreDetails

    // 分项评分
    calculateThemeScore(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>): ThemeScoreResult
    calculateQuantityScore(furnitures: PlacedFurniture[]): number
    calculateValueScore(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>): number
    calculateLayoutScore(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>, roomSize: Vec2): number
    calculateAdjacentBonus(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>): number

    // 配置管理
    setScoreWeights(weights: ScoreWeights): void
    getScoreWeights(): ScoreWeights

    // 工具方法
    getDominantTheme(furnitures: PlacedFurniture[], templates: Map<number, FurnitureTemplate>): FurnitureTheme
    getThemeName(theme: FurnitureTheme): string
}
```



### 6. MultiRoomManager (多房间管理器)
**职责：** 管理多房间模式的房间切换和操作
```typescript
class MultiRoomManager {
    // 初始化
    init(config: MultiRoomConfig): void

    // 房间管理
    addRoom(roomConfig: SingleRoomConfig): boolean
    removeRoom(roomId: string): boolean
    switchToRoom(roomId: string): boolean
    getCurrentRoomId(): string
    getRoomConfig(roomId: string): SingleRoomConfig | null
    getAllRoomIds(): string[]

    // 家具操作
    placeFurniture(templateId: number, position: Vec2, rotation: Rotation, roomId?: string): PlacementResult
    removeFurniture(furnitureId: string): boolean
    moveFurniture(furnitureId: string, newPosition: Vec2, targetRoomId?: string): PlacementResult

    // 评分计算
    calculateTotalScore(): RoomScoreDetails
    calculateRoomScore(roomId: string): RoomScoreDetails

    // 数据获取
    getRoomFurnitures(roomId: string): PlacedFurniture[]
    getAllFurnitures(): Map<string, PlacedFurniture[]>
}
```

### 7. DefaultRoomTemplates (默认房间模板)
**职责：** 提供预定义的房间模板
```typescript
class DefaultRoomTemplates {
    // 模板获取
    static getAllTemplates(): RoomTemplate[]
    static getTemplate(templateId: string): RoomTemplate | null
    static getRecommendedTemplates(): RoomTemplate[]

    // 模板验证和修复
    static validateTemplate(template: RoomTemplate): boolean
    static fixRoomTemplates(templates: RoomTemplate[]): RoomTemplate[]

    // 预定义模板
    static ROOM_A: RoomTemplate    // 10x10 房间A
    static ROOM_B: RoomTemplate    // 6x6 房间B
    static LARGE_ROOM: RoomTemplate // 12x12 大房间
    static SMALL_ROOM: RoomTemplate // 4x4 小房间
}
```

## 核心数据结构

### 基础类型定义
```typescript
// 家具模板
interface FurnitureTemplate {
    id: number
    name: string
    type: FurnitureType
    baseSize: Vec2
    spriteFrame: string
    description: string
    properties: FurnitureProperties
}

// 家具属性
interface FurnitureProperties {
    theme: FurnitureTheme
    level: number
    value: number
    beauty: number
    isWallDecoration?: boolean
}

// 已放置的家具
interface PlacedFurniture {
    id: string
    templateId: number
    position: Vec2
    rotation: Rotation
    currentSize: Vec2
    placedTime: number
    roomId?: string
}
```

### 验证和结果类型
```typescript
// 验证结果
interface ValidationResult {
    isValid: boolean
    reason?: string
    conflictPositions?: Vec2[]
    suggestedPosition?: Vec2
}

// 摆放结果
interface PlacementResult {
    success: boolean
    errorMessage?: string
    conflictPositions?: Vec2[]
}

// 自动修正结果
interface AutoCorrectionResult {
    success: boolean
    correctedPosition?: Vec2
    originalPosition: Vec2
    reason?: string
}

// 随机变化结果
interface RandomizeResult {
    success: boolean
    changeType?: 'position' | 'rotation' | 'both'
    oldPosition?: Vec2
    newPosition?: Vec2
    oldRotation?: Rotation
    newRotation?: Rotation
    attempts: number
    reason?: string
}
```

### 评分系统类型
```typescript
// 房间评分详情
interface RoomScoreDetails {
    totalScore: number
    themeScore: number
    quantityScore: number
    valueScore: number
    layoutScore: number
    adjacentBonus: number
    dominantTheme: FurnitureTheme
    furnitureCount: number
    themeDistribution: Map<FurnitureTheme, number>
}

// 评分权重
interface ScoreWeights {
    theme: number      // 主题权重 (默认: 0.4)
    quantity: number   // 数量权重 (默认: 0.2)
    value: number      // 价值权重 (默认: 0.25)
    layout: number     // 布局权重 (默认: 0.25)
    adjacent: number   // 相邻加成权重 (默认: 0.1)
}
```

### 多房间系统类型
```typescript
// 多房间配置
interface MultiRoomConfig {
    rooms: Map<string, SingleRoomConfig>
    sharedScore: boolean
}

// 单房间配置
interface SingleRoomConfig {
    id: string
    name: string
    size: Vec2
    layout: number[][]
    placedFurnitures: PlacedFurniture[]
    lastModified: number
}

// 房间模板
interface RoomTemplate {
    id: string
    name: string
    size: Vec2
    layout: number[][]
    description?: string
}
```

## 使用示例

### 1. 系统初始化
```typescript
// 初始化装饰管理器
const decorationMgr = DecorationMgr.getInstance();
await decorationMgr.init();

// 获取家具管理器
const furnitureManager = FurnitureManager.getInstance();

// 加载家具模板
await furnitureManager.loadTemplatesFromJson("resources/json/furnitureTemplates.json");
```

### 2. 基础家具操作
```typescript
// 放置家具
const placeResult = decorationMgr.placeFurniture(1, new Vec2(3, 3), Rotation.Deg0);
if (placeResult.success) {
    console.log("家具放置成功");
} else {
    console.log("放置失败:", placeResult.errorMessage);
}

// 移动家具
const moveResult = decorationMgr.moveFurniture("furniture_id", new Vec2(5, 5));

// 旋转家具
const rotateResult = decorationMgr.rotateFurniture("furniture_id", Rotation.Deg90);

// 移除家具
const removed = decorationMgr.removeFurniture("furniture_id");
```

### 3. 验证功能
```typescript
// 验证摆放位置
const validation = decorationMgr.validateFurniturePlacement(1, new Vec2(5, 3), Rotation.Deg0);
if (validation.isValid) {
    console.log("可以摆放");
} else {
    console.log("无法摆放:", validation.reason);
    if (validation.suggestedPosition) {
        console.log("建议位置:", validation.suggestedPosition);
    }
}
```

### 4. 评分计算
```typescript
// 获取评分计算器
const scoreCalculator = new RoomScoreCalculator();

// 计算当前房间评分
const furnitures = decorationMgr.getPlacedFurnitures();
const templates = new Map(decorationMgr.getAllFurnitureTemplates().map(t => [t.id, t]));
const roomSize = decorationMgr.getRoomSize();

const scoreDetails = scoreCalculator.calculateScore(furnitures, templates, roomSize);
console.log("总评分:", scoreDetails.totalScore);
console.log("主题评分:", scoreDetails.themeScore);
console.log("主导主题:", scoreDetails.dominantTheme);
```

### 5. 多房间模式
```typescript
// 检查多房间可用性
if (furnitureManager.isMultiRoomAvailable()) {
    // 启用多房间模式
    const success = furnitureManager.enableMultiRoomMode(["room_a", "room_b"]);

    if (success) {
        const multiRoomManager = furnitureManager.getMultiRoomManager();

        // 切换房间
        multiRoomManager.switchToRoom("room_a");

        // 在指定房间放置家具
        const result = multiRoomManager.placeFurniture(1, new Vec2(2, 2), Rotation.Deg0, "room_a");

        // 计算总评分
        const totalScore = multiRoomManager.calculateTotalScore();
        console.log("多房间总评分:", totalScore.totalScore);
    }
}
```



### 6. 自动修正功能
```typescript
// 创建验证器
const validator = new FurniturePlacementValidator(roomGrid, furnitures);

// 尝试自动修正位置
const template = templates.get(1);
const correctionResult = validator.autoCorrectPosition(template, new Vec2(15, 15), Rotation.Deg0);

if (correctionResult.success) {
    console.log("自动修正成功");
    console.log("修正后位置:", correctionResult.correctedPosition);
} else {
    console.log("无法自动修正:", correctionResult.reason);
}
```

## 配置选项



### 评分权重配置
```typescript
const scoreWeights: ScoreWeights = {
    theme: 0.4,      // 主题权重
    quantity: 0.2,   // 数量权重
    value: 0.25,     // 价值权重
    layout: 0.25,    // 布局权重
    adjacent: 0.1    // 相邻加成权重
};

scoreCalculator.setScoreWeights(scoreWeights);
```

### 房间设置配置
```typescript
const roomSetupOptions: RoomSetupOptions = {
    mode: 'dual',                    // 'single' | 'dual' | 'multi'
    roomTemplateIds: ['room_a', 'room_b'],
    useRecommended: true,
    customRoomConfigs: [
        {
            templateId: 'room_a',
            position: new Vec2(0, 0)
        },
        {
            templateId: 'room_b',
            position: new Vec2(15, 0)
        }
    ]
};
```

## 最佳实践

### 1. 家具摆放验证
- 始终在放置家具前进行验证，避免无效摆放
- 利用 `autoCorrectPosition` 提供更好的用户体验
- 对于挂饰类家具，确保使用 `canPlaceWallDecoration` 进行特殊验证

```typescript
// 验证并自动修正
const validation = validator.validatePlacement(template, position, rotation);
if (!validation.isValid && validation.suggestedPosition) {
    position = validation.suggestedPosition;
    // 重新验证修正后的位置
    const revalidation = validator.validatePlacement(template, position, rotation);
    if (revalidation.isValid) {
        // 使用修正后的位置
    }
}
```

### 2. 性能优化
- 对于大量家具的场景，避免频繁调用 `calculateScore`
- 随机变化功能可能需要多次尝试，合理设置 `maxAttempts` 避免性能问题
- 使用 `RoomGrid.clone()` 进行临时验证，避免修改原始数据

```typescript
// 性能优化示例
const tempGrid = roomGrid.clone();
const isValid = tempGrid.isAreaEmpty(position, size);
if (isValid) {
    // 在真实网格上执行操作
    roomGrid.placeFurniture(furniture);
}
```

### 3. 多房间管理
- 使用 `roomId` 参数明确指定操作的目标房间
- 避免跨房间移动家具时的冲突
- 共享评分系统时注意平衡各房间的装饰

```typescript
// 多房间操作示例
const multiRoomManager = furnitureManager.getMultiRoomManager();
if (multiRoomManager) {
    // 在特定房间操作
    multiRoomManager.placeFurniture(templateId, position, rotation, "room_a");

    // 获取特定房间的家具
    const roomAFurnitures = multiRoomManager.getRoomFurnitures("room_a");
}
```

### 4. 错误处理
- 所有操作方法都返回结果对象，始终检查 `success` 字段
- 利用返回的错误信息提供用户反馈
- 对于异步操作，使用 try-catch 处理可能的异常

```typescript
// 错误处理示例
try {
    const result = decorationMgr.placeFurniture(templateId, position, rotation);
    if (!result.success) {
        console.error("放置失败:", result.errorMessage);
        // 向用户显示错误信息
    }
} catch (error) {
    console.error("操作异常:", error);
}
```

### 5. 数据一致性
- 使用 `RoomGrid` 和 `FurnitureManager` 保持数据一致性
- 避免直接修改 `PlacedFurniture` 对象，使用提供的方法
- 操作完成后更新相关UI和评分

## 注意事项

1. **挂饰类家具**：必须设置 `properties.isWallDecoration = true` 并确保贴墙摆放
2. **旋转处理**：旋转会改变家具的 `currentSize`，确保正确处理尺寸变化
3. **格子状态**：理解不同的 `GridState` 枚举值及其用途
   - `Empty = 0`: 空地，可以摆放普通家具
   - `WallAdjacent = 1`: 邻墙格子，挂饰类家具只能放在这里
   - `Obstacle = 2`: 障碍物，不可摆放任何家具
   - `Occupied = 3`: 已被家具占用
4. **ID生成**：家具ID应该是唯一的，推荐使用时间戳+随机字符串
5. **异步操作**：某些操作可能是异步的，特别是涉及资源加载时

## 枚举定义

### FurnitureType (家具类型)
```typescript
enum FurnitureType {
    Small = 1,           // 1×1 小型家具
    Medium = 2,          // 2×1 中型家具
    Large = 3,           // 2×2 大型家具
    WallDecoration = 4   // 挂饰类家具（必须贴墙）
}
```

### FurnitureTheme (家具主题)
```typescript
enum FurnitureTheme {
    Modern = 1,      // 现代风格
    Classic = 2,     // 古典风格
    Natural = 3,     // 自然风格
    Industrial = 4,  // 工业风格
    Minimalist = 5   // 简约风格
}
```

### Rotation (旋转角度)
```typescript
enum Rotation {
    Deg0 = 0,
    Deg90 = 90,
    Deg180 = 180,
    Deg270 = 270
}
```

### GridState (格子状态)
```typescript
enum GridState {
    Empty = 0,          // 空地（可摆放）
    WallAdjacent = 1,   // 邻接障碍或墙壁的格子（可摆放，挂饰类家具只能摆放在这里）
    Obstacle = 2,       // 障碍（不可摆放）
    Occupied = 3        // 已占用（被家具占用）
}
```

## 总结

房间摆放系统的核心逻辑层提供了完整的家具管理功能：

- **DecorationMgr**: 系统主入口，提供统一的API接口
- **FurnitureManager**: 管理家具模板和实例
- **RoomGrid**: 处理房间格子状态和占用管理
- **FurniturePlacementValidator**: 验证摆放合法性和自动修正
- **RoomScoreCalculator**: 计算房间装饰评分
- **MultiRoomManager**: 支持多房间模式
- **DefaultRoomTemplates**: 提供预定义房间模板

这些组件协同工作，为上层UI提供了稳定可靠的业务逻辑支持。通过合理使用这些API，可以构建出功能丰富的房间装饰系统。
